# MDAC Chrome扩展贡献者指南

## 📖 文档概述

**版本**: 1.0  
**更新日期**: 2025-07-15  
**适用版本**: MDAC AI智能填充工具 v2.0.0  
**目标读者**: 开源贡献者、开发团队成员、代码审查员

---

## 🎯 贡献指南概述

欢迎参与MDAC Chrome扩展项目的开发！本指南提供了参与项目开发所需的完整信息，包括代码规范、提交流程、测试要求等。我们致力于构建一个开放、包容、高质量的开发社区。

### 🌟 我们的价值观
- **🔓 开放透明** - 开源协作，公开透明的开发流程
- **🤝 包容友善** - 欢迎所有水平的贡献者参与
- **📈 持续改进** - 通过代码审查和反馈不断提升质量
- **🛡️ 用户优先** - 始终以用户体验和数据安全为首要考虑

---

## 🚀 快速开始

### 准备工作

#### 环境要求
```bash
# 必需工具
- Git >= 2.0
- Node.js >= 16.0 (如果使用构建工具)
- Chrome浏览器 >= 114
- 代码编辑器 (推荐 VS Code)

# 推荐工具
- Chrome Extension Developer Tools
- Prettier (代码格式化)
- ESLint (代码检查)
```

#### 获取代码
```bash
# 1. Fork项目到你的GitHub账户
# 2. 克隆你的Fork
git clone https://github.com/your-username/mdac-extension.git
cd mdac-extension

# 3. 添加上游仓库
git remote add upstream https://github.com/original-org/mdac-extension.git

# 4. 安装依赖 (如果有)
npm install  # 可选

# 5. 验证开发环境
./scripts/verify-setup.sh  # 如果提供了验证脚本
```

### 首次贡献指导

#### 适合新手的任务
```markdown
🔰 新手友好任务:
- 📝 文档改进和翻译
- 🐛 简单bug修复
- 🎨 UI/UX优化
- 🧪 添加测试用例
- 📊 性能优化

🏷️ 查找Issues:
- 标签: "good first issue"
- 标签: "help wanted"
- 标签: "documentation"
```

#### 贡献流程概览
```mermaid
graph TD
    A[选择Issue] --> B[创建分支]
    B --> C[编写代码]
    C --> D[本地测试]
    D --> E[提交变更]
    E --> F[创建PR]
    F --> G[代码审查]
    G --> H{审查结果}
    H -->|通过| I[合并]
    H -->|需修改| C
    I --> J[删除分支]
```

---

## 📋 代码规范

### JavaScript编码标准

#### 基础规范
```javascript
// ✅ 推荐的代码风格

// 1. 使用const和let，避免var
const API_BASE_URL = 'https://api.example.com';
let currentUser = null;

// 2. 函数命名：动词+名词，驼峰式
function getUserData() { }
function updateFormField() { }

// 3. 类命名：首字母大写，驼峰式
class StateManager {
    constructor() {
        this.initialized = false;
    }
}

// 4. 常量命名：全大写+下划线
const MAX_RETRY_COUNT = 3;
const DEFAULT_TIMEOUT = 5000;

// 5. 私有方法：下划线前缀
class MyClass {
    _privateMethod() {
        // 私有方法实现
    }
    
    publicMethod() {
        this._privateMethod();
    }
}
```

#### 注释标准
```javascript
/**
 * 解析用户输入的个人信息
 * @param {string} inputText - 用户输入的原始文本
 * @param {Object} options - 解析选项
 * @param {boolean} options.strict - 是否使用严格模式解析
 * @param {string} options.language - 输入语言 ('zh', 'en')
 * @returns {Promise<Object>} 解析后的个人信息对象
 * @throws {ValidationError} 当输入格式无效时抛出
 * 
 * @example
 * const result = await parsePersonalInfo('姓名：张三\n护照：A12345', {
 *   strict: true,
 *   language: 'zh'
 * });
 */
async function parsePersonalInfo(inputText, options = {}) {
    // 实现细节...
}

// 单行注释：解释复杂逻辑的目的
// 检查护照号格式是否符合国际标准
if (!/^[A-Z0-9]{6,12}$/.test(passport)) {
    throw new ValidationError('护照号格式无效');
}
```

#### 错误处理规范
```javascript
// ✅ 统一错误处理模式

// 1. 自定义错误类
class MDACError extends Error {
    constructor(message, code, details = {}) {
        super(message);
        this.name = 'MDACError';
        this.code = code;
        this.details = details;
        this.timestamp = new Date().toISOString();
    }
}

// 2. 错误码常量
const ERROR_CODES = {
    VALIDATION_FAILED: 'VALIDATION_FAILED',
    API_REQUEST_FAILED: 'API_REQUEST_FAILED',
    NETWORK_ERROR: 'NETWORK_ERROR'
};

// 3. 统一错误处理
async function safeApiCall(apiFunction) {
    try {
        return await apiFunction();
    } catch (error) {
        // 记录错误日志
        mdacLogger?.log('error', 'APICall', error.message, {
            stack: error.stack,
            timestamp: new Date().toISOString()
        });
        
        // 转换为统一错误格式
        throw new MDACError(
            '网络请求失败', 
            ERROR_CODES.API_REQUEST_FAILED,
            { originalError: error.message }
        );
    }
}
```

### HTML/CSS规范

#### HTML结构标准
```html
<!-- ✅ 语义化HTML结构 -->
<div class="mdac-sidebar" role="complementary" aria-label="MDAC助手侧边栏">
    <header class="mdac-sidebar__header">
        <h1 class="mdac-sidebar__title">MDAC AI助手</h1>
        <div class="mdac-sidebar__status" id="connectionStatus" aria-live="polite">
            就绪
        </div>
    </header>
    
    <main class="mdac-sidebar__content">
        <section class="mdac-form-section" aria-labelledby="personal-info-title">
            <h2 id="personal-info-title" class="mdac-form-section__title">
                个人信息
            </h2>
            <textarea 
                id="personalInfoText"
                class="mdac-form-section__textarea"
                placeholder="请输入您的个人信息..."
                aria-describedby="personal-info-help"
            ></textarea>
            <div id="personal-info-help" class="mdac-form-section__help">
                支持中英文混合输入，AI会自动识别
            </div>
        </section>
    </main>
</div>
```

#### CSS命名规范 (BEM)
```css
/* ✅ BEM命名规范：块__元素--修饰符 */

/* 块 (Block) */
.mdac-sidebar { }

/* 元素 (Element) */
.mdac-sidebar__header { }
.mdac-sidebar__title { }
.mdac-sidebar__content { }

/* 修饰符 (Modifier) */
.mdac-sidebar--collapsed { }
.mdac-sidebar__status--error { }
.mdac-sidebar__status--loading { }

/* 状态类 */
.is-hidden { display: none; }
.is-loading { opacity: 0.6; }
.is-disabled { pointer-events: none; }
```

---

## 🔄 Git工作流程

### 分支策略

#### 分支命名规范
```bash
# 主要分支
main          # 生产分支，只包含稳定版本
develop       # 开发分支，最新开发进度

# 功能分支
feature/功能描述     # 新功能开发
feature/ai-ocr      # 示例：AI OCR功能
feature/multi-lang  # 示例：多语言支持

# 修复分支
bugfix/问题描述      # Bug修复
bugfix/form-fill    # 示例：表单填充问题修复

# 热修复分支
hotfix/紧急问题      # 生产环境紧急修复
hotfix/security     # 示例：安全问题修复

# 发布分支
release/版本号       # 发布准备
release/v2.1.0      # 示例：v2.1.0发布分支
```

#### 标准工作流程
```bash
# 1. 更新本地主分支
git checkout main
git pull upstream main

# 2. 创建功能分支
git checkout -b feature/new-feature

# 3. 开发和提交
# ... 编写代码 ...
git add .
git commit -m "feat: 添加新功能的核心逻辑"

# 4. 定期同步主分支
git fetch upstream
git rebase upstream/main

# 5. 推送到你的Fork
git push origin feature/new-feature

# 6. 创建Pull Request
# 在GitHub上创建PR，从你的分支到upstream/main
```

### 提交消息规范 (Conventional Commits)

#### 提交消息格式
```
<类型>(<范围>): <描述>

[可选的正文]

[可选的脚注]
```

#### 提交类型
```bash
feat:     # 新功能
fix:      # Bug修复
docs:     # 文档更新
style:    # 代码格式调整（不影响功能）
refactor: # 代码重构（既不是新功能也不是Bug修复）
perf:     # 性能优化
test:     # 添加或修改测试
chore:    # 构建过程或辅助工具的变动
ci:       # CI/CD配置变更
revert:   # 回滚之前的提交
```

#### 提交消息示例
```bash
# ✅ 好的提交消息
feat(ai): 添加OCR图片识别功能
fix(ui): 修复侧边栏在小屏幕上的显示问题
docs(api): 更新StateManager API文档
style(css): 统一按钮样式规范
refactor(core): 简化事件总线实现
perf(storage): 优化数据存储性能
test(form): 添加表单检测单元测试

# ❌ 不好的提交消息
fix bug           # 太模糊
update code       # 没有说明更新了什么
添加新功能        # 应该使用英文
WIP              # 不应该提交未完成的工作
```

#### 详细提交消息示例
```bash
feat(ai): 添加智能表单字段映射功能

- 实现基于机器学习的字段识别算法
- 支持自定义字段映射规则配置
- 添加映射准确率统计和反馈机制

Closes #123
Co-authored-by: Jane Doe <<EMAIL>>
```

---

## 🧪 测试要求

### 测试策略

#### 测试层级
```
测试金字塔:
    🔺 E2E测试 (少量)
       - 完整用户流程测试
       - 真实环境集成测试
    
    🔶 集成测试 (适量)
       - 模块间交互测试
       - API集成测试
    
    🔷 单元测试 (大量)
       - 函数级别测试
       - 组件独立测试
```

#### 测试工具栈
```javascript
// 测试框架和工具
{
  "unitTest": "Jest",           // 单元测试框架
  "e2eTest": "Puppeteer",      // 端到端测试
  "coverage": "Istanbul",       // 代码覆盖率
  "mocking": "Sinon",          // 模拟和存根
  "assertions": "Chai"          // 断言库
}
```

### 单元测试标准

#### 测试文件结构
```
tests/
├── unit/                     # 单元测试
│   ├── modules/
│   │   ├── event-bus.test.js
│   │   ├── state-manager.test.js
│   │   └── logger.test.js
│   └── utils/
│       └── validation.test.js
├── integration/              # 集成测试
│   ├── ai-parsing.test.js
│   └── form-filling.test.js
├── e2e/                      # 端到端测试
│   ├── user-workflow.test.js
│   └── performance.test.js
└── fixtures/                 # 测试数据
    ├── sample-data.json
    └── mock-responses.json
```

#### 单元测试示例
```javascript
// tests/unit/modules/state-manager.test.js
describe('StateManager', () => {
    let stateManager;
    
    beforeEach(() => {
        // 设置测试环境
        stateManager = new StateManager({
            storageArea: 'local',
            prefix: 'test_'
        });
    });
    
    afterEach(() => {
        // 清理测试数据
        return stateManager.clearAll();
    });
    
    describe('setState', () => {
        it('应该能够保存字符串值', async () => {
            // Arrange
            const key = 'testKey';
            const value = 'testValue';
            
            // Act
            await stateManager.setState(key, value);
            const result = await stateManager.getState(key);
            
            // Assert
            expect(result).toBe(value);
        });
        
        it('应该能够保存对象值', async () => {
            // Arrange
            const key = 'userInfo';
            const value = {
                name: 'Test User',
                email: '<EMAIL>'
            };
            
            // Act
            await stateManager.setState(key, value);
            const result = await stateManager.getState(key);
            
            // Assert
            expect(result).toEqual(value);
            expect(result.name).toBe('Test User');
        });
        
        it('应该在传入无效参数时抛出错误', async () => {
            // Arrange & Act & Assert
            await expect(stateManager.setState()).rejects.toThrow();
            await expect(stateManager.setState('')).rejects.toThrow();
        });
    });
    
    describe('状态变化订阅', () => {
        it('应该在状态变化时通知订阅者', async () => {
            // Arrange
            const key = 'watchedKey';
            const newValue = 'newValue';
            const callback = jest.fn();
            
            // Act
            stateManager.subscribe(key, callback);
            await stateManager.setState(key, newValue);
            
            // Assert
            expect(callback).toHaveBeenCalledWith(newValue, undefined, key);
        });
    });
});
```

### 集成测试示例
```javascript
// tests/integration/ai-parsing.test.js
describe('AI解析集成测试', () => {
    let mockBackground;
    let mockStateManager;
    
    beforeEach(() => {
        // 设置模拟环境
        mockBackground = {
            callGeminiAI: jest.fn()
        };
        
        mockStateManager = {
            setState: jest.fn(),
            getState: jest.fn()
        };
        
        global.chrome = {
            runtime: {
                sendMessage: jest.fn((message, callback) => {
                    // 模拟背景脚本响应
                    setTimeout(() => {
                        callback({
                            success: true,
                            data: mockParseResult
                        });
                    }, 100);
                })
            }
        };
    });
    
    it('应该能够完成完整的AI解析流程', async () => {
        // Arrange
        const inputText = '姓名：张三\n护照：*********';
        const expectedResult = {
            name: 'ZHANG SAN',
            passport: '*********'
        };
        
        // Act
        const controller = new MDACMainController();
        await controller.parsePersonalInfo();
        
        // Assert
        expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
            expect.objectContaining({
                action: 'callGeminiAI',
                userInput: inputText
            }),
            expect.any(Function)
        );
    });
});
```

### 端到端测试示例
```javascript
// tests/e2e/user-workflow.test.js
const puppeteer = require('puppeteer');

describe('用户完整工作流程', () => {
    let browser;
    let page;
    
    beforeAll(async () => {
        browser = await puppeteer.launch({
            headless: false,  // 在开发时可以看到浏览器操作
            args: [
                '--load-extension=./dist',  // 加载扩展
                '--disable-extensions-except=./dist'
            ]
        });
    });
    
    afterAll(async () => {
        await browser.close();
    });
    
    beforeEach(async () => {
        page = await browser.newPage();
        await page.goto('https://example.com');  // 测试页面
    });
    
    afterEach(async () => {
        await page.close();
    });
    
    it('用户应该能够完成完整的表单填充流程', async () => {
        // 1. 打开扩展侧边栏
        await page.click('[data-testid="extension-icon"]');
        await page.waitForSelector('.mdac-sidebar');
        
        // 2. 输入个人信息
        await page.type('#personalInfoText', '姓名：张三\n护照：*********');
        
        // 3. 点击AI解析按钮
        await page.click('[data-testid="ai-parse-btn"]');
        
        // 4. 等待解析完成
        await page.waitForSelector('.parse-success');
        
        // 5. 填充表单
        await page.click('[data-testid="fill-form-btn"]');
        
        // 6. 验证表单已填充
        const nameField = await page.$eval('#name', el => el.value);
        const passportField = await page.$eval('#passport', el => el.value);
        
        expect(nameField).toBe('ZHANG SAN');
        expect(passportField).toBe('*********');
    });
});
```

### 代码覆盖率要求

```javascript
// jest.config.js
module.exports = {
    collectCoverage: true,
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'lcov', 'html'],
    coverageThreshold: {
        global: {
            branches: 80,      // 分支覆盖率 >= 80%
            functions: 85,     // 函数覆盖率 >= 85%
            lines: 85,         // 行覆盖率 >= 85%
            statements: 85     // 语句覆盖率 >= 85%
        }
    },
    collectCoverageFrom: [
        'modules/**/*.js',
        'ui/**/*.js',
        '!**/*.test.js',
        '!**/node_modules/**'
    ]
};
```

---

## 📋 Pull Request流程

### PR创建指南

#### PR标题规范
```
格式: <类型>(<范围>): <简短描述>

示例:
feat(ai): 添加OCR图片识别功能
fix(ui): 修复侧边栏响应式布局问题
docs(api): 完善StateManager API文档
```

#### PR描述模板
```markdown
## 📋 变更描述
[简要描述这个PR的目的和主要变更]

## 🎯 变更类型
- [ ] 新功能 (feature)
- [ ] Bug修复 (bugfix)
- [ ] 文档更新 (docs)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (performance)
- [ ] 测试添加/修改 (test)
- [ ] 其他 (chore)

## 🧪 测试
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成
- [ ] 代码覆盖率达标

## 📸 截图/演示
[如果是UI变更，请提供before/after截图或GIF演示]

## ✅ 检查清单
- [ ] 代码遵循项目规范
- [ ] 已添加/更新相关测试
- [ ] 已更新相关文档
- [ ] 提交消息符合规范
- [ ] 代码已经过自测
- [ ] 无合并冲突

## 🔗 相关Issue
Closes #123
Related to #456

## 📝 备注
[任何需要审查者特别注意的点]
```

### 代码审查标准

#### 审查要点
```markdown
🔍 代码审查检查清单:

## 功能性
- [ ] 代码实现了预期功能
- [ ] 边界情况处理正确
- [ ] 错误处理完善
- [ ] 性能影响可接受

## 代码质量
- [ ] 代码可读性良好
- [ ] 命名清晰有意义
- [ ] 函数和类职责单一
- [ ] 避免重复代码

## 安全性
- [ ] 没有硬编码敏感信息
- [ ] 输入验证充分
- [ ] 权限检查正确
- [ ] 依赖安全可靠

## 测试
- [ ] 测试覆盖充分
- [ ] 测试用例有意义
- [ ] 测试可以稳定通过
- [ ] 集成测试验证

## 文档
- [ ] API文档已更新
- [ ] 代码注释充分
- [ ] 变更日志已更新
- [ ] 用户文档同步
```

#### 审查反馈指南
```markdown
💬 提供建设性反馈:

✅ 好的反馈:
- "这个函数的逻辑很清晰，建议添加JSDoc注释说明参数类型"
- "考虑使用Map代替对象来提升查找性能"
- "这里的错误处理可以更具体，建议区分不同错误类型"

❌ 避免的反馈:
- "代码不好"
- "重写这个函数"
- "我不喜欢这个实现"
```

### PR合并要求

#### 合并前检查
```bash
# 自动化检查 (CI/CD)
✅ 所有测试通过
✅ 代码质量检查通过
✅ 安全扫描通过
✅ 构建成功

# 人工审查要求
✅ 至少2个核心贡献者审查通过
✅ 项目维护者最终批准
✅ 所有讨论已解决
✅ 分支已更新到最新主分支
```

#### 合并策略
```
合并方式:
1. Squash and Merge (推荐)
   - 将多个提交压缩为一个
   - 保持主分支历史清洁
   
2. Merge Commit
   - 保留完整的分支历史
   - 适用于重要功能开发
   
3. Rebase and Merge
   - 重写提交历史
   - 创建线性历史
```

---

## 🛡️ 安全规范

### 代码安全要求

#### 敏感信息保护
```javascript
// ❌ 错误做法 - 硬编码敏感信息
const API_KEY = "sk-1234567890abcdef";
const DATABASE_PASSWORD = "super_secret_password";

// ✅ 正确做法 - 使用环境变量或配置
const API_KEY = process.env.GEMINI_API_KEY || '';
const config = await loadSecureConfig();

// ✅ 在日志中脱敏敏感信息
function logUserAction(userData) {
    const sanitized = {
        ...userData,
        passport: userData.passport ? '***' + userData.passport.slice(-3) : '',
        email: userData.email ? userData.email.replace(/(.{2})(.*)(@.*)/, '$1***$3') : ''
    };
    
    mdacLogger.log('info', 'UserAction', 'User data processed', sanitized);
}
```

#### 输入验证和清理
```javascript
// ✅ 严格的输入验证
class InputValidator {
    static validatePersonalInfo(data) {
        const errors = [];
        
        // 验证姓名
        if (!data.name || typeof data.name !== 'string') {
            errors.push('姓名是必需的字符串');
        } else if (data.name.length > 100) {
            errors.push('姓名长度不能超过100字符');
        }
        
        // 验证护照号
        if (!data.passport || !/^[A-Za-z0-9]{6,12}$/.test(data.passport)) {
            errors.push('护照号格式无效');
        }
        
        // 验证日期
        if (data.birthDate && !moment(data.birthDate, 'YYYY-MM-DD', true).isValid()) {
            errors.push('出生日期格式无效');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    static sanitizeInput(input) {
        if (typeof input !== 'string') return '';
        
        return input
            .trim()
            .replace(/[<>]/g, '')      // 移除HTML标签字符
            .substring(0, 1000);       // 限制长度
    }
}
```

### 依赖安全管理

#### 定期安全检查
```bash
# 检查已知漏洞
npm audit

# 修复自动可修复的漏洞
npm audit fix

# 检查过时依赖
npm outdated

# 使用工具检查许可证兼容性
npx license-checker
```

#### 依赖更新策略
```json
{
  "scripts": {
    "security-check": "npm audit && npm outdated",
    "update-deps": "npm update",
    "check-licenses": "license-checker --onlyAllow 'MIT;Apache-2.0;BSD-3-Clause'"
  }
}
```

---

## 📊 性能要求

### 性能基准

#### 性能目标
```javascript
// 性能要求基准
const PERFORMANCE_TARGETS = {
    moduleLoadTime: 100,     // 模块加载 < 100ms
    uiResponseTime: 50,      // UI响应 < 50ms
    aiParsingTime: 5000,     // AI解析 < 5s
    formFillTime: 200,       // 表单填充 < 200ms
    memoryUsage: 50,         // 内存使用 < 50MB
    storageSize: 5           // 存储大小 < 5MB
};
```

#### 性能测试
```javascript
// 性能测试示例
describe('性能测试', () => {
    it('模块加载时间应该在100ms以内', async () => {
        const startTime = performance.now();
        
        // 加载模块
        await import('../modules/state-manager.js');
        
        const loadTime = performance.now() - startTime;
        expect(loadTime).toBeLessThan(100);
    });
    
    it('AI解析应该在5秒内完成', async () => {
        const startTime = performance.now();
        
        // 执行AI解析
        await parsePersonalInfo(testData);
        
        const parseTime = performance.now() - startTime;
        expect(parseTime).toBeLessThan(5000);
    });
});
```

### 优化指导

#### 代码优化建议
```javascript
// ✅ 性能优化最佳实践

// 1. 使用防抖和节流
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 2. 延迟加载
async function lazyLoadModule(moduleName) {
    if (!window[moduleName]) {
        await import(`./modules/${moduleName}.js`);
    }
    return window[moduleName];
}

// 3. 缓存计算结果
const memoize = (fn) => {
    const cache = new Map();
    return (...args) => {
        const key = JSON.stringify(args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        const result = fn(...args);
        cache.set(key, result);
        return result;
    };
};

// 4. 避免内存泄漏
class ComponentManager {
    constructor() {
        this.eventListeners = [];
        this.timers = [];
    }
    
    addEventListener(element, event, handler) {
        element.addEventListener(event, handler);
        this.eventListeners.push({ element, event, handler });
    }
    
    destroy() {
        // 清理事件监听器
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        
        // 清理定时器
        this.timers.forEach(timer => clearTimeout(timer));
        
        // 清空引用
        this.eventListeners = [];
        this.timers = [];
    }
}
```

---

## 📚 文档贡献

### 文档标准

#### 文档结构要求
```markdown
# 标题层级规范

# 主标题 (H1) - 每个文档只有一个
## 主要章节 (H2)
### 子章节 (H3)
#### 详细说明 (H4)

# 必需章节
- 概述/简介
- 使用方法
- API参考 (如适用)
- 示例
- 常见问题
- 更新历史
```

#### API文档规范
```javascript
/**
 * 用户状态管理器
 * 提供用户数据的持久化存储和状态管理功能
 * 
 * @class StateManager
 * @version 2.0.0
 * @since 1.0.0
 * 
 * @example
 * const stateManager = new StateManager({
 *   storageArea: 'local',
 *   prefix: 'mdac_'
 * });
 * 
 * await stateManager.setState('userInfo', userData);
 * const userData = await stateManager.getState('userInfo');
 */
class StateManager {
    /**
     * 创建状态管理器实例
     * 
     * @param {Object} config - 配置选项
     * @param {string} [config.storageArea='local'] - 存储区域 ('local' | 'sync')
     * @param {string} [config.prefix='mdac_'] - 键名前缀
     * @param {boolean} [config.autoSave=true] - 是否自动保存
     * 
     * @throws {Error} 当配置无效时抛出错误
     * 
     * @example
     * const manager = new StateManager({
     *   storageArea: 'sync',
     *   prefix: 'app_'
     * });
     */
    constructor(config = {}) {
        // 实现...
    }
}
```

### 多语言文档

#### 文档翻译指南
```
📖 文档语言优先级:
1. 中文 (zh-CN) - 主要文档
2. 英文 (en-US) - 国际化支持
3. 其他语言 - 社区贡献

文档文件命名:
- README.md (中文)
- README.en.md (英文)
- API_REFERENCE.md (中文)
- API_REFERENCE.en.md (英文)
```

---

## 🎉 社区参与

### 社区行为准则

#### 我们的承诺
- 🤝 **友善互助** - 营造包容、友善的社区环境
- 🚀 **共同成长** - 鼓励学习、分享和相互帮助
- 🎯 **专注品质** - 致力于提供高质量的代码和文档
- 🌍 **开放包容** - 欢迎不同背景和经验水平的参与者

#### 不被接受的行为
- 🚫 人身攻击、歧视性言论
- 🚫 恶意破坏或故意提交低质量代码
- 🚫 未经许可发布他人隐私信息
- 🚫 其他可能造成负面影响的行为

### 获取帮助和支持

#### 社区资源
```
💬 讨论和求助:
- GitHub Discussions - 技术讨论和问答
- GitHub Issues - Bug报告和功能请求
- 开发者邮件列表 - <EMAIL>

📚 学习资源:
- 项目Wiki - 详细开发指南
- 示例代码库 - 最佳实践参考
- 视频教程 - 入门和进阶指导

🎯 专项支持:
- 新手指导 - 专门的导师计划
- 代码审查 - 经验丰富的开发者指导
- 技术分享 - 定期的技术交流会
```

### 贡献认可

#### 贡献者荣誉
```markdown
🏆 贡献者等级:

🌟 贡献者 (Contributor)
- 提交了被接受的PR
- 在贡献者列表中展示

⭐ 活跃贡献者 (Active Contributor)
- 持续贡献代码或文档
- 参与代码审查和社区讨论

🌟 核心贡献者 (Core Contributor)
- 长期维护项目核心功能
- 指导新贡献者
- 参与技术决策

👑 项目维护者 (Maintainer)
- 项目技术方向决策权
- 发布版本管理权限
- 社区管理责任
```

#### 贡献统计
我们会定期更新贡献者统计，包括：
- 代码提交数量和质量
- 文档贡献情况
- 社区参与度
- 问题解决帮助

---

## 📋 开发工具推荐

### IDE配置

#### VS Code推荐插件
```json
{
    "recommendations": [
        "ms-vscode.vscode-eslint",           // ESLint
        "esbenp.prettier-vscode",            // Prettier
        "ms-vscode.vscode-chrome-debug",     // Chrome调试
        "bradlc.vscode-tailwindcss",         // CSS智能提示
        "christian-kohler.path-intellisense", // 路径智能提示
        "streetsidesoftware.code-spell-checker", // 拼写检查
        "ms-vscode.vscode-json",             // JSON支持
        "redhat.vscode-yaml"                 // YAML支持
    ]
}
```

#### 工作区配置
```json
// .vscode/settings.json
{
    "editor.formatOnSave": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "files.exclude": {
        "**/node_modules": true,
        "**/dist": true,
        "**/.git": true
    },
    "eslint.autoFixOnSave": true,
    "prettier.requireConfig": true
}
```

### 构建和部署工具

#### 推荐工具链
```bash
# 代码质量
eslint          # 代码检查
prettier        # 代码格式化
husky          # Git hooks
lint-staged    # 暂存区代码检查

# 测试工具
jest           # 测试框架
puppeteer      # E2E测试
@testing-library/jest-dom  # DOM测试工具

# 构建工具
webpack        # 模块打包
babel          # JavaScript编译
terser         # 代码压缩

# 开发辅助
nodemon        # 文件监控和重启
cross-env      # 跨平台环境变量
concurrently   # 并行运行命令
```

---

## 🚀 发布和版本管理

### 版本发布流程

#### 发布准备检查清单
```markdown
📋 发布前检查:

## 代码质量
- [ ] 所有测试通过
- [ ] 代码覆盖率达标
- [ ] 无安全漏洞
- [ ] 性能测试通过

## 文档更新
- [ ] CHANGELOG已更新
- [ ] README已同步
- [ ] API文档已更新
- [ ] 版本号已更新

## 功能验证
- [ ] 核心功能正常
- [ ] 新功能已测试
- [ ] 兼容性验证
- [ ] 用户验收测试

## 发布准备
- [ ] 发布说明已准备
- [ ] 回滚方案已就绪
- [ ] 监控告警已配置
- [ ] 团队已通知
```

#### 语义化版本控制
```
版本格式: X.Y.Z

X (主版本号): 不兼容的API修改
- 重大架构变更
- 破坏性功能修改
- 需要用户迁移

Y (次版本号): 向后兼容的功能性新增
- 新功能添加
- API扩展
- 性能优化

Z (修订版本号): 向后兼容的问题修正
- Bug修复
- 安全补丁
- 文档更新

例如:
1.0.0 → 1.0.1 (Bug修复)
1.0.1 → 1.1.0 (新功能)
1.1.0 → 2.0.0 (重大变更)
```

---

## 📞 联系方式

### 项目维护团队

```markdown
👥 核心团队:
- 项目负责人: [姓名] <<EMAIL>>
- 技术架构师: [姓名] <<EMAIL>>  
- 社区管理员: [姓名] <<EMAIL>>

📧 联系邮箱:
- 技术问题: <EMAIL>
- 安全问题: <EMAIL>
- 社区合作: <EMAIL>

🔗 在线资源:
- 项目仓库: https://github.com/org/mdac-extension
- 文档网站: https://docs.mdac-extension.com
- 问题跟踪: https://github.com/org/mdac-extension/issues
- 讨论区: https://github.com/org/mdac-extension/discussions
```

---

## 📚 参考资料

### 相关文档
- [开发者指南](./DEVELOPER_GUIDE.md) - 开发环境和工作流程
- [API接口文档](./API_REFERENCE.md) - 详细API说明
- [用户使用手册](./USER_MANUAL.md) - 最终用户指南
- [部署维护手册](./DEPLOYMENT_GUIDE.md) - 部署和运维指南
- [故障排除指南](./TROUBLESHOOTING_GUIDE.md) - 问题诊断和解决

### 外部资源
- [Chrome Extension Developer Guide](https://developer.chrome.com/docs/extensions/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Semantic Versioning](https://semver.org/)
- [Jest Testing Framework](https://jestjs.io/)
- [ESLint Rules](https://eslint.org/docs/rules/)

---

## 🎉 感谢贡献

感谢每一位为MDAC Chrome扩展项目做出贡献的开发者！您的参与让这个项目变得更好。

无论您是：
- 🔧 提交代码改进
- 📝 完善文档说明  
- 🐛 报告问题和Bug
- 💡 提出功能建议
- 🤝 帮助其他用户

您的每一份贡献都是宝贵的！

---

**贡献者指南版本**: 1.0  
**最后更新**: 2025-07-15  
**维护团队**: MDAC开源社区