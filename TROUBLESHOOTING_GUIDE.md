# MDAC Chrome扩展故障排除指南

## 📖 文档概述

**版本**: 1.0  
**更新日期**: 2025-07-15  
**适用版本**: MDAC AI智能填充工具 v2.0.0  
**目标读者**: 用户、技术支持人员、开发者

---

## 🎯 故障排除概述

本指南提供MDAC Chrome扩展常见问题的诊断方法和解决方案。通过系统化的排查步骤，帮助用户快速定位并解决问题，确保扩展的正常使用。

### 🔍 故障分类
- **🚀 安装和启动问题** - 扩展安装、加载、激活相关
- **🎨 界面和交互问题** - UI显示、操作响应相关
- **🤖 AI功能问题** - 智能解析、数据处理相关
- **📝 表单填充问题** - 字段检测、数据填充相关
- **💾 数据存储问题** - 数据保存、读取、同步相关
- **🌐 网络连接问题** - API调用、网络通信相关

---

## 🛠️ 快速诊断工具

### 一键健康检查

#### 在任意网页控制台运行
```javascript
// 快速系统诊断脚本
(function() {
    console.log('🔍 MDAC扩展快速诊断开始...');
    
    const results = [];
    
    // 1. 检查Chrome扩展API
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        results.push('✅ Chrome扩展API正常');
        console.log('扩展ID:', chrome.runtime.id);
    } else {
        results.push('❌ Chrome扩展API不可用');
    }
    
    // 2. 检查核心模块
    const modules = ['mdacEventBus', 'mdacLogger', 'mdacStateManager'];
    modules.forEach(module => {
        if (window[module]) {
            results.push(`✅ ${module}已加载`);
        } else {
            results.push(`❌ ${module}未找到`);
        }
    });
    
    // 3. 检查网络连接
    fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' })
        .then(() => results.push('✅ 网络连接正常'))
        .catch(() => results.push('❌ 网络连接异常'));
    
    // 4. 输出诊断结果
    setTimeout(() => {
        console.log('\n📋 诊断结果汇总:');
        results.forEach(result => console.log(result));
        
        const errors = results.filter(r => r.includes('❌')).length;
        if (errors === 0) {
            console.log('🎉 系统状态良好！');
        } else {
            console.log(`⚠️ 发现${errors}个问题，请查看下方解决方案`);
        }
    }, 2000);
})();
```

### 环境信息收集
```javascript
// 收集环境信息脚本
function collectEnvironmentInfo() {
    const info = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        chromeVersion: /Chrome\/([0-9\.]+)/.exec(navigator.userAgent)?.[1],
        extensionId: chrome?.runtime?.id || 'N/A',
        url: window.location.href,
        modules: {}
    };
    
    // 检查模块状态
    ['mdacEventBus', 'mdacLogger', 'mdacStateManager', 'MDACMainController'].forEach(module => {
        info.modules[module] = window[module] ? 'loaded' : 'missing';
    });
    
    // 内存使用情况
    if (performance.memory) {
        info.memory = {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
        };
    }
    
    console.log('📊 环境信息:', JSON.stringify(info, null, 2));
    return info;
}
```

---

## 🚀 安装和启动问题

### 问题 1.1：扩展无法安装

#### 症状描述
- Chrome Web Store显示"无法添加扩展"
- 安装按钮无响应
- 提示权限不足或其他错误

#### 诊断步骤
```markdown
🔍 检查清单:
1. [ ] Chrome版本 >= 114
2. [ ] 网络连接正常
3. [ ] Chrome登录状态
4. [ ] 扩展管理权限
5. [ ] 磁盘空间充足
```

#### 解决方案

**方案A：基础检查**
1. **更新Chrome浏览器**
   ```
   步骤：
   1. 点击Chrome菜单（三个点）
   2. 帮助 → 关于Google Chrome
   3. 等待自动更新完成
   4. 重启Chrome后重试
   ```

2. **检查网络连接**
   ```
   测试步骤：
   1. 访问其他网站确认网络正常
   2. 尝试安装其他扩展验证Chrome Web Store连接
   3. 如在公司网络，联系IT确认代理设置
   ```

**方案B：清理和重置**
1. **清理Chrome数据**
   ```
   警告：这会清除浏览器数据，请先备份重要信息
   
   步骤：
   1. Chrome设置 → 高级 → 重置和清理
   2. 清理计算机
   3. 重置设置为原始默认设置
   4. 重启Chrome后重试安装
   ```

2. **手动安装（开发版本）**
   ```
   步骤：
   1. 下载扩展源码包
   2. 解压到本地文件夹
   3. Chrome → 扩展程序 → 开启开发者模式
   4. 加载已解压的扩展程序
   ```

### 问题 1.2：扩展已安装但无法启动

#### 症状描述
- 扩展在扩展管理页面显示已安装
- 工具栏中没有扩展图标
- 或图标显示为灰色

#### 诊断步骤
1. **检查扩展状态**
   ```
   访问：chrome://extensions/
   查找：MDAC AI智能分析工具
   确认：
   - [ ] 扩展开关为"开启"状态
   - [ ] 没有错误信息显示
   - [ ] 权限已正确授予
   ```

2. **检查错误日志**
   ```
   在扩展管理页面：
   1. 找到MDAC扩展
   2. 点击"详细信息"
   3. 查看"错误"部分
   4. 记录任何错误信息
   ```

#### 解决方案

**方案A：重新启用扩展**
```
步骤：
1. chrome://extensions/
2. 找到MDAC扩展，关闭开关
3. 等待3秒后重新开启
4. 刷新当前页面
5. 检查工具栏图标是否出现
```

**方案B：重新安装扩展**
```
步骤：
1. 记录当前设置和数据（如需要）
2. 在扩展管理页面点击"移除"
3. 重启Chrome浏览器
4. 重新从Chrome Web Store安装
5. 重新配置设置
```

### 问题 1.3：权限错误

#### 症状描述
- 提示"权限不足"
- 无法访问目标网站
- 功能受限

#### 诊断和解决
```
🔒 权限检查：
1. 访问 chrome://extensions/
2. 点击MDAC扩展的"详细信息"
3. 确认以下权限已启用：
   - [ ] 在所有网站上读取和更改数据（或仅MDAC网站）
   - [ ] 管理应用、扩展程序和主题背景
   - [ ] 存储
```

**修复权限**：
1. 如果权限缺失，点击"扩展程序选项"
2. 重新授予所需权限
3. 刷新页面后重试

---

## 🎨 界面和交互问题

### 问题 2.1：侧边栏无法打开

#### 症状描述
- 点击扩展图标无响应
- 或图标菜单中没有"打开侧边栏"选项
- 侧边栏闪现后立即关闭

#### 诊断步骤
```javascript
// 在控制台检查侧边栏状态
chrome.sidePanel && chrome.sidePanel.getOptions(
    { tabId: chrome.tabs.getCurrent().id },
    (options) => console.log('侧边栏选项:', options)
);
```

#### 解决方案

**方案A：基础修复**
1. **刷新页面重试**
   ```
   1. 按F5刷新当前页面
   2. 等待页面完全加载
   3. 重新点击扩展图标
   ```

2. **检查Chrome版本**
   ```
   侧边栏功能需要Chrome 114+
   如版本过低，请更新Chrome浏览器
   ```

**方案B：深度修复**
1. **清除扩展数据**
   ```javascript
   // 在控制台执行
   chrome.storage.local.clear(() => {
       console.log('扩展数据已清除');
       location.reload();
   });
   ```

2. **重新加载扩展**
   ```
   1. chrome://extensions/
   2. 找到MDAC扩展点击刷新按钮
   3. 等待重新加载完成
   4. 重试打开侧边栏
   ```

### 问题 2.2：界面显示异常

#### 症状描述
- 侧边栏界面布局错乱
- 按钮或文本无法正常显示
- CSS样式丢失

#### 诊断步骤
```javascript
// 检查UI控制器状态
if (window.mdacMainController) {
    console.log('✅ UI控制器已加载');
    console.log('状态:', window.mdacMainController.getStatus());
} else {
    console.error('❌ UI控制器未加载');
}
```

#### 解决方案

**方案A：强制刷新UI**
```javascript
// 在侧边栏控制台执行
if (window.mdacMainController) {
    window.mdacMainController.refreshUI();
} else {
    location.reload();
}
```

**方案B：修复CSS加载**
1. **检查样式文件**
   ```
   在侧边栏页面按F12：
   1. 查看Network标签
   2. 确认CSS文件是否加载成功
   3. 检查是否有404或其他错误
   ```

2. **手动重新加载样式**
   ```javascript
   // 强制重新加载CSS
   const link = document.querySelector('link[href*="sidepanel.css"]');
   if (link) {
       link.href = link.href + '?t=' + Date.now();
   }
   ```

### 问题 2.3：按钮无响应

#### 症状描述
- 点击按钮没有任何反应
- 按钮显示正常但功能失效
- 部分按钮工作，部分不工作

#### 诊断步骤
```javascript
// 检查事件监听器
function checkEventListeners() {
    const buttons = document.querySelectorAll('button');
    buttons.forEach((btn, index) => {
        const listeners = getEventListeners(btn);
        console.log(`按钮${index + 1}:`, btn.textContent, '监听器:', listeners);
    });
}
checkEventListeners();
```

#### 解决方案

**方案A：重新绑定事件**
```javascript
// 强制重新初始化UI
if (window.mdacMainController) {
    window.mdacMainController.rebindEvents();
} else {
    console.log('正在重新加载控制器...');
    location.reload();
}
```

**方案B：检查错误阻塞**
```javascript
// 检查是否有JavaScript错误
console.log('最近的错误:');
if (window.mdacLogger) {
    const errors = window.mdacLogger.getLogs('error');
    errors.slice(-5).forEach(error => {
        console.error('错误:', error.message, error.details);
    });
}
```

---

## 🤖 AI功能问题

### 问题 3.1：AI解析失败

#### 症状描述
- 点击"AI解析"按钮后提示错误
- 解析过程卡住不动
- 返回空结果或格式错误的结果

#### 诊断步骤

**步骤1：检查网络连接**
```javascript
// 测试API连接
async function testAPIConnection() {
    try {
        const response = await fetch('https://generativelanguage.googleapis.com/', {
            method: 'HEAD',
            mode: 'no-cors'
        });
        console.log('✅ API端点可达');
    } catch (error) {
        console.error('❌ API连接失败:', error);
    }
}
testAPIConnection();
```

**步骤2：检查输入格式**
```javascript
// 验证输入格式
function validateInput(text) {
    const checks = {
        notEmpty: text.trim().length > 0,
        hasPersonalInfo: /姓名|护照|出生|国籍/.test(text),
        reasonableLength: text.length > 10 && text.length < 2000
    };
    
    console.log('输入验证结果:', checks);
    return Object.values(checks).every(Boolean);
}

// 使用示例
const userInput = document.querySelector('#personalInfoText').value;
validateInput(userInput);
```

#### 解决方案

**方案A：输入格式优化**
```
✅ 推荐输入格式:
姓名：ZHANG SAN
护照号：A12345678
出生日期：1990/01/01
国籍：中国
性别：男

❌ 避免的格式:
我的名字是张三，护照号码A12345678，1990年1月1日出生...
```

**方案B：API配置检查**
```javascript
// 检查AI配置
if (window.MDAC_AI_CONFIG) {
    console.log('✅ AI配置已加载');
    console.log('配置概要:', {
        hasPrompts: !!window.MDAC_AI_CONFIG.AI_PROMPTS,
        hasConfig: !!window.MDAC_AI_CONFIG.GEMINI_CONFIG,
        promptCount: Object.keys(window.MDAC_AI_CONFIG.AI_PROMPTS || {}).length
    });
} else {
    console.error('❌ AI配置未加载');
}
```

**方案C：手动重试机制**
```javascript
// 手动触发AI解析
async function manualAIRetry() {
    const text = prompt('请输入要解析的文本:');
    if (!text) return;
    
    try {
        // 直接调用背景脚本
        const response = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: window.MDAC_AI_CONFIG.AI_PROMPTS.PERSONAL_INFO_PARSING,
                userInput: text
            }, (response) => {
                if (response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
        
        console.log('✅ AI解析成功:', response);
    } catch (error) {
        console.error('❌ AI解析失败:', error);
    }
}
```

### 问题 3.2：解析结果不准确

#### 症状描述
- AI能够返回结果，但信息识别错误
- 字段匹配不正确
- 部分信息丢失或格式错误

#### 解决方案

**方案A：改进输入质量**
```
📝 输入优化建议:
1. 使用标准格式和关键词
2. 避免歧义表达
3. 提供完整信息

示例改进：
❌ 我叫张三，是中国人
✅ 姓名：ZHANG SAN
   国籍：中国
```

**方案B：手动修正结果**
```javascript
// 手动修正解析结果
function manualCorrection(originalResult) {
    const corrected = { ...originalResult };
    
    // 常见修正规则
    if (corrected.name && !/^[A-Z\s]+$/.test(corrected.name)) {
        console.warn('姓名格式可能需要修正为大写英文');
    }
    
    if (corrected.nationality && corrected.nationality.includes('中国')) {
        corrected.nationality = 'CHINA';
    }
    
    return corrected;
}
```

### 问题 3.3：AI服务不可用

#### 症状描述
- 提示"AI服务暂时不可用"
- 长时间无响应
- 网络错误

#### 诊断和解决
```javascript
// AI服务状态检查
async function checkAIServiceStatus() {
    const checks = [];
    
    // 1. 网络连接检查
    try {
        await fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' });
        checks.push('✅ 基础网络连接正常');
    } catch (error) {
        checks.push('❌ 网络连接异常');
        return checks;
    }
    
    // 2. API服务检查
    try {
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
        });
        
        if (response.status === 401) {
            checks.push('⚠️ API密钥问题，但服务可用');
        } else if (response.ok) {
            checks.push('✅ API服务正常');
        } else {
            checks.push(`❌ API服务异常: ${response.status}`);
        }
    } catch (error) {
        checks.push('❌ API服务不可达');
    }
    
    console.log('AI服务状态检查结果:');
    checks.forEach(check => console.log(check));
    
    return checks;
}
```

---

## 📝 表单填充问题

### 问题 4.1：无法检测表单字段

#### 症状描述
- 点击"更新到MDAC"后提示找不到表单
- 表单字段未被正确识别
- 部分字段能检测，部分不能

#### 诊断步骤
```javascript
// 手动检测表单字段
function manualFormDetection() {
    console.log('🔍 开始手动表单检测...');
    
    // 1. 检测所有表单
    const forms = document.querySelectorAll('form');
    console.log(`发现 ${forms.length} 个表单`);
    
    // 2. 检测所有输入字段
    const inputs = document.querySelectorAll('input, select, textarea');
    console.log(`发现 ${inputs.length} 个输入字段`);
    
    // 3. 分析字段属性
    inputs.forEach((input, index) => {
        const info = {
            index: index + 1,
            type: input.type || input.tagName.toLowerCase(),
            name: input.name,
            id: input.id,
            placeholder: input.placeholder,
            className: input.className
        };
        
        console.log(`字段 ${index + 1}:`, info);
    });
    
    return { forms: forms.length, inputs: inputs.length };
}
```

#### 解决方案

**方案A：确认页面状态**
```
📋 页面检查清单:
1. [ ] 确认在MDAC官方网站
2. [ ] 页面完全加载完成
3. [ ] 没有弹窗或遮罩层
4. [ ] 表单字段可见且可编辑
```

**方案B：手动字段检测**
```javascript
// 强制重新检测表单
if (window.FormFieldDetector) {
    const detector = new window.FormFieldDetector();
    const fields = detector.detectFormFields();
    console.log('检测到的字段:', fields);
    
    // 验证检测质量
    const validation = detector.validateDetection(fields);
    console.log('检测质量:', validation);
} else {
    console.error('字段检测器未加载');
}
```

### 问题 4.2：填充数据不正确

#### 症状描述
- 数据填充到错误的字段
- 数据格式与表单要求不匹配
- 部分字段填充成功，部分失败

#### 解决方案

**方案A：数据格式检查**
```javascript
// 检查数据格式
async function checkDataFormat() {
    const data = await mdacStateManager.getState('personalInfo');
    
    if (!data) {
        console.error('❌ 未找到个人信息数据');
        return false;
    }
    
    const formatChecks = {
        name: data.name && /^[A-Za-z\s]+$/.test(data.name),
        passport: data.passport && /^[A-Za-z0-9]+$/.test(data.passport),
        birthDate: data.birthDate && /^\d{4}[-\/]\d{2}[-\/]\d{2}$/.test(data.birthDate),
        nationality: data.nationality && data.nationality.length > 0
    };
    
    console.log('数据格式检查:', formatChecks);
    return formatChecks;
}
```

**方案B：手动字段映射**
```javascript
// 手动映射字段
function manualFieldMapping() {
    const data = {
        name: 'ZHANG SAN',
        passport: 'A12345678',
        birthDate: '1990/01/01',
        nationality: 'CHINA'
    };
    
    // 手动查找和填充字段
    const mappings = [
        { field: 'name', selectors: ['#fullName', '[name="name"]', '[placeholder*="name"]'] },
        { field: 'passport', selectors: ['#passportNo', '[name="passport"]', '[placeholder*="passport"]'] },
        { field: 'birthDate', selectors: ['#birthDate', '[name="birthDate"]', '[type="date"]'] },
        { field: 'nationality', selectors: ['#nationality', '[name="nationality"]'] }
    ];
    
    mappings.forEach(mapping => {
        const value = data[mapping.field];
        if (!value) return;
        
        for (const selector of mapping.selectors) {
            const element = document.querySelector(selector);
            if (element) {
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`✅ ${mapping.field} 已填充到 ${selector}`);
                break;
            }
        }
    });
}
```

### 问题 4.3：MDAC网站访问问题

#### 症状描述
- MDAC网站无法打开
- 提示"请先打开MDAC网站"
- 网站加载异常

#### 诊断步骤
```javascript
// 检查网站状态
async function checkMDACWebsite() {
    const urls = [
        'https://imigresen-online.imi.gov.my/',
        'https://imigresen-online.imi.gov.my/mdac/main?registerMain'
    ];
    
    for (const url of urls) {
        try {
            const response = await fetch(url, { 
                method: 'HEAD',
                mode: 'no-cors'
            });
            console.log(`✅ ${url} 可访问`);
        } catch (error) {
            console.log(`❌ ${url} 不可访问:`, error.message);
        }
    }
}
```

#### 解决方案

**方案A：替代访问方式**
```
🌐 访问建议:
1. 直接访问: https://imigresen-online.imi.gov.my/
2. 搜索: Google搜索"Malaysia Digital Arrival Card"
3. 使用VPN: 如果在某些地区访问受限
4. 等待恢复: 网站可能临时维护
```

**方案B：离线模式使用**
```javascript
// 启用离线测试模式
function enableOfflineMode() {
    // 创建模拟表单进行测试
    const testForm = document.createElement('form');
    testForm.innerHTML = `
        <input name="name" placeholder="Full Name" />
        <input name="passport" placeholder="Passport Number" />
        <input name="birthDate" type="date" />
        <select name="nationality">
            <option value="">Select Nationality</option>
            <option value="CHINA">China</option>
        </select>
    `;
    
    document.body.appendChild(testForm);
    console.log('✅ 已创建测试表单，可以测试填充功能');
}
```

---

## 💾 数据存储问题

### 问题 5.1：数据无法保存

#### 症状描述
- 输入信息后关闭浏览器，数据丢失
- 提示"保存失败"
- 数据预览显示空白

#### 诊断步骤
```javascript
// 测试存储功能
async function testStorageFunction() {
    const testData = {
        test: 'value',
        timestamp: Date.now()
    };
    
    try {
        // 测试设置数据
        await chrome.storage.local.set({ 'mdac_test': testData });
        console.log('✅ 数据写入成功');
        
        // 测试读取数据
        const result = await chrome.storage.local.get('mdac_test');
        if (result.mdac_test && result.mdac_test.test === 'value') {
            console.log('✅ 数据读取成功');
        } else {
            console.error('❌ 数据读取失败');
        }
        
        // 清理测试数据
        await chrome.storage.local.remove('mdac_test');
        console.log('✅ 存储功能正常');
        
    } catch (error) {
        console.error('❌ 存储功能异常:', error);
    }
}
```

#### 解决方案

**方案A：权限修复**
```
🔒 权限检查和修复:
1. chrome://extensions/
2. 找到MDAC扩展 → 详细信息
3. 确认"存储"权限已启用
4. 如未启用，重新安装扩展
```

**方案B：存储空间清理**
```javascript
// 检查存储使用情况
chrome.storage.local.getBytesInUse(null, (bytes) => {
    console.log('当前存储使用:', bytes, 'bytes');
    
    // Chrome扩展存储限制通常是5MB
    if (bytes > 5 * 1024 * 1024 * 0.8) {  // 80%警告线
        console.warn('⚠️ 存储空间使用过高，建议清理');
    }
});

// 清理过期数据
async function cleanupStorage() {
    const allData = await chrome.storage.local.get();
    const keysToRemove = [];
    
    Object.entries(allData).forEach(([key, value]) => {
        // 清理超过30天的数据
        if (value.timestamp && Date.now() - value.timestamp > 30 * 24 * 60 * 60 * 1000) {
            keysToRemove.push(key);
        }
    });
    
    if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`✅ 已清理 ${keysToRemove.length} 个过期数据项`);
    }
}
```

### 问题 5.2：数据损坏或格式错误

#### 症状描述
- 保存的数据无法正确读取
- 数据格式异常
- 部分数据丢失

#### 解决方案

**方案A：数据验证和修复**
```javascript
// 数据完整性检查
async function validateUserData() {
    try {
        const personalInfo = await mdacStateManager.getState('personalInfo');
        const travelInfo = await mdacStateManager.getState('travelInfo');
        
        const issues = [];
        
        // 检查个人信息
        if (personalInfo) {
            if (!personalInfo.name || typeof personalInfo.name !== 'string') {
                issues.push('个人姓名数据异常');
            }
            if (!personalInfo.passport || !/^[A-Za-z0-9]+$/.test(personalInfo.passport)) {
                issues.push('护照号码格式异常');
            }
        }
        
        // 检查旅行信息
        if (travelInfo && typeof travelInfo !== 'object') {
            issues.push('旅行信息格式异常');
        }
        
        if (issues.length > 0) {
            console.warn('⚠️ 发现数据问题:', issues);
            return false;
        } else {
            console.log('✅ 数据完整性检查通过');
            return true;
        }
        
    } catch (error) {
        console.error('❌ 数据验证失败:', error);
        return false;
    }
}
```

**方案B：数据重置和恢复**
```javascript
// 安全重置用户数据
async function safeDataReset() {
    const backup = {};
    
    try {
        // 1. 备份当前数据
        backup.personalInfo = await mdacStateManager.getState('personalInfo');
        backup.travelInfo = await mdacStateManager.getState('travelInfo');
        backup.settings = await mdacStateManager.getState('userSettings');
        
        console.log('📦 数据已备份:', backup);
        
        // 2. 清除损坏的数据
        await mdacStateManager.clearState('personalInfo');
        await mdacStateManager.clearState('travelInfo');
        
        // 3. 重新初始化
        console.log('✅ 数据已重置，请重新输入信息');
        
        // 4. 提供恢复选项
        window.mdacDataBackup = backup;
        console.log('💡 如需恢复数据，运行: restoreFromBackup()');
        
    } catch (error) {
        console.error('❌ 数据重置失败:', error);
    }
}

// 从备份恢复数据
async function restoreFromBackup() {
    if (window.mdacDataBackup) {
        try {
            const backup = window.mdacDataBackup;
            
            if (backup.personalInfo) {
                await mdacStateManager.setState('personalInfo', backup.personalInfo);
            }
            if (backup.travelInfo) {
                await mdacStateManager.setState('travelInfo', backup.travelInfo);
            }
            if (backup.settings) {
                await mdacStateManager.setState('userSettings', backup.settings);
            }
            
            console.log('✅ 数据已从备份恢复');
            location.reload();
            
        } catch (error) {
            console.error('❌ 数据恢复失败:', error);
        }
    } else {
        console.error('❌ 未找到备份数据');
    }
}
```

---

## 🌐 网络连接问题

### 问题 6.1：网络连接超时

#### 症状描述
- AI解析长时间无响应
- 提示"网络连接超时"
- 功能间歇性失效

#### 诊断和解决

**网络连接测试**
```javascript
// 综合网络连接测试
async function comprehensiveNetworkTest() {
    const tests = [
        {
            name: '基础连接',
            url: 'https://www.google.com/favicon.ico',
            timeout: 5000
        },
        {
            name: 'Google API',
            url: 'https://generativelanguage.googleapis.com/',
            timeout: 10000
        },
        {
            name: 'MDAC网站',
            url: 'https://imigresen-online.imi.gov.my/',
            timeout: 15000
        }
    ];
    
    console.log('🌐 开始网络连接测试...');
    
    for (const test of tests) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), test.timeout);
            
            const response = await fetch(test.url, {
                signal: controller.signal,
                mode: 'no-cors'
            });
            
            clearTimeout(timeoutId);
            console.log(`✅ ${test.name}: 连接正常`);
            
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log(`⏰ ${test.name}: 连接超时 (>${test.timeout}ms)`);
            } else {
                console.log(`❌ ${test.name}: 连接失败`, error.message);
            }
        }
    }
}
```

**解决方案**：
1. **检查网络环境**
   - 确认基础网络连接正常
   - 测试其他网站是否能正常访问
   - 如在公司网络，联系IT确认代理设置

2. **调整超时设置**
   ```javascript
   // 增加API调用超时时间
   const extendedTimeout = 30000; // 30秒
   // 在设置中调整或重新配置
   ```

### 问题 6.2：代理和防火墙问题

#### 症状描述
- 在公司网络环境下无法使用
- 提示"连接被拒绝"
- 部分功能可用，AI功能不可用

#### 解决方案

**企业网络配置**
```
🏢 企业网络解决方案:
1. 联系IT部门将以下域名加入白名单:
   - generativelanguage.googleapis.com
   - imigresen-online.imi.gov.my
   - *.google.com

2. 确认防火墙允许Chrome扩展访问外部API

3. 如使用代理，确认代理配置正确
```

**代理检测脚本**
```javascript
// 检测代理设置
function detectProxySettings() {
    // 检查是否通过代理访问
    const isProxied = window.location.hostname !== window.location.host;
    
    console.log('代理检测结果:');
    console.log('当前主机:', window.location.host);
    console.log('是否使用代理:', isProxied ? '是' : '否');
    
    // 测试直连和代理访问
    Promise.all([
        fetch('https://httpbin.org/ip').then(r => r.json()),
        fetch('https://api.ipify.org?format=json').then(r => r.json())
    ]).then(results => {
        console.log('IP检测结果:', results);
    }).catch(error => {
        console.log('IP检测失败:', error);
    });
}
```

---

## 🔧 高级故障排除

### 系统级诊断

#### 完整系统检查脚本
```javascript
// 完整的系统诊断脚本
async function fullSystemDiagnostics() {
    console.log('🔍 开始完整系统诊断...\n');
    
    const results = {
        timestamp: new Date().toISOString(),
        environment: {},
        modules: {},
        storage: {},
        network: {},
        errors: []
    };
    
    try {
        // 1. 环境信息
        results.environment = {
            userAgent: navigator.userAgent,
            chromeVersion: /Chrome\/([0-9\.]+)/.exec(navigator.userAgent)?.[1],
            extensionId: chrome?.runtime?.id || 'N/A',
            url: window.location.href,
            memory: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
            } : 'N/A'
        };
        
        // 2. 模块状态
        const modules = ['mdacEventBus', 'mdacLogger', 'mdacStateManager', 'MDACMainController', 'FormFieldDetector'];
        modules.forEach(module => {
            results.modules[module] = window[module] ? 'loaded' : 'missing';
        });
        
        // 3. 存储功能
        try {
            await chrome.storage.local.set({ 'diagnostic_test': Date.now() });
            const test = await chrome.storage.local.get('diagnostic_test');
            await chrome.storage.local.remove('diagnostic_test');
            results.storage.status = 'working';
        } catch (error) {
            results.storage.status = 'failed';
            results.storage.error = error.message;
        }
        
        // 4. 网络连接
        try {
            await fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' });
            results.network.basic = 'connected';
            
            await fetch('https://generativelanguage.googleapis.com/', { mode: 'no-cors' });
            results.network.api = 'reachable';
        } catch (error) {
            results.network.basic = 'failed';
            results.network.error = error.message;
        }
        
        // 5. 错误收集
        if (window.mdacLogger) {
            results.errors = window.mdacLogger.getLogs('error').slice(-5);
        }
        
    } catch (error) {
        results.error = error.message;
    }
    
    // 输出诊断报告
    console.log('📋 系统诊断报告:');
    console.log(JSON.stringify(results, null, 2));
    
    // 生成问题摘要
    const issues = [];
    if (Object.values(results.modules).includes('missing')) {
        issues.push('模块加载不完整');
    }
    if (results.storage.status === 'failed') {
        issues.push('存储功能异常');
    }
    if (results.network.basic === 'failed') {
        issues.push('网络连接问题');
    }
    if (results.errors.length > 0) {
        issues.push(`发现${results.errors.length}个错误`);
    }
    
    if (issues.length === 0) {
        console.log('🎉 系统运行正常！');
    } else {
        console.log('⚠️ 发现问题:', issues.join(', '));
        console.log('💡 建议查看具体解决方案');
    }
    
    return results;
}
```

### 日志分析工具

```javascript
// 日志分析和问题识别
function analyzeErrorLogs() {
    if (!window.mdacLogger) {
        console.error('❌ 日志系统不可用');
        return;
    }
    
    const allLogs = window.mdacLogger.getLogs();
    const errorLogs = allLogs.filter(log => log.level === 'error');
    const warnLogs = allLogs.filter(log => log.level === 'warn');
    
    console.log('📊 日志分析报告:');
    console.log(`总日志数: ${allLogs.length}`);
    console.log(`错误数: ${errorLogs.length}`);
    console.log(`警告数: ${warnLogs.length}`);
    
    // 分析错误模式
    const errorPatterns = {};
    errorLogs.forEach(log => {
        const key = log.category + ':' + log.message.split(' ')[0];
        errorPatterns[key] = (errorPatterns[key] || 0) + 1;
    });
    
    console.log('🔍 错误模式分析:');
    Object.entries(errorPatterns)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .forEach(([pattern, count]) => {
            console.log(`${pattern}: ${count}次`);
        });
    
    // 最近错误
    console.log('🕒 最近错误:');
    errorLogs.slice(-3).forEach(log => {
        console.log(`[${log.timestamp}] ${log.category}: ${log.message}`);
    });
}
```

---

## 📞 技术支持联系

### 问题报告模板

当需要联系技术支持时，请使用以下模板：

```markdown
# MDAC扩展问题报告

## 基础信息
- 用户ID: [可选]
- 报告时间: [自动填入]
- 扩展版本: [从chrome://extensions/获取]
- Chrome版本: [从Chrome菜单→帮助→关于获取]
- 操作系统: [Windows/Mac/Linux及版本]

## 问题描述
[详细描述遇到的问题]

## 复现步骤
1. [第一步操作]
2. [第二步操作]
3. [问题出现]

## 预期结果
[描述期望的正常行为]

## 实际结果
[描述实际发生的异常行为]

## 错误信息
[如有错误提示，请完整复制]

## 环境信息
[运行 fullSystemDiagnostics() 并粘贴结果]

## 附加信息
[任何其他相关信息，如截图等]
```

### 联系方式
- 📧 技术支持邮箱: [<EMAIL>]
- 🐛 GitHub Issues: [仓库链接]
- 📖 文档中心: [文档网站]
- 💬 用户社区: [社区链接]

---

## 📚 相关资源

- [用户使用手册](./USER_MANUAL.md) - 基础使用指导
- [开发者指南](./DEVELOPER_GUIDE.md) - 开发环境配置
- [API接口文档](./API_REFERENCE.md) - 技术接口说明
- [部署维护手册](./DEPLOYMENT_GUIDE.md) - 部署和维护指导

---

**故障排除指南版本**: 1.0  
**最后更新**: 2025-07-15  
**维护团队**: MDAC技术支持团队