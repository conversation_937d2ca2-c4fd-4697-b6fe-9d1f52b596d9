/**
 * Chrome扩展错误修复验证脚本
 * 用于测试之前报告的6个错误是否已修复
 */

console.log('🧪 开始Chrome扩展错误修复验证...');

// 测试1: 检查background service worker错误修复
console.log('\n1️⃣ 测试Service Worker兼容性修复');
try {
    if (typeof globalThis.mdacBackground !== 'undefined') {
        console.log('✅ Background: globalThis.mdacBackground 可访问');
    } else {
        console.log('⚠️ Background: globalThis.mdacBackground 未找到');
    }
} catch (error) {
    console.log('❌ Background访问测试失败:', error.message);
}

// 测试2: 检查waitForContentScript和sendMessageWithRetry方法
console.log('\n2️⃣ 测试UI方法实现');
try {
    if (typeof window.mdacMainController !== 'undefined') {
        const controller = window.mdacMainController;
        
        if (typeof controller.waitForContentScript === 'function') {
            console.log('✅ UI: waitForContentScript 方法已实现');
        } else {
            console.log('❌ UI: waitForContentScript 方法缺失');
        }
        
        if (typeof controller.sendMessageWithRetry === 'function') {
            console.log('✅ UI: sendMessageWithRetry 方法已实现');
        } else {
            console.log('❌ UI: sendMessageWithRetry 方法缺失');
        }
    } else {
        console.log('⚠️ UI: mdacMainController 未初始化');
    }
} catch (error) {
    console.log('❌ UI方法测试失败:', error.message);
}

// 测试3: 检查Logger兼容性
console.log('\n3️⃣ 测试Logger兼容性修复');
try {
    if (typeof window.mdacLogger !== 'undefined') {
        const logger = window.mdacLogger;
        const requiredMethods = ['setLevel', 'startPerformance', 'endPerformance'];
        const missingMethods = requiredMethods.filter(method => typeof logger[method] !== 'function');
        
        if (missingMethods.length === 0) {
            console.log('✅ Logger: 所有兼容性方法已实现');
        } else {
            console.log('❌ Logger: 缺失方法:', missingMethods);
        }
    } else {
        console.log('⚠️ Logger: mdacLogger 未初始化');
    }
} catch (error) {
    console.log('❌ Logger测试失败:', error.message);
}

// 测试4: 检查Content Script通信
console.log('\n4️⃣ 测试Content Script通信');
try {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 发送测试消息
        chrome.runtime.sendMessage({ action: 'ping' }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('❌ Content Script: 通信失败 -', chrome.runtime.lastError.message);
            } else {
                console.log('✅ Content Script: 通信正常', response);
            }
        });
    } else {
        console.log('⚠️ Content Script: Chrome API不可用');
    }
} catch (error) {
    console.log('❌ Content Script测试失败:', error.message);
}

// 测试5: 检查CSP违规
console.log('\n5️⃣ 测试CSP合规性');
try {
    // 尝试创建内联脚本元素（应该失败或成功取决于CSP设置）
    const testScript = document.createElement('script');
    testScript.innerHTML = 'console.log("CSP测试");';
    
    // 如果能添加到DOM，说明CSP允许内联脚本
    document.head.appendChild(testScript);
    document.head.removeChild(testScript);
    console.log('✅ CSP: 内联脚本允许执行');
} catch (error) {
    console.log('⚠️ CSP: 内联脚本被阻止 (这可能是正常的):', error.message);
}

// 测试6: 检查UI元素绑定
console.log('\n6️⃣ 测试UI元素绑定');
try {
    const testButtons = ['updateToMDACBtn', 'parsePersonalBtn', 'parseTravelBtn'];
    let bindingErrors = 0;
    
    testButtons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            console.log(`✅ UI Element: ${buttonId} 找到`);
        } else {
            console.log(`❌ UI Element: ${buttonId} 未找到`);
            bindingErrors++;
        }
    });
    
    if (bindingErrors === 0) {
        console.log('✅ UI: 所有测试按钮元素已找到');
    } else {
        console.log(`⚠️ UI: ${bindingErrors} 个按钮元素未找到`);
    }
} catch (error) {
    console.log('❌ UI元素测试失败:', error.message);
}

console.log('\n🎯 Chrome扩展错误修复验证完成');
console.log('如果仍有错误，请查看具体的测试结果并进行相应修复。');
