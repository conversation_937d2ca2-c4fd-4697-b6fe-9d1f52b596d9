/**
 * 进度指示器
 * 显示表单填充的整体进度和详细状态
 */

class ProgressIndicator {
  constructor() {
    this.element = null;
    this.progressBar = null;
    this.statusList = null;
    this.statusItems = new Map();
    this.isVisible = false;
    this.createProgressIndicator();
  }

  /**
   * 创建进度指示器
   */
  createProgressIndicator() {
    // 创建样式
    this.createStyles();
    
    // 创建主元素
    this.element = document.createElement('div');
    this.element.className = 'mdac-progress-indicator';
    
    // 创建内容
    this.element.innerHTML = `
      <div class="mdac-progress-header">
        <h3>表单填充进度</h3>
        <button class="mdac-progress-close">×</button>
      </div>
      <div class="mdac-progress-content">
        <div class="mdac-progress-bar-container">
          <div class="mdac-progress-bar"></div>
          <div class="mdac-progress-text">0%</div>
        </div>
        <div class="mdac-progress-status">
          <h4>字段状态</h4>
          <ul class="mdac-status-list"></ul>
        </div>
      </div>
    `;
    
    // 获取引用
    this.progressBar = this.element.querySelector('.mdac-progress-bar');
    this.progressText = this.element.querySelector('.mdac-progress-text');
    this.statusList = this.element.querySelector('.mdac-status-list');
    
    // 添加事件监听器
    const closeButton = this.element.querySelector('.mdac-progress-close');
    closeButton.addEventListener('click', () => this.hide());
    
    // 添加到页面
    document.body.appendChild(this.element);
  }

  /**
   * 创建样式
   */
  createStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .mdac-progress-indicator {
        position: fixed;
        top: 20px;
        left: 20px;
        width: 300px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transform: translateX(-110%);
        transition: transform 0.3s ease;
      }
      
      .mdac-progress-indicator.visible {
        transform: translateX(0);
      }
      
      .mdac-progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #007bff;
        color: #fff;
        border-radius: 8px 8px 0 0;
      }
      
      .mdac-progress-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
      
      .mdac-progress-close {
        background: none;
        border: none;
        color: #fff;
        font-size: 20px;
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
      }
      
      .mdac-progress-close:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
      
      .mdac-progress-content {
        padding: 15px;
      }
      
      .mdac-progress-bar-container {
        position: relative;
        height: 20px;
        background: #f0f0f0;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 15px;
      }
      
      .mdac-progress-bar {
        height: 100%;
        background: #4CAF50;
        width: 0;
        transition: width 0.3s ease;
      }
      
      .mdac-progress-text {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        font-size: 12px;
        font-weight: 600;
      }
      
      .mdac-progress-status {
        margin-top: 15px;
      }
      
      .mdac-progress-status h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #333;
      }
      
      .mdac-status-list {
        list-style: none;
        padding: 0;
        margin: 0;
        max-height: 200px;
        overflow-y: auto;
      }
      
      .mdac-status-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
      }
      
      .mdac-status-item:last-child {
        border-bottom: none;
      }
      
      .mdac-status-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 12px;
        color: white;
      }
      
      .mdac-status-icon.success {
        background-color: #4CAF50;
      }
      
      .mdac-status-icon.error {
        background-color: #F44336;
      }
      
      .mdac-status-icon.warning {
        background-color: #FFC107;
      }
      
      .mdac-status-icon.loading {
        background-color: #2196F3;
        animation: mdac-spin 1s linear infinite;
      }
      
      @keyframes mdac-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .mdac-status-text {
        flex: 1;
        font-size: 13px;
      }
      
      .mdac-status-message {
        font-size: 11px;
        color: #666;
        margin-top: 2px;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 显示进度指示器
   */
  show() {
    this.element.classList.add('visible');
    this.isVisible = true;
  }

  /**
   * 隐藏进度指示器
   */
  hide() {
    this.element.classList.remove('visible');
    this.isVisible = false;
  }

  /**
   * 更新进度
   */
  updateProgress(current, total, message = '') {
    const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
    
    this.progressBar.style.width = `${percentage}%`;
    this.progressText.textContent = `${percentage}% (${current}/${total})`;
    
    if (message) {
      this.addStatus('progress', 'info', message);
    }
  }

  /**
   * 添加字段状态
   */
  addFieldStatus(fieldId, status, message = '') {
    const statusText = this.getFieldName(fieldId);
    
    let icon = '';
    let statusClass = '';
    
    switch (status) {
      case 'success':
        icon = '✓';
        statusClass = 'success';
        break;
        
      case 'error':
        icon = '✗';
        statusClass = 'error';
        break;
        
      case 'warning':
        icon = '!';
        statusClass = 'warning';
        break;
        
      case 'loading':
        icon = '↻';
        statusClass = 'loading';
        break;
    }
    
    // 检查是否已存在
    if (this.statusItems.has(fieldId)) {
      const item = this.statusItems.get(fieldId);
      item.icon.className = `mdac-status-icon ${statusClass}`;
      item.icon.innerHTML = icon;
      item.text.textContent = statusText;
      
      if (message) {
        if (!item.message) {
          item.message = document.createElement('div');
          item.message.className = 'mdac-status-message';
          item.content.appendChild(item.message);
        }
        item.message.textContent = message;
      } else if (item.message) {
        item.message.remove();
        item.message = null;
      }
    } else {
      // 创建新状态项
      const item = document.createElement('li');
      item.className = 'mdac-status-item';
      
      const iconElement = document.createElement('div');
      iconElement.className = `mdac-status-icon ${statusClass}`;
      iconElement.innerHTML = icon;
      
      const content = document.createElement('div');
      content.className = 'mdac-status-content';
      
      const textElement = document.createElement('div');
      textElement.className = 'mdac-status-text';
      textElement.textContent = statusText;
      
      content.appendChild(textElement);
      
      let messageElement = null;
      if (message) {
        messageElement = document.createElement('div');
        messageElement.className = 'mdac-status-message';
        messageElement.textContent = message;
        content.appendChild(messageElement);
      }
      
      item.appendChild(iconElement);
      item.appendChild(content);
      
      this.statusList.appendChild(item);
      
      this.statusItems.set(fieldId, {
        element: item,
        icon: iconElement,
        content,
        text: textElement,
        message: messageElement
      });
    }
  }

  /**
   * 添加一般状态消息
   */
  addStatus(id, type, message) {
    this.addFieldStatus(id, type, message);
  }

  /**
   * 清除状态列表
   */
  clearStatus() {
    this.statusList.innerHTML = '';
    this.statusItems.clear();
  }

  /**
   * 获取字段显示名称
   */
  getFieldName(fieldId) {
    const fieldNames = {
      fullName: '姓名',
      passportNumber: '护照号码',
      dob: '出生日期',
      nationality: '国籍',
      sex: '性别',
      passExpDte: '护照到期日期',
      email: '电子邮箱',
      region: '国家代码',
      mobile: '手机号码',
      arrDt: '到达日期',
      depDt: '离开日期',
      embark: '最后登机港口',
      vesselNm: '航班号',
      trvlMode: '旅行方式',
      progress: '进度'
    };
    
    return fieldNames[fieldId] || fieldId;
  }
}

// 导出类
window.ProgressIndicator = ProgressIndicator;
