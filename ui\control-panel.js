/**
 * MDAC控制面板
 * 提供用户界面控制和操作
 */

class ControlPanel {
  constructor() {
    this.element = null;
    this.isVisible = false;
    this.isExpanded = false;
    this.createControlPanel();
  }

  /**
   * 创建控制面板
   */
  createControlPanel() {
    // 创建样式
    this.createStyles();
    
    // 创建主元素
    this.element = document.createElement('div');
    this.element.className = 'mdac-control-panel';
    
    // 创建内容
    this.element.innerHTML = `
      <div class="mdac-control-toggle">
        <span class="mdac-control-icon">🤖</span>
        <span class="mdac-control-text">MDAC AI</span>
      </div>
      <div class="mdac-control-content">
        <div class="mdac-control-header">
          <h3>MDAC AI 控制面板</h3>
          <button class="mdac-control-close">×</button>
        </div>
        <div class="mdac-control-body">
          <div class="mdac-control-section">
            <h4>智能填充</h4>
            <div class="mdac-control-actions">
              <button class="mdac-btn mdac-btn-primary" id="mdac-fill-all">
                <span class="mdac-btn-icon">📝</span>
                <span class="mdac-btn-text">填充所有字段</span>
              </button>
              <button class="mdac-btn" id="mdac-show-progress">
                <span class="mdac-btn-icon">📊</span>
                <span class="mdac-btn-text">显示进度</span>
              </button>
            </div>
          </div>
          
          <div class="mdac-control-section">
            <h4>表单验证</h4>
            <div class="mdac-control-actions">
              <button class="mdac-btn" id="mdac-validate-form">
                <span class="mdac-btn-icon">✓</span>
                <span class="mdac-btn-text">验证表单</span>
              </button>
              <button class="mdac-btn" id="mdac-optimize-form">
                <span class="mdac-btn-icon">🔄</span>
                <span class="mdac-btn-text">优化表单</span>
              </button>
            </div>
          </div>
          
          <div class="mdac-control-section">
            <h4>AI助手</h4>
            <div class="mdac-control-actions">
              <button class="mdac-btn" id="mdac-ai-help">
                <span class="mdac-btn-icon">🤖</span>
                <span class="mdac-btn-text">AI帮助</span>
              </button>
              <button class="mdac-btn" id="mdac-ai-translate">
                <span class="mdac-btn-icon">🌐</span>
                <span class="mdac-btn-text">翻译</span>
              </button>
            </div>
          </div>
          
          <div class="mdac-control-section">
            <h4>设置</h4>
            <div class="mdac-control-settings">
              <div class="mdac-setting-item">
                <label for="mdac-auto-fill">自动填充</label>
                <div class="mdac-toggle">
                  <input type="checkbox" id="mdac-auto-fill" checked>
                  <span class="mdac-toggle-slider"></span>
                </div>
              </div>
              <div class="mdac-setting-item">
                <label for="mdac-show-indicators">显示状态指示器</label>
                <div class="mdac-toggle">
                  <input type="checkbox" id="mdac-show-indicators" checked>
                  <span class="mdac-toggle-slider"></span>
                </div>
              </div>
              <div class="mdac-setting-item">
                <label for="mdac-ai-validation">AI验证</label>
                <div class="mdac-toggle">
                  <input type="checkbox" id="mdac-ai-validation" checked>
                  <span class="mdac-toggle-slider"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mdac-control-footer">
          <span class="mdac-version">MDAC AI v1.0.0</span>
          <a href="#" class="mdac-help-link" id="mdac-help">帮助</a>
        </div>
      </div>
    `;
    
    // 添加到页面
    document.body.appendChild(this.element);
    
    // 添加事件监听器
    this.addEventListeners();
  }

  /**
   * 创建样式
   */
  createStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .mdac-control-panel {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 10001;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .mdac-control-toggle {
        display: flex;
        align-items: center;
        background: #007bff;
        color: #fff;
        padding: 10px 15px;
        border-radius: 30px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .mdac-control-toggle:hover {
        background: #0069d9;
        transform: translateY(-2px);
      }
      
      .mdac-control-icon {
        font-size: 20px;
        margin-right: 8px;
      }
      
      .mdac-control-text {
        font-weight: 600;
        font-size: 14px;
      }
      
      .mdac-control-content {
        position: absolute;
        bottom: 60px;
        right: 0;
        width: 320px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        display: none;
        overflow: hidden;
      }
      
      .mdac-control-panel.expanded .mdac-control-content {
        display: block;
      }
      
      .mdac-control-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #007bff;
        color: #fff;
      }
      
      .mdac-control-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
      
      .mdac-control-close {
        background: none;
        border: none;
        color: #fff;
        font-size: 20px;
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
      }
      
      .mdac-control-close:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
      
      .mdac-control-body {
        padding: 15px;
      }
      
      .mdac-control-section {
        margin-bottom: 20px;
      }
      
      .mdac-control-section:last-child {
        margin-bottom: 0;
      }
      
      .mdac-control-section h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #333;
        font-weight: 600;
      }
      
      .mdac-control-actions {
        display: flex;
        gap: 10px;
      }
      
      .mdac-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #f8f9fa;
        color: #333;
        font-size: 13px;
        cursor: pointer;
        transition: all 0.2s;
        flex: 1;
      }
      
      .mdac-btn:hover {
        background: #e9ecef;
      }
      
      .mdac-btn-primary {
        background: #007bff;
        color: #fff;
        border-color: #007bff;
      }
      
      .mdac-btn-primary:hover {
        background: #0069d9;
      }
      
      .mdac-btn-icon {
        margin-right: 5px;
        font-size: 14px;
      }
      
      .mdac-control-settings {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      
      .mdac-setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .mdac-setting-item label {
        font-size: 13px;
        color: #333;
      }
      
      .mdac-toggle {
        position: relative;
        display: inline-block;
        width: 40px;
        height: 20px;
      }
      
      .mdac-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
      }
      
      .mdac-toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
      }
      
      .mdac-toggle-slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
      }
      
      input:checked + .mdac-toggle-slider {
        background-color: #007bff;
      }
      
      input:checked + .mdac-toggle-slider:before {
        transform: translateX(20px);
      }
      
      .mdac-control-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: #f8f9fa;
        border-top: 1px solid #eee;
      }
      
      .mdac-version {
        font-size: 11px;
        color: #666;
      }
      
      .mdac-help-link {
        font-size: 12px;
        color: #007bff;
        text-decoration: none;
      }
      
      .mdac-help-link:hover {
        text-decoration: underline;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 添加事件监听器
   */
  addEventListeners() {
    // 切换控制面板
    const toggle = this.element.querySelector('.mdac-control-toggle');
    toggle.addEventListener('click', () => this.togglePanel());
    
    // 关闭按钮
    const closeButton = this.element.querySelector('.mdac-control-close');
    closeButton.addEventListener('click', () => this.hidePanel());
    
    // 填充所有字段按钮
    const fillAllButton = this.element.querySelector('#mdac-fill-all');
    fillAllButton.addEventListener('click', () => this.handleFillAll());
    
    // 显示进度按钮
    const showProgressButton = this.element.querySelector('#mdac-show-progress');
    showProgressButton.addEventListener('click', () => this.handleShowProgress());
    
    // 验证表单按钮
    const validateFormButton = this.element.querySelector('#mdac-validate-form');
    validateFormButton.addEventListener('click', () => this.handleValidateForm());
    
    // 优化表单按钮
    const optimizeFormButton = this.element.querySelector('#mdac-optimize-form');
    optimizeFormButton.addEventListener('click', () => this.handleOptimizeForm());
    
    // AI帮助按钮
    const aiHelpButton = this.element.querySelector('#mdac-ai-help');
    aiHelpButton.addEventListener('click', () => this.handleAIHelp());
    
    // AI翻译按钮
    const aiTranslateButton = this.element.querySelector('#mdac-ai-translate');
    aiTranslateButton.addEventListener('click', () => this.handleAITranslate());
    
    // 帮助链接
    const helpLink = this.element.querySelector('#mdac-help');
    helpLink.addEventListener('click', (e) => {
      e.preventDefault();
      this.handleHelp();
    });
    
    // 设置切换
    const autoFillToggle = this.element.querySelector('#mdac-auto-fill');
    autoFillToggle.addEventListener('change', (e) => {
      this.handleSettingChange('autoFill', e.target.checked);
    });
    
    const showIndicatorsToggle = this.element.querySelector('#mdac-show-indicators');
    showIndicatorsToggle.addEventListener('change', (e) => {
      this.handleSettingChange('showIndicators', e.target.checked);
    });
    
    const aiValidationToggle = this.element.querySelector('#mdac-ai-validation');
    aiValidationToggle.addEventListener('change', (e) => {
      this.handleSettingChange('aiValidation', e.target.checked);
    });
  }

  /**
   * 显示控制面板
   */
  showPanel() {
    this.element.classList.add('expanded');
    this.isExpanded = true;
  }

  /**
   * 隐藏控制面板
   */
  hidePanel() {
    this.element.classList.remove('expanded');
    this.isExpanded = false;
  }

  /**
   * 切换控制面板
   */
  togglePanel() {
    if (this.isExpanded) {
      this.hidePanel();
    } else {
      this.showPanel();
    }
  }

  /**
   * 处理填充所有字段
   */
  handleFillAll() {
    this.hidePanel();
    
    // 检查是否有ContentScript实例
    if (window.contentScript) {
      window.contentScript.fillAllFields();
    } else {
      this.showNotification('无法找到ContentScript实例', 'error');
    }
  }

  /**
   * 处理显示进度
   */
  handleShowProgress() {
    // 检查是否有进度指示器
    if (window.mdacProgressIndicator) {
      window.mdacProgressIndicator.show();
    } else {
      this.showNotification('无法找到进度指示器', 'error');
    }
  }

  /**
   * 处理验证表单
   */
  handleValidateForm() {
    this.hidePanel();
    
    // 检查是否有ContentScript实例
    if (window.contentScript) {
      window.contentScript.validateForm();
    } else {
      this.showNotification('无法找到ContentScript实例', 'error');
    }
  }

  /**
   * 处理优化表单
   */
  handleOptimizeForm() {
    this.hidePanel();
    
    // 检查是否有ContentScript实例
    if (window.contentScript) {
      window.contentScript.optimizeForm();
    } else {
      this.showNotification('无法找到ContentScript实例', 'error');
    }
  }

  /**
   * 处理AI帮助
   */
  handleAIHelp() {
    this.hidePanel();
    this.showNotification('AI助手功能即将推出', 'info');
  }

  /**
   * 处理AI翻译
   */
  handleAITranslate() {
    this.hidePanel();
    this.showNotification('AI翻译功能即将推出', 'info');
  }

  /**
   * 处理帮助
   */
  handleHelp() {
    this.hidePanel();
    this.showNotification('帮助文档即将推出', 'info');
  }

  /**
   * 处理设置变更
   */
  handleSettingChange(setting, value) {
    console.log(`设置 ${setting} 已更改为 ${value}`);
    
    // 保存设置
    if (window.contentScript) {
      window.contentScript.updateSetting(setting, value);
    }
    
    // 应用设置
    switch (setting) {
      case 'showIndicators':
        if (window.mdacFieldStatusIndicator) {
          // 遍历所有指示器
          for (const [fieldId, item] of window.mdacFieldStatusIndicator.indicators) {
            if (value) {
              item.indicator.classList.add('visible');
            } else {
              item.indicator.classList.remove('visible');
            }
          }
        }
        break;
    }
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    if (window.mdacUIManager) {
      window.mdacUIManager.showNotification(message, type);
    } else {
      alert(message);
    }
  }
}

// 导出类
window.ControlPanel = ControlPanel;
