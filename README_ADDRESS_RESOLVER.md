# 🔍 智能地址解析器

## 概述

MDAC智能地址解析器是一个三层智能地址解析系统，能够从自然语言文本中提取完整的马来西亚地址信息，包括地址、邮编、州属和城市。

## 功能特性

### 🎯 三层解析架构
1. **本地快速匹配** - 使用邮编、地标和关键词进行快速匹配
2. **AI智能解析** - 利用Gemini AI进行自然语言处理
3. **Google Maps验证** - 通过Google Maps API验证和补全地址信息

### 📊 支持的解析类型
- 邮编识别（5位数字）
- 地标匹配（KLCC、云顶高原等）
- 城市名称匹配
- 州属识别
- 地区/区域匹配
- 模糊匹配和别名处理

### 🗄️ 数据库支持
- 完整的马来西亚地址数据库
- 主要城市的邮编范围
- 热门地标和旅游景点
- 城市别名和变体名称

## 使用方法

### 在扩展中使用

1. **打开MDAC AI助手侧边栏**
2. **在旅行信息输入框中输入包含地址的文本**
3. **点击"智能地址解析"按钮**
4. **系统将自动识别并填充地址相关字段**

### 测试地址解析功能

1. **打开测试页面**
   ```
   file://C:\Users\<USER>\Downloads\入境卡\test\address-resolver-test.html
   ```

2. **运行测试用例**
   - 手动测试：输入自定义文本
   - 预设测试：点击预设的测试用例
   - 自动测试：批量运行所有测试用例

## 测试用例示例

### 1. 地标 + 邮编
```
输入：我住在吉隆坡KLCC附近，邮编50088
预期：地址解析出KLCC地标信息和50088邮编
```

### 2. 完整地址
```
输入：酒店地址：7 Jalan Legoland, Bandar Medini Iskandar, 79100 Nusajaya, Johor
预期：解析出完整的柔佛州新山地址信息
```

### 3. 地标名称
```
输入：我要去云顶高原度假，住在那里的酒店
预期：识别云顶高原地标，返回彭亨州相关信息
```

### 4. 城市+地标
```
输入：槟城乔治市的酒店，靠近光大
预期：识别槟城州乔治市
```

### 5. 邮编+城市
```
输入：40100 Shah Alam, Selangor
预期：识别雪兰莪州沙阿南市
```

## 技术实现

### 文件结构
```
├── modules/
│   ├── address-resolver.js          # 主要解析器类
│   └── logger.js                   # 日志模块
├── config/
│   ├── malaysia-address-database.json  # 地址数据库
│   └── ai-config.js                # AI配置
├── ui/
│   ├── ui-sidepanel.html           # 界面文件
│   └── ui-sidepanel-main.js        # 主控制器
└── test/
    └── address-resolver-test.html   # 测试页面
```

### 核心方法

#### `resolveAddress(inputText)`
主要解析方法，按顺序执行三层解析：
1. 尝试本地匹配
2. 如果置信度不够，调用AI解析
3. 使用Google Maps验证结果

#### `tryLocalMatch(text)`
本地匹配方法，支持：
- 邮编匹配（优先级最高）
- 地标匹配
- 关键词模糊匹配

#### `aiParseAddress(text)`
AI解析方法，使用Gemini AI：
- 自然语言处理
- 结构化数据提取
- 置信度评估

#### `verifyWithGoogleMaps(aiResult)`
Google Maps验证：
- 地址地理编码
- 坐标验证
- 格式化地址获取

## 配置要求

### 必需的配置
1. **AI配置** - Gemini API密钥
2. **地址数据库** - 马来西亚地址数据
3. **Google Maps API** - 地理编码服务

### 可选配置
1. **置信度阈值** - 各层解析的置信度要求
2. **超时设置** - 各层解析的超时时间
3. **错误处理** - 失败后的降级策略

## 返回数据格式

### 成功响应
```json
{
  "success": true,
  "data": {
    "address": "完整地址",
    "postcode": "邮编",
    "state": "州属代码",
    "city": "城市代码",
    "coordinates": {
      "lat": "纬度",
      "lng": "经度"
    },
    "confidence": 0.95,
    "verified": true
  },
  "metadata": {
    "method": "解析方法",
    "duration": "耗时ms",
    "timestamp": "时间戳"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "metadata": {
    "method": "尝试的方法",
    "duration": "耗时ms",
    "timestamp": "时间戳"
  }
}
```

## 性能优化

### 缓存策略
- 本地匹配结果缓存
- AI解析结果缓存
- Google Maps结果缓存

### 超时控制
- 本地匹配：500ms
- AI解析：5000ms
- Google Maps：3000ms

### 降级策略
- 本地匹配失败 → AI解析
- AI解析失败 → Google Maps直接查询
- 所有方法失败 → 返回错误

## 故障排除

### 常见问题

1. **AddressResolver 类未加载**
   - 检查 `modules/address-resolver.js` 文件
   - 确认manifest.json中的web_accessible_resources配置

2. **AI配置未加载**
   - 检查 `config/ai-config.js` 文件
   - 确认API密钥配置正确

3. **地址数据库加载失败**
   - 检查 `config/malaysia-address-database.json` 文件
   - 确认文件格式正确

4. **Google Maps API不可用**
   - 检查API密钥
   - 确认网络连接

### 调试方法

1. **打开开发者工具**
2. **查看控制台日志**
3. **检查网络请求**
4. **运行测试页面**

## 更新日志

### v1.0.0 (2025-01-15)
- 初始版本发布
- 实现三层解析架构
- 支持马来西亚地址数据库
- 集成Gemini AI和Google Maps
- 添加完整的测试套件

## 许可证

本项目为MDAC AI智能分析工具的一部分，仅供内部使用。