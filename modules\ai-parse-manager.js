/**
 * AI解析管理器模块
 * 负责处理AI解析功能的所有逻辑
 * 
 * <AUTHOR> AI Team
 * @version 3.1.0
 */

class AIParseManager {
    constructor(memoryManager, errorHandler, messageSystem, loadingManager) {
        this.memoryManager = memoryManager;
        this.errorHandler = errorHandler;
        this.messageSystem = messageSystem;
        this.loadingManager = loadingManager;
        
        // AI配置
        this.aiConfig = null;
        
        // 解析缓存
        this.parseCache = new Map();
        this.maxCacheSize = 50;
        
        // 解析统计
        this.stats = {
            totalParses: 0,
            successfulParses: 0,
            failedParses: 0,
            cacheHits: 0
        };
    }

    /**
     * 初始化AI配置
     * @param {Object} aiConfig - AI配置
     */
    initialize(aiConfig) {
        this.aiConfig = aiConfig;
        console.log('✅ [AIParseManager] 初始化完成');
    }

    /**
     * 解析个人信息
     * @param {string} inputText - 输入文本
     * @param {Function} fillFieldsCallback - 填充字段回调
     * @param {Function} parseTextCallback - 文本解析回调
     * @returns {Promise<boolean>} 解析是否成功
     */
    async parsePersonalInfo(inputText, fillFieldsCallback, parseTextCallback) {
        if (!inputText || !inputText.trim()) {
            this.messageSystem.show('warning', '请输入个人信息内容');
            return false;
        }

        // 显示加载状态
        const loader = this.loadingManager.show('parsePersonal', 'AI正在解析个人信息...', {
            progress: true
        });

        try {
            this.stats.totalParses++;
            
            // 更新进度
            loader.updateProgress(20);
            loader.updateText('正在连接AI服务...');
            
            // 检查缓存
            const cacheKey = this.generateCacheKey('personal', inputText);
            if (this.parseCache.has(cacheKey)) {
                this.stats.cacheHits++;
                const cachedResult = this.parseCache.get(cacheKey);
                
                loader.updateProgress(70);
                loader.updateText('使用缓存结果...');
                
                fillFieldsCallback(cachedResult);
                
                loader.updateProgress(100);
                setTimeout(() => {
                    loader.hide();
                    this.messageSystem.show('success', '个人信息解析完成（缓存）');
                }, 500);
                
                this.stats.successfulParses++;
                return true;
            }
            
            // 更新进度
            loader.updateProgress(40);
            
            // 调用AI解析
            const parsedData = await this.callGeminiAI(inputText, 'personal');
            
            // 更新进度
            loader.updateProgress(70);
            loader.updateText('正在填充表单字段...');

            if (parsedData && Object.keys(parsedData).length > 0) {
                // 缓存结果
                this.cacheParseResult(cacheKey, parsedData);
                
                // 填充表单字段
                fillFieldsCallback(parsedData);
                
                // 更新进度
                loader.updateProgress(100);
                loader.updateText('解析完成！');
                
                setTimeout(() => {
                    loader.hide();
                    this.messageSystem.show('success', '个人信息AI解析完成');
                }, 500);
                
                this.stats.successfulParses++;
                return true;
            } else {
                // AI解析失败，使用后备解析
                return await this.handleFallbackParse(
                    loader, inputText, 'personal', 
                    fillFieldsCallback, parseTextCallback,
                    'AI服务暂不可用'
                );
            }

        } catch (error) {
            return await this.handleParseError(
                error, loader, inputText, 'personal',
                fillFieldsCallback, parseTextCallback
            );
        }
    }

    /**
     * 解析旅行信息
     * @param {string} inputText - 输入文本
     * @param {Function} fillFieldsCallback - 填充字段回调
     * @param {Function} parseTextCallback - 文本解析回调
     * @returns {Promise<boolean>} 解析是否成功
     */
    async parseTravelInfo(inputText, fillFieldsCallback, parseTextCallback) {
        if (!inputText || !inputText.trim()) {
            this.messageSystem.show('warning', '请输入旅行信息内容');
            return false;
        }

        // 显示加载状态
        const loader = this.loadingManager.show('parseTravel', 'AI正在解析旅行信息...', {
            progress: true
        });

        try {
            this.stats.totalParses++;
            
            // 更新进度
            loader.updateProgress(20);
            loader.updateText('正在连接AI服务...');
            
            // 检查缓存
            const cacheKey = this.generateCacheKey('travel', inputText);
            if (this.parseCache.has(cacheKey)) {
                this.stats.cacheHits++;
                const cachedResult = this.parseCache.get(cacheKey);
                
                loader.updateProgress(70);
                loader.updateText('使用缓存结果...');
                
                fillFieldsCallback(cachedResult);
                
                loader.updateProgress(100);
                setTimeout(() => {
                    loader.hide();
                    this.messageSystem.show('success', '旅行信息解析完成（缓存）');
                }, 500);
                
                this.stats.successfulParses++;
                return true;
            }
            
            // 更新进度
            loader.updateProgress(40);
            
            // 调用AI解析
            const parsedData = await this.callGeminiAI(inputText, 'travel');
            
            // 更新进度
            loader.updateProgress(70);
            loader.updateText('正在填充表单字段...');

            if (parsedData && Object.keys(parsedData).length > 0) {
                // 缓存结果
                this.cacheParseResult(cacheKey, parsedData);
                
                // 填充表单字段
                fillFieldsCallback(parsedData);
                
                // 更新进度
                loader.updateProgress(100);
                loader.updateText('解析完成！');
                
                setTimeout(() => {
                    loader.hide();
                    this.messageSystem.show('success', '旅行信息AI解析完成');
                }, 500);
                
                this.stats.successfulParses++;
                return true;
            } else {
                // AI解析失败，使用后备解析
                return await this.handleFallbackParse(
                    loader, inputText, 'travel',
                    fillFieldsCallback, parseTextCallback,
                    'AI服务暂不可用'
                );
            }

        } catch (error) {
            return await this.handleParseError(
                error, loader, inputText, 'travel',
                fillFieldsCallback, parseTextCallback
            );
        }
    }

    /**
     * 处理后备解析
     * @param {Object} loader - 加载器
     * @param {string} inputText - 输入文本
     * @param {string} type - 解析类型
     * @param {Function} fillFieldsCallback - 填充字段回调
     * @param {Function} parseTextCallback - 文本解析回调
     * @param {string} reason - 后备原因
     * @returns {Promise<boolean>} 解析是否成功
     */
    async handleFallbackParse(loader, inputText, type, fillFieldsCallback, parseTextCallback, reason) {
        try {
            loader.updateText('使用基础解析...');
            
            const fallbackLoader = this.loadingManager.show(`fallback${type}`, '使用基础解析...', {
                timeout: 5000
            });
            
            const fallbackData = parseTextCallback(inputText);
            fillFieldsCallback(fallbackData);
            
            fallbackLoader.hide();
            loader.hide();
            
            this.messageSystem.show('warning', `使用基础解析（${reason}）`);
            this.stats.successfulParses++;
            return true;
        } catch (fallbackError) {
            loader.hide();
            this.messageSystem.show('error', `解析失败：${reason}`);
            this.stats.failedParses++;
            return false;
        }
    }

    /**
     * 处理解析错误
     * @param {Error} error - 错误对象
     * @param {Object} loader - 加载器
     * @param {string} inputText - 输入文本
     * @param {string} type - 解析类型
     * @param {Function} fillFieldsCallback - 填充字段回调
     * @param {Function} parseTextCallback - 文本解析回调
     * @returns {Promise<boolean>} 解析是否成功
     */
    async handleParseError(error, loader, inputText, type, fillFieldsCallback, parseTextCallback) {
        console.error(`${type}信息解析失败:`, error);
        
        // 隐藏加载器
        loader.hide();
        this.stats.failedParses++;

        // 详细的错误分析和提示
        let errorMessage = '解析失败：';
        if (error.message.includes('network') || error.message.includes('fetch')) {
            errorMessage += '网络连接问题，请检查网络连接';
        } else if (error.message.includes('API') || error.message.includes('key')) {
            errorMessage += 'AI服务配置问题，请检查API密钥';
        } else if (error.message.includes('quota') || error.message.includes('limit')) {
            errorMessage += 'AI服务配额已用完，请稍后再试';
        } else if (error.message.includes('format') || error.message.includes('parse')) {
            errorMessage += '输入内容格式不支持，请尝试更清晰的描述';
        } else {
            errorMessage += error.message || '未知错误';
        }

        // 后备解析
        return await this.handleFallbackParse(
            { hide: () => {}, updateText: () => {} }, // 空加载器
            inputText, type, fillFieldsCallback, parseTextCallback, errorMessage
        );
    }

    /**
     * 调用Gemini AI进行解析
     * @param {string} inputText - 输入文本
     * @param {string} type - 解析类型
     * @returns {Promise<Object>} 解析结果
     */
    async callGeminiAI(inputText, type) {
        // 这里应该是实际的Gemini AI调用逻辑
        // 目前返回模拟数据
        console.log(`🤖 [AIParseManager] 调用Gemini AI解析${type}信息:`, inputText);
        
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟解析结果
        if (type === 'personal') {
            return {
                fullName: 'John Doe',
                passportNo: '*********',
                nationality: 'MY',
                dob: '01/01/1990'
            };
        } else if (type === 'travel') {
            return {
                arrivalDate: '15/01/2025',
                departureDate: '25/01/2025',
                flightNo: 'MH123'
            };
        }
        
        return null;
    }

    /**
     * 生成缓存键
     * @param {string} type - 解析类型
     * @param {string} inputText - 输入文本
     * @returns {string} 缓存键
     */
    generateCacheKey(type, inputText) {
        const hash = this.simpleHash(inputText);
        return `${type}_${hash}`;
    }

    /**
     * 简单哈希函数
     * @param {string} str - 字符串
     * @returns {string} 哈希值
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * 缓存解析结果
     * @param {string} key - 缓存键
     * @param {Object} result - 解析结果
     */
    cacheParseResult(key, result) {
        // 如果缓存已满，删除最旧的条目
        if (this.parseCache.size >= this.maxCacheSize) {
            const firstKey = this.parseCache.keys().next().value;
            this.parseCache.delete(firstKey);
        }
        
        this.parseCache.set(key, result);
    }

    /**
     * 获取解析统计
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.parseCache.size,
            successRate: this.stats.totalParses > 0 ? 
                (this.stats.successfulParses / this.stats.totalParses * 100).toFixed(1) + '%' : '0%'
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.parseCache.clear();
        console.log('✅ [AIParseManager] 资源清理完成');
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIParseManager;
} else {
    window.AIParseManager = AIParseManager;
}
