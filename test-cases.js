/**
 * MDAC AI Chrome扩展测试用例
 * 包含单元测试、集成测试和端到端测试
 */

// 日期解析器测试
describe('日期解析器测试', () => {
  let dateParser;
  
  beforeAll(() => {
    console.log('🔧 设置日期解析器测试环境...');
    
    // 检查日期解析器是否已加载
    if (!window.dateParser) {
      console.log('📅 创建日期解析器实例...');
      
      // 创建简化的日期解析器
      class SimpleDateParser {
        smartParse(dateStr) {
          if (!dateStr) return '';
          
          // 相对日期处理
          const now = new Date();
          
          if (dateStr === 'today' || dateStr === '今天') {
            return this.formatForMDAC(now);
          }
          
          if (dateStr === 'tomorrow' || dateStr === '明天') {
            const tomorrow = new Date(now);
            tomorrow.setDate(now.getDate() + 1);
            return this.formatForMDAC(tomorrow);
          }
          
          if (dateStr === 'yesterday' || dateStr === '昨天') {
            const yesterday = new Date(now);
            yesterday.setDate(now.getDate() - 1);
            return this.formatForMDAC(yesterday);
          }
          
          // 处理 "7天后" 格式
          const daysAfterMatch = dateStr.match(/^(\d+)天后$/);
          if (daysAfterMatch) {
            const days = parseInt(daysAfterMatch[1]);
            const futureDate = new Date(now);
            futureDate.setDate(now.getDate() + days);
            return this.formatForMDAC(futureDate);
          }
          
          // 处理 "3天前" 格式
          const daysAgoMatch = dateStr.match(/^(\d+)天前$/);
          if (daysAgoMatch) {
            const days = parseInt(daysAgoMatch[1]);
            const pastDate = new Date(now);
            pastDate.setDate(now.getDate() - days);
            return this.formatForMDAC(pastDate);
          }
          
          // 处理 "in 7 days" 格式
          const inDaysMatch = dateStr.match(/^in\s+(\d+)\s+days?$/i);
          if (inDaysMatch) {
            const days = parseInt(inDaysMatch[1]);
            const futureDate = new Date(now);
            futureDate.setDate(now.getDate() + days);
            return this.formatForMDAC(futureDate);
          }
          
          // 处理 "2025年8月1日" 格式
          const chineseDateMatch = dateStr.match(/^(\d{4})年(\d{1,2})月(\d{1,2})日?$/);
          if (chineseDateMatch) {
            const year = parseInt(chineseDateMatch[1]);
            const month = parseInt(chineseDateMatch[2]) - 1;
            const day = parseInt(chineseDateMatch[3]);
            return this.formatForMDAC(new Date(year, month, day));
          }
          
          // 标准格式处理
          if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
            return dateStr; // 已经是DD/MM/YYYY格式
          }
          
          if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
            const [year, month, day] = dateStr.split('-');
            return `${day}/${month}/${year}`;
          }
          
          // 尝试原生解析
          try {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
              return this.formatForMDAC(date);
            }
          } catch (error) {
            // 忽略错误
          }
          
          return dateStr;
        }
        
        formatForMDAC(date) {
          if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
            return '';
          }
          
          const day = date.getDate().toString().padStart(2, '0');
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const year = date.getFullYear();
          
          return `${day}/${month}/${year}`;
        }
      }
      
      dateParser = new SimpleDateParser();
      window.dateParser = dateParser;
    } else {
      dateParser = window.dateParser;
    }
  });
  
  it('应该正确解析YYYY-MM-DD格式', () => {
    const result = dateParser.smartParse('2025-12-31');
    expect(result).toBe('31/12/2025');
  });
  
  it('应该正确解析中文相对日期', () => {
    const today = new Date();
    const day = today.getDate().toString().padStart(2, '0');
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const year = today.getFullYear();
    const todayFormatted = `${day}/${month}/${year}`;
    
    const result = dateParser.smartParse('今天');
    expect(result).toBe(todayFormatted);
  });
  
  it('应该正确解析英文相对日期', () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const day = tomorrow.getDate().toString().padStart(2, '0');
    const month = (tomorrow.getMonth() + 1).toString().padStart(2, '0');
    const year = tomorrow.getFullYear();
    const tomorrowFormatted = `${day}/${month}/${year}`;
    
    const result = dateParser.smartParse('tomorrow');
    expect(result).toBe(tomorrowFormatted);
  });
  
  it('应该正确解析中文日期格式', () => {
    const result = dateParser.smartParse('2025年8月1日');
    expect(result).toBe('01/08/2025');
  });
});

// 机场匹配算法测试
describe('机场匹配算法测试', () => {
  let airportDB;
  
  beforeAll(() => {
    console.log('🔧 设置机场匹配测试环境...');
    
    // 检查机场数据库是否已加载
    if (!window.airportDB) {
      console.log('🛫 创建机场数据库实例...');
      
      // 创建简化的机场数据库
      class AirportDatabase {
        constructor() {
          this.airports = [];
          this.initialized = false;
          this.init();
        }
        
        init() {
          this.airports = [
            {
              iata: 'KUL',
              icao: 'WMKK',
              name: 'Kuala Lumpur International Airport',
              city: 'Kuala Lumpur',
              country: 'Malaysia',
              aliases: ['KLIA', 'KLIA1', 'Sepang', '吉隆坡国际机场', '吉隆坡机场']
            },
            {
              iata: 'SIN',
              icao: 'WSSS',
              name: 'Singapore Changi Airport',
              city: 'Singapore',
              country: 'Singapore',
              aliases: ['Changi Airport', 'Changi', '樟宜机场', '新加坡机场']
            },
            {
              iata: 'BKK',
              icao: 'VTBS',
              name: 'Suvarnabhumi Airport',
              city: 'Bangkok',
              country: 'Thailand',
              aliases: ['Bangkok Airport', 'Suvarnabhumi', '素万那普机场', '曼谷机场']
            }
          ];
          
          this.initialized = true;
        }
        
        searchAirports(query, limit = 5) {
          if (!query) return [];
          
          const results = [];
          const searchTerm = query.toLowerCase();
          
          for (const airport of this.airports) {
            // 检查IATA/ICAO代码
            if (airport.iata.toLowerCase() === searchTerm || 
                airport.icao.toLowerCase() === searchTerm) {
              results.push({ airport, score: 100 });
              continue;
            }
            
            // 检查名称
            if (airport.name.toLowerCase().includes(searchTerm)) {
              results.push({ airport, score: 90 });
              continue;
            }
            
            // 检查城市
            if (airport.city.toLowerCase().includes(searchTerm)) {
              results.push({ airport, score: 80 });
              continue;
            }
            
            // 检查国家
            if (airport.country.toLowerCase().includes(searchTerm)) {
              results.push({ airport, score: 70 });
              continue;
            }
            
            // 检查别名
            if (airport.aliases && airport.aliases.some(alias => 
                alias.toLowerCase().includes(searchTerm))) {
              results.push({ airport, score: 85 });
              continue;
            }
          }
          
          return results
            .sort((a, b) => b.score - a.score)
            .slice(0, limit);
        }
      }
      
      airportDB = new AirportDatabase();
      window.airportDB = airportDB;
    } else {
      airportDB = window.airportDB;
    }
  });
  
  it('应该正确匹配IATA代码', () => {
    const results = airportDB.searchAirports('KUL');
    expect(results.length).toBeGreaterThan(0);
    expect(results[0].airport.iata).toBe('KUL');
  });
  
  it('应该正确匹配机场名称', () => {
    const results = airportDB.searchAirports('Changi');
    expect(results.length).toBeGreaterThan(0);
    expect(results[0].airport.iata).toBe('SIN');
  });
  
  it('应该正确匹配中文机场名称', () => {
    const results = airportDB.searchAirports('吉隆坡');
    expect(results.length).toBeGreaterThan(0);
    expect(results[0].airport.iata).toBe('KUL');
  });
});

// 字段填充测试
describe('字段填充测试', () => {
  beforeAll(() => {
    console.log('🔧 设置字段填充测试环境...');
  });
  
  it('应该正确填充passExpDte字段', async () => {
    const field = document.getElementById('passExpDte');
    if (!field) {
      console.warn('⚠️ passExpDte字段不存在，跳过测试');
      return;
    }
    
    const originalValue = field.value;
    const testValue = '31/12/2030';
    
    try {
      // 清空字段
      field.value = '';
      field.focus();
      field.dispatchEvent(new Event('focus', { bubbles: true }));
      await testFramework.delay(100);
      
      // 设置值
      field.value = testValue;
      
      // 触发事件序列
      field.dispatchEvent(new Event('input', { bubbles: true }));
      await testFramework.delay(50);
      field.dispatchEvent(new Event('change', { bubbles: true }));
      await testFramework.delay(50);
      field.dispatchEvent(new Event('blur', { bubbles: true }));
      
      // 验证填充结果
      expect(field.value).toBe(testValue);
    } finally {
      // 恢复原始值
      field.value = originalValue;
      field.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });
  
  it('应该正确填充arrDt字段', async () => {
    const field = document.getElementById('arrDt');
    if (!field) {
      console.warn('⚠️ arrDt字段不存在，跳过测试');
      return;
    }
    
    const originalValue = field.value;
    const testValue = '01/08/2025';
    
    try {
      // 清空字段
      field.value = '';
      field.focus();
      field.dispatchEvent(new Event('focus', { bubbles: true }));
      await testFramework.delay(100);
      
      // 设置值
      field.value = testValue;
      
      // 触发事件序列
      field.dispatchEvent(new Event('input', { bubbles: true }));
      await testFramework.delay(50);
      field.dispatchEvent(new Event('change', { bubbles: true }));
      await testFramework.delay(50);
      field.dispatchEvent(new Event('blur', { bubbles: true }));
      
      // 验证填充结果
      expect(field.value).toBe(testValue);
    } finally {
      // 恢复原始值
      field.value = originalValue;
      field.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });
});

// 运行所有测试
console.log('🧪 MDAC测试用例已加载');
console.log('使用 runAllTests() 运行所有测试');
