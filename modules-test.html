<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC全字段映射测试 - Chrome Extension Test</title>
    <!-- 引入MDAC监控接口 -->
    <script src="mdac-monitor-interface.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e74c3c;
            border-radius: 8px;
            background-color: #fff5f5;
        }
        .test-section h2 {
            color: #e74c3c;
            margin-top: 0;
        }
        .field-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }
        .field-item {
            display: flex;
            flex-direction: column;
        }
        label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #34495e;
        }
        input, select, textarea {
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
        }
        .success {
            background-color: #d4edda;
            border-color: #27ae60;
        }
        .error {
            background-color: #f8d7da;
            border-color: #e74c3c;
        }
        .test-button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #27ae60; }
        .status-error { background-color: #e74c3c; }
        .status-pending { background-color: #f39c12; }

        /* 监控接口样式 */
        .log-entry {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        .log-entry.status-success {
            background-color: rgba(76, 175, 80, 0.1);
            border-left: 3px solid #4CAF50;
        }
        .log-entry.status-error {
            background-color: rgba(244, 67, 54, 0.1);
            border-left: 3px solid #F44336;
        }
        .log-entry.status-warning {
            background-color: rgba(255, 152, 0, 0.1);
            border-left: 3px solid #FF9800;
        }
        .log-entry.status-info {
            background-color: rgba(33, 150, 243, 0.1);
            border-left: 3px solid #2196F3;
        }
        .log-entry.status-pending {
            background-color: rgba(156, 39, 176, 0.1);
            border-left: 3px solid #9C27B0;
        }
        .log-time {
            color: #666;
            font-size: 0.8em;
            margin-right: 8px;
        }
        .log-icon {
            margin-right: 8px;
        }
        .log-message {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 MDAC Chrome Extension 全字段映射测试</h1>
        
        <div class="test-section">
            <h2>📋 个人信息字段测试 (Personal Information)</h2>
            <div class="field-group">
                <div class="field-item">
                    <label for="passNo">护照号码 (Passport Number):</label>
                    <input type="text" id="passNo" name="passNo" placeholder="输入护照号码">
                </div>
                <div class="field-item">
                    <label for="passExpiry">护照到期日 (Passport Expiry):</label>
                    <input type="date" id="passExpiry" name="passExpiry">
                </div>
                <div class="field-item">
                    <label for="email">电子邮件 (Email):</label>
                    <input type="email" id="email" name="email" placeholder="输入电子邮件地址">
                </div>
                <div class="field-item">
                    <label for="mobile">手机号码 (Mobile Number):</label>
                    <input type="tel" id="mobile" name="mobile" placeholder="输入手机号码">
                </div>
                <div class="field-item">
                    <label for="firstName">名字 (First Name):</label>
                    <input type="text" id="firstName" name="firstName" placeholder="输入名字">
                </div>
                <div class="field-item">
                    <label for="lastName">姓氏 (Last Name):</label>
                    <input type="text" id="lastName" name="lastName" placeholder="输入姓氏">
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🌍 地址信息字段测试 (Address Information)</h2>
            <div class="field-group">
                <div class="field-item">
                    <label for="accommodationState">住宿州属 (Accommodation State):</label>
                    <select id="accommodationState" name="accommodationState">
                        <option value="">请选择州属</option>
                        <option value="01">01 - 柔佛 (Johor)</option>
                        <option value="02">02 - 吉打 (Kedah)</option>
                        <option value="03">03 - 吉兰丹 (Kelantan)</option>
                        <option value="04">04 - 马六甲 (Melaka)</option>
                        <option value="05">05 - 森美兰 (Negeri Sembilan)</option>
                        <option value="06">06 - 彭亨 (Pahang)</option>
                        <option value="07">07 - 槟城 (Pulau Pinang)</option>
                        <option value="08">08 - 霹雳 (Perak)</option>
                        <option value="09">09 - 玻璃市 (Perlis)</option>
                        <option value="10">10 - 雪兰莪 (Selangor)</option>
                        <option value="11">11 - 登嘉楼 (Terengganu)</option>
                        <option value="12">12 - 沙巴 (Sabah)</option>
                        <option value="13">13 - 砂拉越 (Sarawak)</option>
                        <option value="14">14 - 吉隆坡 (Kuala Lumpur)</option>
                        <option value="15">15 - 纳闽 (Labuan)</option>
                        <option value="16">16 - 布城 (Putrajaya)</option>
                    </select>
                </div>
                <div class="field-item">
                    <label for="accommodationCity">住宿城市 (Accommodation City):</label>
                    <input type="text" id="accommodationCity" name="accommodationCity" placeholder="输入住宿城市">
                </div>
                <div class="field-item">
                    <label for="accommodationAddress">住宿地址 (Accommodation Address):</label>
                    <textarea id="accommodationAddress" name="accommodationAddress" rows="3" placeholder="输入完整住宿地址"></textarea>
                </div>
                <div class="field-item">
                    <label for="accommodationPostcode">住宿邮编 (Accommodation Postcode):</label>
                    <input type="text" id="accommodationPostcode" name="accommodationPostcode" placeholder="输入邮政编码">
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>✈️ 旅行信息字段测试 (Travel Information)</h2>
            <div class="field-group">
                <div class="field-item">
                    <label for="arrivalDate">抵达日期 (Arrival Date):</label>
                    <input type="date" id="arrivalDate" name="arrivalDate">
                </div>
                <div class="field-item">
                    <label for="departureDate">离开日期 (Departure Date):</label>
                    <input type="date" id="departureDate" name="departureDate">
                </div>
                <div class="field-item">
                    <label for="flightNumber">航班号码 (Flight Number):</label>
                    <input type="text" id="flightNumber" name="flightNumber" placeholder="输入航班号码">
                </div>
                <div class="field-item">
                    <label for="purposeOfVisit">访问目的 (Purpose of Visit):</label>
                    <select id="purposeOfVisit" name="purposeOfVisit">
                        <option value="">请选择访问目的</option>
                        <option value="tourism">旅游 (Tourism)</option>
                        <option value="business">商务 (Business)</option>
                        <option value="transit">过境 (Transit)</option>
                        <option value="study">学习 (Study)</option>
                        <option value="medical">医疗 (Medical)</option>
                        <option value="others">其他 (Others)</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 测试控制面板 (Test Control Panel)</h2>
            <div style="text-align: center;">
                <button class="test-button" onclick="runFieldDetectionTest()">
                    🔍 运行字段检测测试
                </button>
                <button class="test-button" onclick="runBasicFieldMappingTest()">
                    📝 运行基础字段映射测试
                </button>
                <button class="test-button" onclick="runAdvancedMappingTest()">
                    🚀 运行高级映射测试
                </button>
                <button class="test-button" onclick="runStateCodeMappingTest()">
                    🗺️ 运行州代码映射测试
                </button>
                <button class="test-button" onclick="clearAllFields()">
                    🧹 清空所有字段
                </button>
                <button class="test-button" onclick="detectChromeExtension()">
                    🔌 检测Chrome扩展
                </button>

                <!-- 监控接口测试按钮 -->
                <hr style="margin: 15px 0; border-top: 1px dashed #ccc;">
                <h3>🔄 MDAC AI 监控接口测试</h3>
                <button class="test-button" onclick="runMonitorInterfaceTest()" style="background-color: #4CAF50; color: white;">
                    🧪 运行监控接口集成测试
                </button>
                <button class="test-button" onclick="runExtensionTests()" style="background-color: #2196F3; color: white;">
                    📊 运行扩展测试框架
                </button>
                <button class="test-button" onclick="clearMonitorLog()" style="background-color: #607D8B; color: white;">
                    🗑️ 清空监控日志
                </button>
            </div>

            <div class="test-results" id="testResults">
                <h3>📊 测试结果 (Test Results):</h3>
                <div id="testLog">
                    <p><span class="status-indicator status-pending"></span>等待测试开始...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        const testData = {
            basic: {
                passNo: "A12345678",
                email: "<EMAIL>",
                mobile: "+60123456789",
                firstName: "张",
                lastName: "三"
            },
            advanced: {
                passExpiry: "2030-12-31",
                accommodationCity: "吉隆坡",
                accommodationAddress: "Jalan Bukit Bintang, 55100",
                accommodationPostcode: "55100",
                arrivalDate: "2025-08-01",
                departureDate: "2025-08-15",
                flightNumber: "MH370",
                purposeOfVisit: "tourism"
            },
            stateMapping: {
                accommodationState: "14" // 吉隆坡
            }
        };

        function logTest(message, status = 'pending') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = `status-${status}`;
            
            logElement.innerHTML += `
                <p>
                    <span class="status-indicator ${statusClass}"></span>
                    [${timestamp}] ${message}
                </p>
            `;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function runFieldDetectionTest() {
            logTest('开始字段检测测试...', 'pending');
            
            const allFields = document.querySelectorAll('input, select, textarea');
            logTest(`检测到 ${allFields.length} 个字段`, 'success');
            
            allFields.forEach((field, index) => {
                const fieldInfo = `字段 ${index + 1}: ${field.tagName} - ID: ${field.id || 'N/A'} - Name: ${field.name || 'N/A'}`;
                logTest(fieldInfo, 'success');
            });
            
            logTest('字段检测测试完成 ✅', 'success');
        }

        function runBasicFieldMappingTest() {
            logTest('开始基础字段映射测试...', 'pending');
            
            try {
                Object.entries(testData.basic).forEach(([fieldId, value]) => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.value = value;
                        field.classList.add('success');
                        logTest(`✅ ${fieldId}: ${value}`, 'success');
                    } else {
                        logTest(`❌ 字段未找到: ${fieldId}`, 'error');
                    }
                });
                
                logTest('基础字段映射测试完成 ✅', 'success');
            } catch (error) {
                logTest(`❌ 基础映射测试失败: ${error.message}`, 'error');
            }
        }

        function runAdvancedMappingTest() {
            logTest('开始高级字段映射测试...', 'pending');
            
            try {
                Object.entries(testData.advanced).forEach(([fieldId, value]) => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.value = value;
                        field.classList.add('success');
                        logTest(`✅ ${fieldId}: ${value}`, 'success');
                    } else {
                        logTest(`❌ 字段未找到: ${fieldId}`, 'error');
                    }
                });
                
                logTest('高级字段映射测试完成 ✅', 'success');
            } catch (error) {
                logTest(`❌ 高级映射测试失败: ${error.message}`, 'error');
            }
        }

        function runStateCodeMappingTest() {
            logTest('开始州代码映射测试...', 'pending');
            
            try {
                const stateField = document.getElementById('accommodationState');
                if (stateField) {
                    stateField.value = testData.stateMapping.accommodationState;
                    stateField.classList.add('success');
                    
                    const selectedOption = stateField.options[stateField.selectedIndex];
                    const stateName = selectedOption.text;
                    
                    logTest(`✅ 州代码映射成功: ${testData.stateMapping.accommodationState} - ${stateName}`, 'success');
                    logTest('州代码映射测试完成 ✅', 'success');
                } else {
                    logTest('❌ 州属字段未找到', 'error');
                }
            } catch (error) {
                logTest(`❌ 州代码映射测试失败: ${error.message}`, 'error');
            }
        }

        function clearAllFields() {
            logTest('清空所有字段...', 'pending');
            
            const allFields = document.querySelectorAll('input, select, textarea');
            allFields.forEach(field => {
                field.value = '';
                field.classList.remove('success', 'error');
            });
            
            document.getElementById('testLog').innerHTML = '<p><span class="status-indicator status-success"></span>所有字段已清空 ✅</p>';
            logTest('字段清空完成，准备新的测试', 'success');
        }

        // Chrome Extension Detection
        function detectChromeExtension() {
            logTest('检测Chrome扩展...', 'pending');

            // 检查新版监控接口
            if (window.mdacMonitorInterface) {
                const stats = window.mdacMonitorInterface.getMonitoringStats();
                const componentCount = stats.components.length;

                if (stats.extensionDetected) {
                    logTest(`✅ MDAC AI Chrome扩展已检测到 (${componentCount}/4 组件)`, 'success');
                    logTest(`组件: ${stats.components.join(', ')}`, 'success');
                    logTest(`监控字段: ${stats.fieldsMonitored} 个`, 'success');
                } else {
                    logTest('⏳ 正在检测MDAC AI Chrome扩展...', 'pending');
                    // 重试检测
                    setTimeout(detectChromeExtension, 2000);
                }
            }
            // 检查旧版扩展
            else if (window.mdacModularSidePanel) {
                logTest('✅ MDAC Chrome扩展已检测到 (旧版本)', 'success');
                logTest('扩展版本信息已获取', 'success');
            } else {
                logTest('⚠️ 未检测到MDAC Chrome扩展', 'error');
                logTest('请确保扩展已安装并启用', 'error');
            }
        }

        // 页面加载完成后自动检测扩展
        window.addEventListener('load', function() {
            setTimeout(() => {
                detectChromeExtension();
            }, 1000);
        });

        // 监控接口测试函数
        function runMonitorInterfaceTest() {
            logTest('🚀 启动监控接口集成测试...', 'pending');

            if (window.mdacMonitorInterface) {
                window.mdacMonitorInterface.runIntegrationTest();
            } else {
                logTest('❌ 监控接口未找到', 'error');
                logTest('请确保mdac-monitor-interface.js已正确加载', 'error');
            }
        }

        function runExtensionTests() {
            logTest('🧪 启动扩展测试框架...', 'pending');

            if (window.mdacMonitorInterface) {
                window.mdacMonitorInterface.runExtensionTests();
            } else {
                logTest('❌ 监控接口未找到', 'error');
            }
        }

        function clearMonitorLog() {
            if (window.mdacMonitorInterface) {
                window.mdacMonitorInterface.clearTestLog();
                logTest('🗑️ 监控日志已清空', 'success');
            } else {
                document.getElementById('testLog').innerHTML = '<p><span class="status-indicator status-success"></span>日志已清空 ✅</p>';
            }
        }

        // 监听字段变化
        document.addEventListener('input', function(e) {
            if (e.target.matches('input, select, textarea')) {
                if (e.target.value) {
                    e.target.classList.add('success');
                    e.target.classList.remove('error');
                } else {
                    e.target.classList.remove('success', 'error');
                }
            }
        });
    </script>
</body>
</html>
![1752562594977](image/modules-test/1752562594977.png)![1752562600027](image/modules-test/1752562600027.png)