/**
 * MDAC智能地址解析器
 * 三层地址解析系统：本地匹配 -> AI解析 -> Google Maps验证
 * 
 * 作者: MDAC AI智能分析工具
 * 创建时间: 2025-01-15
 */

class AddressResolver {
    constructor() {
        this.logger = window.mdacLogger || console;
        this.addressDatabase = null;
        this.aiConfig = null;
        this.googleMapsLoaded = false;
        
        // 地址解析配置
        this.config = {
            confidenceThreshold: {
                local: 0.8,      // 本地匹配置信度阈值
                ai: 0.7,         // AI解析置信度阈值
                final: 0.6       // 最终结果置信度阈值
            },
            timeout: {
                local: 500,      // 本地匹配超时时间
                ai: 5000,        // AI解析超时时间
                maps: 3000       // Google Maps超时时间
            }
        };
        
        // 初始化
        this.initialize();
    }
    
    /**
     * 初始化地址解析器
     */
    async initialize() {
        try {
            this.logger.info('AddressResolver', '初始化智能地址解析器...');
            
            // 加载地址数据库
            await this.loadAddressDatabase();
            
            // 检查AI配置
            this.checkAIConfig();
            
            // 检查Google Maps
            await this.checkGoogleMaps();
            
            this.logger.info('AddressResolver', '地址解析器初始化完成');
        } catch (error) {
            this.logger.error('AddressResolver', '初始化失败:', error);
        }
    }
    
    /**
     * 主要地址解析方法 - 三层智能解析
     * @param {string} inputText - 输入的地址文本
     * @returns {Promise<Object>} 解析结果
     */
    async resolveAddress(inputText) {
        if (!inputText || typeof inputText !== 'string') {
            throw new Error('输入文本不能为空');
        }
        
        const startTime = Date.now();
        this.logger.info('AddressResolver', `开始解析地址: "${inputText}"`);
        
        try {
            // 第一层：快速本地匹配
            const localResult = await this.tryLocalMatch(inputText);
            if (localResult.confidence >= this.config.confidenceThreshold.local) {
                this.logger.info('AddressResolver', `本地匹配成功，置信度: ${localResult.confidence}`);
                return this.formatResult(localResult, 'local', Date.now() - startTime);
            }
            
            // 第二层：AI智能解析
            const aiResult = await this.aiParseAddress(inputText);
            if (aiResult.confidence >= this.config.confidenceThreshold.ai) {
                this.logger.info('AddressResolver', `AI解析成功，置信度: ${aiResult.confidence}`);
                
                // 第三层：Google Maps验证
                const verifiedResult = await this.verifyWithGoogleMaps(aiResult);
                return this.formatResult(verifiedResult, 'ai+maps', Date.now() - startTime);
            }
            
            // 兜底：直接Google Maps查询
            this.logger.info('AddressResolver', '尝试Google Maps直接查询');
            const mapsResult = await this.googleMapsGeocode(inputText);
            return this.formatResult(mapsResult, 'maps', Date.now() - startTime);
            
        } catch (error) {
            this.logger.error('AddressResolver', '地址解析失败:', error);
            throw error;
        }
    }
    
    /**
     * 第一层：本地快速匹配
     * @param {string} text - 输入文本
     * @returns {Promise<Object>} 匹配结果
     */
    async tryLocalMatch(text) {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('本地匹配超时')), this.config.timeout.local);
        });
        
        const matchPromise = this._performLocalMatch(text);
        
        try {
            return await Promise.race([matchPromise, timeoutPromise]);
        } catch (error) {
            this.logger.warn('AddressResolver', '本地匹配失败:', error.message);
            return { confidence: 0, reason: error.message };
        }
    }
    
    /**
     * 执行本地匹配
     * @param {string} text - 输入文本
     * @returns {Promise<Object>} 匹配结果
     */
    async _performLocalMatch(text) {
        // 1. 邮编匹配（最高优先级）
        const postcode = this.extractPostcode(text);
        if (postcode) {
            const postcodeResult = await this.getAddressByPostcode(postcode);
            if (postcodeResult.found) {
                return {
                    ...postcodeResult,
                    confidence: 0.95,
                    method: 'postcode'
                };
            }
        }
        
        // 2. 地标匹配
        const landmark = this.matchLandmark(text);
        if (landmark) {
            const landmarkResult = await this.getAddressByLandmark(landmark);
            if (landmarkResult.found) {
                return {
                    ...landmarkResult,
                    confidence: 0.85,
                    method: 'landmark'
                };
            }
        }
        
        // 3. 关键词匹配
        const keywords = this.extractKeywords(text);
        const keywordResult = await this.fuzzyMatch(keywords);
        if (keywordResult.found) {
            return {
                ...keywordResult,
                confidence: keywordResult.similarity,
                method: 'keyword'
            };
        }
        
        return { confidence: 0, reason: '未找到本地匹配' };
    }
    
    /**
     * 第二层：AI智能解析
     * @param {string} text - 输入文本
     * @returns {Promise<Object>} AI解析结果
     */
    async aiParseAddress(text) {
        if (!this.aiConfig || !window.MDAC_AI_CONFIG) {
            throw new Error('AI配置未加载');
        }
        
        const prompt = `
请从以下文本中提取马来西亚地址信息：
"${text}"

解析要求：
1. 识别邮编（5位数字）
2. 识别州属（如Selangor, Kuala Lumpur, Johor等）
3. 识别城市/区域
4. 补全完整地址
5. 计算置信度（0-1）

请返回以下JSON格式：
{
    "address": "完整地址",
    "postcode": "邮编",
    "state": "州属名称",
    "city": "城市名称",
    "confidence": 0.8,
    "reasoning": "解析依据"
}

如果无法确定某个字段，请设为null。
        `;
        
        try {
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('AI解析超时')), this.config.timeout.ai);
            });
            
            const aiPromise = this._callGeminiAI(prompt);
            const aiResponse = await Promise.race([aiPromise, timeoutPromise]);
            
            return this.parseAIResponse(aiResponse);
        } catch (error) {
            this.logger.error('AddressResolver', 'AI解析失败:', error);
            return { confidence: 0, reason: error.message };
        }
    }
    
    /**
     * 第三层：Google Maps验证
     * @param {Object} aiResult - AI解析结果
     * @returns {Promise<Object>} 验证结果
     */
    async verifyWithGoogleMaps(aiResult) {
        if (!this.googleMapsLoaded) {
            this.logger.warn('AddressResolver', 'Google Maps未加载，跳过验证');
            return { ...aiResult, verified: false };
        }
        
        try {
            const geocoded = await this.geocodeAddress(aiResult.address);
            return {
                ...aiResult,
                verified: true,
                coordinates: geocoded.coordinates,
                formattedAddress: geocoded.formatted_address,
                confidence: Math.min(aiResult.confidence + 0.1, 1.0) // 验证成功提升置信度
            };
        } catch (error) {
            this.logger.warn('AddressResolver', 'Google Maps验证失败:', error);
            return { ...aiResult, verified: false };
        }
    }
    
    /**
     * 兜底：Google Maps直接查询
     * @param {string} text - 输入文本
     * @returns {Promise<Object>} 查询结果
     */
    async googleMapsGeocode(text) {
        if (!this.googleMapsLoaded) {
            throw new Error('Google Maps未加载');
        }
        
        const geocoder = new google.maps.Geocoder();
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Google Maps查询超时'));
            }, this.config.timeout.maps);
            
            geocoder.geocode({
                address: text,
                region: 'MY',
                componentRestrictions: { country: 'MY' }
            }, (results, status) => {
                clearTimeout(timeout);
                
                if (status === 'OK' && results.length > 0) {
                    const result = results[0];
                    resolve({
                        address: result.formatted_address,
                        postcode: this.extractPostcodeFromGoogleResult(result),
                        state: this.extractStateFromGoogleResult(result),
                        city: this.extractCityFromGoogleResult(result),
                        coordinates: {
                            lat: result.geometry.location.lat(),
                            lng: result.geometry.location.lng()
                        },
                        confidence: 0.7,
                        verified: true
                    });
                } else {
                    reject(new Error(`Google Maps查询失败: ${status}`));
                }
            });
        });
    }
    
    /**
     * 格式化返回结果
     * @param {Object} result - 原始结果
     * @param {string} method - 解析方法
     * @param {number} duration - 耗时
     * @returns {Object} 格式化结果
     */
    formatResult(result, method, duration) {
        return {
            success: true,
            data: {
                address: result.address || null,
                postcode: result.postcode || null,
                state: result.state || null,
                city: result.city || null,
                coordinates: result.coordinates || null,
                confidence: result.confidence || 0,
                verified: result.verified || false
            },
            metadata: {
                method: method,
                duration: duration,
                timestamp: new Date().toISOString()
            }
        };
    }
    
    /**
     * 工具方法：提取邮编
     * @param {string} text - 文本
     * @returns {string|null} 邮编
     */
    extractPostcode(text) {
        const postcodePattern = /\b\d{5}\b/g;
        const matches = text.match(postcodePattern);
        return matches ? matches[0] : null;
    }
    
    /**
     * 工具方法：提取关键词
     * @param {string} text - 文本
     * @returns {Array} 关键词数组
     */
    extractKeywords(text) {
        // 清理文本并提取关键词
        const cleanText = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
        
        const words = cleanText.split(' ');
        
        // 过滤停用词
        const stopWords = ['the', 'and', 'or', 'at', 'in', 'on', 'to', 'for', 'of', 'with'];
        
        return words.filter(word => 
            word.length > 2 && !stopWords.includes(word)
        );
    }
    
    /**
     * 检查AI配置
     */
    checkAIConfig() {
        if (window.MDAC_AI_CONFIG && window.MDAC_AI_CONFIG.AI_PROMPTS) {
            this.aiConfig = window.MDAC_AI_CONFIG;
            this.logger.info('AddressResolver', 'AI配置检查通过');
        } else {
            this.logger.warn('AddressResolver', 'AI配置未找到');
        }
    }
    
    /**
     * 检查Google Maps
     */
    async checkGoogleMaps() {
        if (typeof google !== 'undefined' && google.maps) {
            this.googleMapsLoaded = true;
            this.logger.info('AddressResolver', 'Google Maps检查通过');
        } else {
            this.logger.warn('AddressResolver', 'Google Maps未加载');
        }
    }
    
    /**
     * 加载地址数据库
     */
    async loadAddressDatabase() {
        try {
            // 优先加载扩展的地址数据库
            let response = await fetch(chrome.runtime.getURL('config/malaysia-address-database.json'));
            if (!response.ok) {
                // 备用：加载基础地址数据
                response = await fetch(chrome.runtime.getURL('config/malaysia-states-cities.json'));
            }
            
            if (response.ok) {
                this.addressDatabase = await response.json();
                this.logger.info('AddressResolver', '地址数据库加载成功');
            } else {
                throw new Error('地址数据库加载失败');
            }
        } catch (error) {
            this.logger.error('AddressResolver', '地址数据库加载失败:', error);
            // 使用默认数据库
            this.addressDatabase = this.getDefaultAddressDatabase();
        }
    }
    
    /**
     * 获取默认地址数据库
     */
    getDefaultAddressDatabase() {
        return {
            states: {
                "14": { name: "W.P. Kuala Lumpur", cities: {} },
                "01": { name: "Johor", cities: {} },
                "07": { name: "Penang", cities: {} }
            },
            landmarks: {
                "KLCC": { state: "14", city: "1400", postcode: "50088" },
                "Legoland": { state: "01", city: "0118", postcode: "79100" },
                "Genting": { state: "06", city: "0600", postcode: "69000" }
            }
        };
    }
    
    /**
     * 根据邮编获取地址信息
     * @param {string} postcode - 邮编
     * @returns {Object} 地址信息
     */
    async getAddressByPostcode(postcode) {
        if (!this.addressDatabase || !this.addressDatabase.states) {
            return { found: false, reason: '地址数据库未加载' };
        }
        
        // 遍历所有州和城市查找邮编
        for (const stateCode in this.addressDatabase.states) {
            const state = this.addressDatabase.states[stateCode];
            for (const cityCode in state.cities) {
                const city = state.cities[cityCode];
                if (city.postcodes && city.postcodes.includes(postcode)) {
                    return {
                        found: true,
                        address: `${city.name}, ${state.name}`,
                        postcode: postcode,
                        state: stateCode,
                        city: cityCode,
                        stateName: state.name,
                        cityName: city.name
                    };
                }
            }
        }
        
        return { found: false, reason: '邮编未找到' };
    }
    
    /**
     * 匹配地标
     * @param {string} text - 文本
     * @returns {Object|null} 地标信息
     */
    matchLandmark(text) {
        if (!this.addressDatabase || !this.addressDatabase.landmarks) {
            return null;
        }
        
        const lowerText = text.toLowerCase();
        
        // 直接匹配地标名称
        for (const landmarkKey in this.addressDatabase.landmarks) {
            const landmark = this.addressDatabase.landmarks[landmarkKey];
            if (lowerText.includes(landmarkKey.toLowerCase()) || 
                lowerText.includes(landmark.name.toLowerCase())) {
                return {
                    name: landmark.name,
                    key: landmarkKey,
                    ...landmark
                };
            }
        }
        
        // 模糊匹配地标
        for (const landmarkKey in this.addressDatabase.landmarks) {
            const landmark = this.addressDatabase.landmarks[landmarkKey];
            const keywords = landmarkKey.toLowerCase().split(' ');
            
            if (keywords.some(keyword => lowerText.includes(keyword) && keyword.length > 2)) {
                return {
                    name: landmark.name,
                    key: landmarkKey,
                    ...landmark
                };
            }
        }
        
        return null;
    }
    
    /**
     * 根据地标获取地址信息
     * @param {Object} landmark - 地标信息
     * @returns {Object} 地址信息
     */
    async getAddressByLandmark(landmark) {
        if (!landmark) {
            return { found: false, reason: '地标信息为空' };
        }
        
        return {
            found: true,
            address: landmark.address || `${landmark.name}, ${landmark.postcode}`,
            postcode: landmark.postcode,
            state: landmark.state,
            city: landmark.city,
            landmark: landmark.name
        };
    }
    
    /**
     * 模糊匹配关键词
     * @param {Array} keywords - 关键词数组
     * @returns {Object} 匹配结果
     */
    async fuzzyMatch(keywords) {
        if (!this.addressDatabase || !keywords || keywords.length === 0) {
            return { found: false, reason: '关键词为空或数据库未加载' };
        }
        
        let bestMatch = null;
        let highestSimilarity = 0;
        
        // 检查别名匹配
        if (this.addressDatabase.aliases) {
            for (const alias in this.addressDatabase.aliases) {
                const variations = this.addressDatabase.aliases[alias];
                for (const keyword of keywords) {
                    const similarity = this.calculateSimilarity(keyword, alias);
                    if (similarity > highestSimilarity) {
                        highestSimilarity = similarity;
                        bestMatch = {
                            type: 'alias',
                            match: alias,
                            variations: variations
                        };
                    }
                    
                    // 检查变体匹配
                    for (const variation of variations) {
                        const varSimilarity = this.calculateSimilarity(keyword, variation);
                        if (varSimilarity > highestSimilarity) {
                            highestSimilarity = varSimilarity;
                            bestMatch = {
                                type: 'variation',
                                match: variation,
                                alias: alias
                            };
                        }
                    }
                }
            }
        }
        
        // 检查城市名称匹配
        for (const stateCode in this.addressDatabase.states) {
            const state = this.addressDatabase.states[stateCode];
            for (const cityCode in state.cities) {
                const city = state.cities[cityCode];
                
                for (const keyword of keywords) {
                    const similarity = this.calculateSimilarity(keyword, city.name);
                    if (similarity > highestSimilarity) {
                        highestSimilarity = similarity;
                        bestMatch = {
                            type: 'city',
                            match: city.name,
                            state: stateCode,
                            city: cityCode,
                            stateName: state.name,
                            cityName: city.name
                        };
                    }
                }
                
                // 检查地区匹配
                if (city.areas) {
                    for (const area of city.areas) {
                        for (const keyword of keywords) {
                            const similarity = this.calculateSimilarity(keyword, area);
                            if (similarity > highestSimilarity) {
                                highestSimilarity = similarity;
                                bestMatch = {
                                    type: 'area',
                                    match: area,
                                    state: stateCode,
                                    city: cityCode,
                                    stateName: state.name,
                                    cityName: city.name
                                };
                            }
                        }
                    }
                }
            }
        }
        
        if (bestMatch && highestSimilarity > 0.6) {
            return {
                found: true,
                similarity: highestSimilarity,
                address: this.generateAddressFromMatch(bestMatch),
                postcode: this.getPostcodeFromMatch(bestMatch),
                state: bestMatch.state,
                city: bestMatch.city,
                match: bestMatch
            };
        }
        
        return { found: false, reason: '未找到匹配结果' };
    }
    
    /**
     * 计算字符串相似度
     * @param {string} str1 - 字符串1
     * @param {string} str2 - 字符串2
     * @returns {number} 相似度 (0-1)
     */
    calculateSimilarity(str1, str2) {
        if (!str1 || !str2) return 0;
        
        const s1 = str1.toLowerCase();
        const s2 = str2.toLowerCase();
        
        // 完全匹配
        if (s1 === s2) return 1.0;
        
        // 包含匹配
        if (s1.includes(s2) || s2.includes(s1)) return 0.8;
        
        // Levenshtein距离算法
        const matrix = [];
        const len1 = s1.length;
        const len2 = s2.length;
        
        for (let i = 0; i <= len1; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= len2; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // 删除
                    matrix[i][j - 1] + 1,      // 插入
                    matrix[i - 1][j - 1] + cost // 替换
                );
            }
        }
        
        const distance = matrix[len1][len2];
        const maxLength = Math.max(len1, len2);
        
        return 1 - (distance / maxLength);
    }
    
    /**
     * 从匹配结果生成地址
     * @param {Object} match - 匹配结果
     * @returns {string} 地址
     */
    generateAddressFromMatch(match) {
        if (match.type === 'city' || match.type === 'area') {
            return `${match.match}, ${match.cityName}, ${match.stateName}`;
        } else if (match.type === 'alias' || match.type === 'variation') {
            return match.match;
        }
        return match.match;
    }
    
    /**
     * 从匹配结果获取邮编
     * @param {Object} match - 匹配结果
     * @returns {string|null} 邮编
     */
    getPostcodeFromMatch(match) {
        if (match.type === 'city' || match.type === 'area') {
            // 返回该城市的第一个邮编
            const state = this.addressDatabase.states[match.state];
            const city = state.cities[match.city];
            return city.postcodes ? city.postcodes[0] : null;
        }
        return null;
    }
    
    /**
     * 调用Gemini AI进行地址解析
     * @param {string} prompt - AI提示词
     * @returns {Promise<Object>} AI响应
     */
    async _callGeminiAI(prompt) {
        if (!this.aiConfig || !window.MDAC_AI_CONFIG) {
            throw new Error('AI配置未加载');
        }
        
        const apiKey = window.MDAC_AI_CONFIG.GEMINI_CONFIG.DEFAULT_API_KEY;
        const model = window.MDAC_AI_CONFIG.GEMINI_CONFIG.DEFAULT_MODEL;
        const apiUrl = `${window.MDAC_AI_CONFIG.GEMINI_CONFIG.API_BASE_URL}/${model}:generateContent?key=${apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: window.MDAC_AI_CONFIG.GEMINI_CONFIG.DEFAULT_GENERATION_CONFIG,
            safetySettings: window.MDAC_AI_CONFIG.GEMINI_CONFIG.SAFETY_SETTINGS
        };
        
        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            
            if (!response.ok) {
                throw new Error(`AI API请求失败: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.candidates && data.candidates.length > 0) {
                return data.candidates[0].content.parts[0].text;
            } else {
                throw new Error('AI API返回空结果');
            }
        } catch (error) {
            this.logger.error('AddressResolver', 'AI API调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 解析AI响应
     * @param {string} response - AI响应文本
     * @returns {Object} 解析结果
     */
    parseAIResponse(response) {
        if (!response) {
            return { confidence: 0, reason: 'AI响应为空' };
        }
        
        try {
            // 清理响应文本，移除markdown标记
            let cleanedResponse = response.trim();
            
            // 移除markdown代码块标记
            cleanedResponse = cleanedResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '');
            
            // 查找JSON块
            const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('未找到有效的JSON格式');
            }
            
            const jsonString = jsonMatch[0];
            const parsed = JSON.parse(jsonString);
            
            // 验证必要字段
            const result = {
                address: parsed.address || null,
                postcode: parsed.postcode || null,
                state: parsed.state || null,
                city: parsed.city || null,
                confidence: Math.max(0, Math.min(1, parsed.confidence || 0.5)),
                reasoning: parsed.reasoning || '无理由说明'
            };
            
            // 进一步验证和标准化
            if (result.postcode && !/^\d{5}$/.test(result.postcode)) {
                result.postcode = null;
            }
            
            // 转换州属名称为代码
            if (result.state && !result.state.match(/^\d{2}$/)) {
                result.state = this.convertStateNameToCode(result.state);
            }
            
            return result;
            
        } catch (error) {
            this.logger.error('AddressResolver', 'AI响应解析失败:', error);
            
            // 尝试简单的文本解析作为备选
            const fallbackResult = this.fallbackTextParsing(response);
            return {
                ...fallbackResult,
                confidence: Math.max(0, fallbackResult.confidence - 0.3), // 降低置信度
                reasoning: `解析失败，使用文本分析: ${error.message}`
            };
        }
    }
    
    /**
     * 备选文本解析
     * @param {string} text - 文本
     * @returns {Object} 解析结果
     */
    fallbackTextParsing(text) {
        const result = {
            address: null,
            postcode: null,
            state: null,
            city: null,
            confidence: 0.3
        };
        
        // 提取邮编
        const postcodeMatch = text.match(/\b\d{5}\b/);
        if (postcodeMatch) {
            result.postcode = postcodeMatch[0];
            result.confidence += 0.2;
        }
        
        // 提取可能的地址信息
        const keywords = this.extractKeywords(text);
        if (keywords.length > 0) {
            result.address = keywords.join(', ');
            result.confidence += 0.1;
        }
        
        return result;
    }
    
    /**
     * 转换州属名称为代码
     * @param {string} stateName - 州属名称
     * @returns {string|null} 州属代码
     */
    convertStateNameToCode(stateName) {
        if (!stateName || !this.addressDatabase || !this.addressDatabase.states) {
            return null;
        }
        
        const lowerStateName = stateName.toLowerCase();
        
        // 直接匹配州属名称
        for (const stateCode in this.addressDatabase.states) {
            const state = this.addressDatabase.states[stateCode];
            if (state.name.toLowerCase() === lowerStateName) {
                return stateCode;
            }
        }
        
        // 模糊匹配州属名称
        for (const stateCode in this.addressDatabase.states) {
            const state = this.addressDatabase.states[stateCode];
            if (state.name.toLowerCase().includes(lowerStateName) || 
                lowerStateName.includes(state.name.toLowerCase())) {
                return stateCode;
            }
        }
        
        // 检查别名
        const stateAliases = {
            'kuala lumpur': '14',
            'kl': '14',
            'johor': '01',
            'penang': '07',
            'selangor': '04',
            'pahang': '06',
            'perak': '08',
            'negeri sembilan': '05',
            'melaka': '04',
            'kedah': '02',
            'kelantan': '03',
            'terengganu': '11',
            'perlis': '09',
            'sabah': '12',
            'sarawak': '13',
            'putrajaya': '15',
            'labuan': '16'
        };
        
        return stateAliases[lowerStateName] || null;
    }
    /**
     * 使用Google Maps进行地址地理编码
     * @param {string} address - 地址
     * @returns {Promise<Object>} 地理编码结果
     */
    async geocodeAddress(address) {
        if (!this.googleMapsLoaded) {
            throw new Error('Google Maps未加载');
        }
        
        const geocoder = new google.maps.Geocoder();
        
        return new Promise((resolve, reject) => {
            geocoder.geocode({
                address: address,
                region: 'MY',
                componentRestrictions: { country: 'MY' }
            }, (results, status) => {
                if (status === 'OK' && results.length > 0) {
                    const result = results[0];
                    resolve({
                        formatted_address: result.formatted_address,
                        coordinates: {
                            lat: result.geometry.location.lat(),
                            lng: result.geometry.location.lng()
                        },
                        address_components: result.address_components,
                        geometry: result.geometry
                    });
                } else {
                    reject(new Error(`地理编码失败: ${status}`));
                }
            });
        });
    }
    
    /**
     * 从Google Maps结果中提取邮编
     * @param {Object} result - Google Maps结果
     * @returns {string|null} 邮编
     */
    extractPostcodeFromGoogleResult(result) {
        if (!result.address_components) {
            return null;
        }
        
        for (const component of result.address_components) {
            if (component.types.includes('postal_code')) {
                return component.long_name;
            }
        }
        
        return null;
    }
    
    /**
     * 从Google Maps结果中提取州属
     * @param {Object} result - Google Maps结果
     * @returns {string|null} 州属
     */
    extractStateFromGoogleResult(result) {
        if (!result.address_components) {
            return null;
        }
        
        for (const component of result.address_components) {
            if (component.types.includes('administrative_area_level_1')) {
                // 转换州属名称为代码
                return this.convertStateNameToCode(component.long_name);
            }
        }
        
        return null;
    }
    
    /**
     * 从Google Maps结果中提取城市
     * @param {Object} result - Google Maps结果
     * @returns {string|null} 城市
     */
    extractCityFromGoogleResult(result) {
        if (!result.address_components) {
            return null;
        }
        
        // 查找城市信息
        let cityName = null;
        for (const component of result.address_components) {
            if (component.types.includes('locality') || 
                component.types.includes('administrative_area_level_2') ||
                component.types.includes('sublocality')) {
                cityName = component.long_name;
                break;
            }
        }
        
        if (!cityName) {
            return null;
        }
        
        // 转换城市名称为代码
        return this.convertCityNameToCode(cityName);
    }
    
    /**
     * 转换城市名称为代码
     * @param {string} cityName - 城市名称
     * @returns {string|null} 城市代码
     */
    convertCityNameToCode(cityName) {
        if (!cityName || !this.addressDatabase || !this.addressDatabase.states) {
            return null;
        }
        
        const lowerCityName = cityName.toLowerCase();
        
        // 遍历所有州和城市查找匹配
        for (const stateCode in this.addressDatabase.states) {
            const state = this.addressDatabase.states[stateCode];
            for (const cityCode in state.cities) {
                const city = state.cities[cityCode];
                if (city.name.toLowerCase() === lowerCityName) {
                    return cityCode;
                }
                
                // 检查地区名称
                if (city.areas) {
                    for (const area of city.areas) {
                        if (area.toLowerCase() === lowerCityName) {
                            return cityCode;
                        }
                    }
                }
            }
        }
        
        return null;
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.AddressResolver = AddressResolver;
    console.log('✅ [AddressResolver] 智能地址解析器类已加载');
}