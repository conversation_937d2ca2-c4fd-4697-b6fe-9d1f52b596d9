/* MDAC AI智能分析工具 - 简洁2x2网格布局样式 */

/* 基础样式重置和现代化设计 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调系统 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* 背景色系统 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;

    /* 文字色系统 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

/* 通用隐藏类 - 用于CSP合规性 */
.hidden {
    display: none !important;
}

body.sidepanel-body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-secondary);
    overflow-x: hidden;
}

.sidepanel-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    position: relative;
}

/* 连接状态指示器 - 简化版 */
.connection-status {
    position: sticky;
    top: 0;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    text-align: center;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: #dcfce7;
    color: var(--success-color);
    border-bottom-color: #bbf7d0;
}

/* MDAC快速访问区域 */
.mdac-quick-access {
    padding: var(--spacing-md);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.mdac-quick-access::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.mdac-access-btn {
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
}

.mdac-access-btn:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.mdac-access-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    width: 100%;
}

.btn-icon {
    font-size: 24px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-text-group {
    flex: 1;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.btn-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.2;
}

.btn-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.2;
}

.btn-arrow {
    font-size: 18px;
    color: var(--primary-color);
    font-weight: bold;
    flex-shrink: 0;
    transition: transform 0.3s ease;
}

.mdac-access-btn:hover .btn-arrow {
    transform: translateX(4px);
}

/* 响应式设计 */
@media (max-width: 400px) {
    .mdac-quick-access {
        padding: var(--spacing-sm);
    }

    .mdac-access-btn {
        padding: var(--spacing-sm);
    }

    .btn-content {
        gap: var(--spacing-sm);
    }

    .btn-icon {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }

    .btn-title {
        font-size: 14px;
    }

    .btn-subtitle {
        font-size: 11px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .mdac-quick-access {
        background: linear-gradient(135deg, #4c1d95 0%, #581c87 100%);
    }

    .mdac-access-btn {
        background: rgba(30, 41, 59, 0.95);
        color: #f1f5f9;
    }

    .mdac-access-btn:hover {
        background: rgba(30, 41, 59, 1);
    }

    .btn-title {
        color: #f1f5f9;
    }

    .btn-subtitle {
        color: #94a3b8;
    }
}

/* 头部区域 - 简化版 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.ai-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 12px;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 网站检测区域 - 简化版 */
.site-detection {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.detection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

/* ===== 核心2x2网格布局样式 ===== */

.grid-layout {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    min-height: 0; /* 允许flex子项收缩 */
    overflow-y: auto;
}

/* 顶部区域：工具栏区域 */
.top-section {
    /* 使用统一的卡片样式 */
    flex-shrink: 0; /* 防止顶部区域被压缩 */
}

/* 工具栏样式 */
.toolbar-header {
    margin-bottom: var(--spacing-md);
}

.toolbar-icon {
    font-size: 16px;
}

.toolbar-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

/* 图片上传按钮样式 */
.image-upload-btn {
    /* 使用统一的基础按钮样式 */
}

/* 工具按钮样式 */
.tool-btn {
    /* 使用统一的基础按钮样式 */
}

.tool-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 解析输入区域样式 */
.parse-input-section {
    /* 使用统一的卡片样式 */
    margin-bottom: var(--spacing-md);
    background: var(--bg-tertiary);
}

.parse-input-header {
    /* 使用统一的布局工具类 */
    margin-bottom: var(--spacing-sm);
}

.parse-icon {
    font-size: 14px;
}

.parse-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.parse-textarea {
    /* 使用统一的输入框样式 */
    font-size: 12px;
}

.parse-btn {
    /* 使用统一的基础按钮样式 */
    margin-top: var(--spacing-sm);
}

/* 中部区域：字段映射显示区 */
.middle-section {
    /* 使用统一的卡片样式 */
    min-height: 300px; /* 确保有足够的高度显示字段 */
    overflow: hidden;
}

.fields-grid {
    /* 使用统一的布局工具类 */
    grid-template-columns: 1fr 1fr;
    min-height: 0; /* 允许内容滚动 */
}

/* 列样式 */
.personal-info-column,
.travel-info-column {
    /* 使用统一的卡片内边距 */
    overflow-y: auto;
}

.personal-info-column {
    border-right: 1px solid var(--border-color);
}

.column-header {
    /* 使用统一的布局工具类 */
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.column-icon {
    font-size: 16px;
}

.column-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

/* 预设区域样式 */
.preset-section {
    /* 使用统一的卡片样式 */
    margin-bottom: var(--spacing-md);
    background: var(--bg-tertiary);
}

.preset-header {
    /* 使用统一的布局工具类 */
    margin-bottom: var(--spacing-sm);
}

.preset-header .preset-icon {
    font-size: 14px;
}

.preset-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.preset-edit-btn {
    background: none;
    border: none;
    font-size: 12px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 2px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.preset-edit-btn:hover {
    background: var(--bg-accent);
    color: var(--primary-color);
}

.preset-fields {
    /* 使用统一的布局工具类 */
}

.preset-field {
    /* 使用统一的布局工具类 */
}

.preset-field label {
    font-size: 11px;
    font-weight: 500;
    color: var(--text-secondary);
}

.preset-input {
    /* 使用统一的输入框样式 */
    border-radius: 4px;
    font-size: 12px;
}

/* 字段组样式 */
.field-group {
    /* 使用统一的布局工具类 */
}

.field-item {
    /* 使用统一的布局工具类 */
    position: relative;
}

.field-item label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.field-input {
    /* 使用统一的输入框样式 */
}

.address-input {
    min-height: 60px;
    resize: vertical;
}

/* 字段状态指示器 */
.field-status {
    position: absolute;
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.field-status.success {
    background: var(--success-color);
    color: white;
    opacity: 1;
}

.field-status.error {
    background: var(--error-color);
    color: white;
    opacity: 1;
}

.field-status.warning {
    background: var(--warning-color);
    color: white;
    opacity: 1;
}

.field-status::after {
    content: '✓';
}

.field-status.error::after {
    content: '✗';
}

.field-status.warning::after {
    content: '!';
}

/* 底部区域：操作按钮区 */
.bottom-section {
    /* 使用统一的卡片样式 */
    flex-shrink: 0; /* 防止底部区域被压缩 */
}

.action-buttons {
    /* 使用统一的布局工具类 */
    margin-bottom: var(--spacing-md);
}

/* 主要按钮 */
.primary-action-btn {
    /* 使用统一的按钮样式 */
    font-size: 14px;
    font-weight: 600;
    box-shadow: var(--shadow-md);
}

.primary-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 辅助按钮组 */
.secondary-actions {
    /* 使用统一的布局工具类 */
    grid-template-columns: repeat(4, 1fr);
}

.secondary-btn {
    /* 使用统一的按钮样式 */
    font-size: 11px;
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 500;
}

/* 状态指示器 */
.status-indicators {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: var(--text-secondary);
}

.status-icon {
    font-size: 12px;
}

.status-text {
    font-weight: 500;
}

/* Footer样式 */
.footer {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: var(--text-muted);
}

.version {
    font-weight: 600;
}

.status {
    color: var(--success-color);
}

/* 隐藏的解析状态和结果区域 */
.parsing-status {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    z-index: 1000;
    min-width: 300px;
    text-align: center;
}

.status-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.parse-results {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    z-index: 1000;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

/* 响应式设计 - 合并和优化 */
@media (max-width: 768px) {
    .fields-grid {
        grid-template-columns: 1fr;
    }

    .personal-info-column {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .secondary-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-indicators {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .grid-layout {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .secondary-actions {
        grid-template-columns: 1fr;
    }
}

/* 连接状态的其他状态 */
.connection-status.disconnected {
    background: #fef3c7;
    color: var(--warning-color);
    border-bottom-color: #fde68a;
}

.connection-status.error {
    background: #fee2e2;
    color: var(--error-color);
    border-bottom-color: #fecaca;
}

/* ===== 统一组件样式系统 ===== */

/* 基础按钮组件 */
.btn-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-height: 36px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-base:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-base:active {
    transform: translateY(0);
}

.btn-base:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 按钮变体 */
.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    box-shadow: var(--shadow-md);
}

.btn-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
}

.btn-gradient:hover {
    box-shadow: var(--shadow-md);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 14px;
    font-weight: 600;
    min-height: 44px;
}

.btn-small {
    padding: 6px var(--spacing-sm);
    font-size: 12px;
    min-height: 28px;
}

.btn-icon-only {
    padding: var(--spacing-sm);
    width: 36px;
    height: 36px;
    min-height: 36px;
}

.btn-full-width {
    width: 100%;
}

/* 基础输入框组件 */
.input-base {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    font-family: inherit;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    width: 100%;
}

.input-base:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.input-base:invalid {
    border-color: var(--error-color);
}

.input-base.filled {
    background: #dcfce7;
    border-color: var(--success-color);
}

.input-small {
    padding: 6px var(--spacing-sm);
    font-size: 12px;
}

.input-large {
    padding: var(--spacing-md);
    font-size: 14px;
}

.input-textarea {
    resize: vertical;
    min-height: 60px;
}

/* 基础卡片组件 */
.card-base {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-padding {
    padding: var(--spacing-md);
}

.card-padding-small {
    padding: var(--spacing-sm);
}

.card-padding-large {
    padding: var(--spacing-lg);
}

.card-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-body {
    padding: var(--spacing-md);
}

.card-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
}

/* 基础布局工具类 */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-end {
    justify-content: flex-end;
}

.flex-1 {
    flex: 1;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.gap-xs {
    gap: var(--spacing-xs);
}

.gap-sm {
    gap: var(--spacing-sm);
}

.gap-md {
    gap: var(--spacing-md);
}

.gap-lg {
    gap: var(--spacing-lg);
}

/* 基础状态组件 */
.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 12px;
    color: var(--text-secondary);
}

.status-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.status-badge.success {
    background: var(--success-color);
    color: white;
}

.status-badge.error {
    background: var(--error-color);
    color: white;
}

.status-badge.warning {
    background: var(--warning-color);
    color: white;
}

/* ===== 工具类样式 ===== */

/* 显示/隐藏工具类 */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 动画工具类 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 成功/错误状态样式 */
.success {
    color: var(--success-color) !important;
}

.error {
    color: var(--error-color) !important;
}

.warning {
    color: var(--warning-color) !important;
}

/* 文本工具类 */
.text-center {
    text-align: center;
}

.text-muted {
    color: var(--text-muted);
}

.font-weight-bold {
    font-weight: 600;
}

/* 间距工具类 */
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }

/* ===== 缺失的基础样式 ===== */

/* 基础按钮样式 - 已移至统一组件系统 */

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex !important;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-md);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* 消息提示系统 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    color: white;
    font-size: 13px;
    font-weight: 500;
    z-index: 10001;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: var(--shadow-lg);
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.message.success {
    background: var(--success-color);
}

.message.error {
    background: var(--error-color);
}

.message.warning {
    background: var(--warning-color);
}

.message.info {
    background: var(--primary-color);
}

/* 响应式紧凑模式 */
.compact-mode .input-grid {
    grid-template-columns: 1fr;
}

.compact-mode .fields-grid {
    grid-template-columns: 1fr;
}

.compact-mode .personal-info-column {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
}

.compact-mode .secondary-actions {
    grid-template-columns: repeat(2, 1fr);
}

.compact-mode .status-indicators {
    flex-direction: column;
    gap: var(--spacing-xs);
}

/* ==========================================
   城市查看器样式
   ========================================== */

.city-viewer-area {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.city-viewer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 1px solid var(--border-color);
}

.viewer-icon {
    font-size: 18px;
    margin-right: var(--spacing-sm);
}

.viewer-title {
    font-weight: 600;
    font-size: 16px;
}

.close-viewer-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    transition: background-color 0.2s;
}

.close-viewer-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 搜索和过滤区域 */
.city-search-section {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.search-box {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.search-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    background: var(--bg-primary);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background: var(--primary-hover);
}

.filter-section {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.state-filter {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    font-size: 14px;
    cursor: pointer;
    min-width: 150px;
}

.show-popular-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--warning-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.show-popular-btn:hover {
    background: #d97706;
}

/* 城市列表容器 */
.city-list-container {
    max-height: 400px;
    overflow-y: auto;
}

.city-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.list-stats {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
}

.view-options {
    display: flex;
    gap: var(--spacing-xs);
}

.view-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s;
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.view-btn:hover:not(.active) {
    background: var(--bg-accent);
}

/* 城市列表 */
.city-list {
    padding: var(--spacing-sm);
}

.city-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    transition: all 0.2s;
    cursor: pointer;
}

.city-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.city-item.popular {
    border-left: 4px solid var(--warning-color);
    background: linear-gradient(90deg, #fffbeb 0%, #ffffff 100%);
}

.city-info {
    flex: 1;
}

.city-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
    font-size: 15px;
}

.city-chinese {
    color: var(--error-color);
    font-style: italic;
    margin-bottom: 4px;
    font-size: 13px;
}

.city-details {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    gap: var(--spacing-md);
}

.city-state {
    color: var(--primary-color);
    font-weight: 500;
}

.city-postcode {
    color: var(--text-muted);
}

.city-code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 50px;
    text-align: center;
}

/* 网格视图样式 */
.city-list.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md);
}

.city-list.grid-view .city-item {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    padding: var(--spacing-lg);
}

.city-list.grid-view .city-info {
    margin-bottom: var(--spacing-sm);
}

.city-list.grid-view .city-details {
    justify-content: center;
    flex-wrap: wrap;
}

.city-list.grid-view .city-code {
    align-self: center;
    margin-top: var(--spacing-sm);
}

/* 城市卡片样式 */
.city-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.city-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.city-card-header {
    margin-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
    padding-bottom: var(--spacing-sm);
}

.city-card .city-name {
    text-align: center;
}

.city-card .name-en {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--text-primary);
    display: block;
    margin-bottom: 4px;
}

.city-card .name-zh {
    font-size: 0.9em;
    color: var(--text-secondary);
    display: block;
}

.city-card-body {
    flex: 1;
    margin-bottom: var(--spacing-md);
}

.city-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    padding: 4px 0;
}

.info-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.9em;
}

.info-value {
    color: var(--text-primary);
    font-size: 0.9em;
    text-align: right;
}

.city-card-footer {
    margin-top: auto;
    text-align: center;
}

.use-city-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.use-city-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 空状态 */
.city-list-empty {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.no-cities {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
    font-style: italic;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    margin: var(--spacing-md) 0;
}

.no-cities::before {
    content: "🏙️";
    display: block;
    font-size: 2em;
    margin-bottom: var(--spacing-sm);
    opacity: 0.5;
}

/* 加载状态 */
.city-list-loading {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading-spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .city-search-section {
        padding: var(--spacing-sm);
    }
    
    .filter-section {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .state-filter {
        min-width: auto;
    }
    
    .city-list.grid-view {
        grid-template-columns: 1fr;
    }
    
    .city-item {
        padding: var(--spacing-sm);
    }
    
    .city-details {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

/* ==========================================
   数据预览样式
   ========================================== */

.data-preview-container {
    max-width: 100%;
    font-family: inherit;
}

.data-preview-container h3 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.preview-category {
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-primary);
}

.preview-category h4 {
    margin: 0;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.preview-item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-md);
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-item strong {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
    min-width: 80px;
    flex-shrink: 0;
}

.preview-value {
    color: var(--text-primary);
    font-size: 13px;
    text-align: right;
    word-break: break-word;
    flex: 1;
}

.preview-actions {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 自动解析状态样式 */
.auto-parse-status {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm) var(--spacing-md);
    margin-top: var(--spacing-sm);
    animation: fadeIn 0.3s ease-in;
}

.countdown-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 13px;
    color: var(--text-secondary);
}

.countdown-icon {
    font-size: 14px;
    animation: pulse 1s infinite;
}

.countdown-text {
    flex: 1;
    font-weight: 500;
}

.cancel-auto-parse {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px var(--spacing-sm);
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.cancel-auto-parse:hover {
    background: #dc2626;
}

/* 自动解析切换开关样式 */
.auto-parse-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-secondary);
    cursor: pointer;
    margin-bottom: var(--spacing-sm);
}

.auto-parse-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 40px;
    height: 20px;
    background: var(--bg-accent);
    border-radius: 20px;
    transition: background-color 0.3s;
    cursor: pointer;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.auto-parse-toggle input[type="checkbox"]:checked + .toggle-slider {
    background: var(--success-color);
}

.auto-parse-toggle input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(20px);
}

/* 解析按钮组样式 */
.parse-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.parse-buttons .parse-btn {
    flex: 1;
    margin-top: 0;
}