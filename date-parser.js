/**
 * 增强日期解析器
 * 支持多种日期格式，包括相对日期和自然语言日期
 */

class EnhancedDateParser {
  constructor() {
    this.supportedFormats = [
      // 标准格式
      'DD/MM/YYYY',
      'MM/DD/YYYY', 
      'YYYY-MM-DD',
      'YYYY/MM/DD',
      'DD-MM-YYYY',
      'MM-DD-YYYY',
      
      // 带时间的格式
      'DD/MM/YYYY HH:mm',
      'YYYY-MM-DD HH:mm:ss',
      
      // 月份名称格式
      'DD MMM YYYY',
      'MMM DD, YYYY',
      'MMMM DD, YYYY',
      'DD MMMM YYYY',
      
      // 简短格式
      'DD/MM/YY',
      'MM/DD/YY',
      'YY-MM-DD',
      
      // ISO格式
      'YYYY-MM-DDTHH:mm:ss.sssZ',
      'YYYY-MM-DDTHH:mm:ssZ'
    ];
    
    this.monthNames = {
      en: {
        full: ['January', 'February', 'March', 'April', 'May', 'June',
               'July', 'August', 'September', 'October', 'November', 'December'],
        short: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
      },
      zh: {
        full: ['一月', '二月', '三月', '四月', '五月', '六月',
               '七月', '八月', '九月', '十月', '十一月', '十二月'],
        short: ['1月', '2月', '3月', '4月', '5月', '6月',
                '7月', '8月', '9月', '10月', '11月', '12月']
      }
    };
    
    this.relativePatterns = {
      en: {
        today: /^today$/i,
        tomorrow: /^tomorrow$/i,
        yesterday: /^yesterday$/i,
        nextWeek: /^next\s+week$/i,
        lastWeek: /^last\s+week$/i,
        nextMonth: /^next\s+month$/i,
        lastMonth: /^last\s+month$/i,
        nextYear: /^next\s+year$/i,
        lastYear: /^last\s+year$/i,
        inDays: /^in\s+(\d+)\s+days?$/i,
        daysAgo: /^(\d+)\s+days?\s+ago$/i,
        inWeeks: /^in\s+(\d+)\s+weeks?$/i,
        weeksAgo: /^(\d+)\s+weeks?\s+ago$/i,
        inMonths: /^in\s+(\d+)\s+months?$/i,
        monthsAgo: /^(\d+)\s+months?\s+ago$/i,
        inYears: /^in\s+(\d+)\s+years?$/i,
        yearsAgo: /^(\d+)\s+years?\s+ago$/i
      },
      zh: {
        today: /^今天|今日$/,
        tomorrow: /^明天|明日$/,
        yesterday: /^昨天|昨日$/,
        nextWeek: /^下周|下星期$/,
        lastWeek: /^上周|上星期$/,
        nextMonth: /^下月|下个月$/,
        lastMonth: /^上月|上个月$/,
        nextYear: /^明年|下年$/,
        lastYear: /^去年|上年$/,
        inDays: /^(\d+)天后$/,
        daysAgo: /^(\d+)天前$/,
        inWeeks: /^(\d+)周后|(\d+)星期后$/,
        weeksAgo: /^(\d+)周前|(\d+)星期前$/,
        inMonths: /^(\d+)个?月后$/,
        monthsAgo: /^(\d+)个?月前$/,
        inYears: /^(\d+)年后$/,
        yearsAgo: /^(\d+)年前$/
      }
    };
  }

  /**
   * 解析日期字符串
   */
  parseDate(dateStr) {
    if (!dateStr || typeof dateStr !== 'string') {
      return null;
    }

    const trimmedStr = dateStr.trim();
    
    // 1. 尝试相对日期解析
    const relativeDate = this.parseRelativeDate(trimmedStr);
    if (relativeDate) {
      return relativeDate;
    }
    
    // 2. 尝试自然语言日期解析
    const naturalDate = this.parseNaturalLanguageDate(trimmedStr);
    if (naturalDate) {
      return naturalDate;
    }
    
    // 3. 尝试标准格式解析
    const standardDate = this.parseStandardFormats(trimmedStr);
    if (standardDate) {
      return standardDate;
    }
    
    // 4. 尝试JavaScript原生解析
    const nativeDate = this.parseNativeDate(trimmedStr);
    if (nativeDate) {
      return nativeDate;
    }
    
    console.warn(`⚠️ 无法解析日期: "${dateStr}"`);
    return null;
  }

  /**
   * 解析相对日期
   */
  parseRelativeDate(dateStr) {
    const now = new Date();
    
    // 英文相对日期
    for (const [key, pattern] of Object.entries(this.relativePatterns.en)) {
      const match = dateStr.match(pattern);
      if (match) {
        return this.calculateRelativeDate(now, key, match);
      }
    }
    
    // 中文相对日期
    for (const [key, pattern] of Object.entries(this.relativePatterns.zh)) {
      const match = dateStr.match(pattern);
      if (match) {
        return this.calculateRelativeDate(now, key, match);
      }
    }
    
    return null;
  }

  /**
   * 计算相对日期
   */
  calculateRelativeDate(baseDate, type, match) {
    const date = new Date(baseDate);
    
    switch (type) {
      case 'today':
        return date;
        
      case 'tomorrow':
        date.setDate(date.getDate() + 1);
        return date;
        
      case 'yesterday':
        date.setDate(date.getDate() - 1);
        return date;
        
      case 'nextWeek':
        date.setDate(date.getDate() + 7);
        return date;
        
      case 'lastWeek':
        date.setDate(date.getDate() - 7);
        return date;
        
      case 'nextMonth':
        date.setMonth(date.getMonth() + 1);
        return date;
        
      case 'lastMonth':
        date.setMonth(date.getMonth() - 1);
        return date;
        
      case 'nextYear':
        date.setFullYear(date.getFullYear() + 1);
        return date;
        
      case 'lastYear':
        date.setFullYear(date.getFullYear() - 1);
        return date;
        
      case 'inDays':
        const daysForward = parseInt(match[1] || match[2]);
        date.setDate(date.getDate() + daysForward);
        return date;
        
      case 'daysAgo':
        const daysBack = parseInt(match[1] || match[2]);
        date.setDate(date.getDate() - daysBack);
        return date;
        
      case 'inWeeks':
        const weeksForward = parseInt(match[1] || match[2]);
        date.setDate(date.getDate() + (weeksForward * 7));
        return date;
        
      case 'weeksAgo':
        const weeksBack = parseInt(match[1] || match[2]);
        date.setDate(date.getDate() - (weeksBack * 7));
        return date;
        
      case 'inMonths':
        const monthsForward = parseInt(match[1] || match[2]);
        date.setMonth(date.getMonth() + monthsForward);
        return date;
        
      case 'monthsAgo':
        const monthsBack = parseInt(match[1] || match[2]);
        date.setMonth(date.getMonth() - monthsBack);
        return date;
        
      case 'inYears':
        const yearsForward = parseInt(match[1] || match[2]);
        date.setFullYear(date.getFullYear() + yearsForward);
        return date;
        
      case 'yearsAgo':
        const yearsBack = parseInt(match[1] || match[2]);
        date.setFullYear(date.getFullYear() - yearsBack);
        return date;
        
      default:
        return null;
    }
  }

  /**
   * 解析自然语言日期
   */
  parseNaturalLanguageDate(dateStr) {
    // 匹配 "2025年8月1日" 格式
    const chineseDatePattern = /^(\d{4})年(\d{1,2})月(\d{1,2})日?$/;
    const chineseMatch = dateStr.match(chineseDatePattern);
    if (chineseMatch) {
      const year = parseInt(chineseMatch[1]);
      const month = parseInt(chineseMatch[2]) - 1; // JavaScript月份从0开始
      const day = parseInt(chineseMatch[3]);
      return new Date(year, month, day);
    }
    
    // 匹配 "August 1, 2025" 格式
    const englishDatePattern = /^(\w+)\s+(\d{1,2}),?\s+(\d{4})$/;
    const englishMatch = dateStr.match(englishDatePattern);
    if (englishMatch) {
      const monthName = englishMatch[1];
      const day = parseInt(englishMatch[2]);
      const year = parseInt(englishMatch[3]);
      
      const monthIndex = this.getMonthIndex(monthName);
      if (monthIndex !== -1) {
        return new Date(year, monthIndex, day);
      }
    }
    
    // 匹配 "1st August 2025" 格式
    const ordinalDatePattern = /^(\d{1,2})(st|nd|rd|th)\s+(\w+)\s+(\d{4})$/i;
    const ordinalMatch = dateStr.match(ordinalDatePattern);
    if (ordinalMatch) {
      const day = parseInt(ordinalMatch[1]);
      const monthName = ordinalMatch[3];
      const year = parseInt(ordinalMatch[4]);
      
      const monthIndex = this.getMonthIndex(monthName);
      if (monthIndex !== -1) {
        return new Date(year, monthIndex, day);
      }
    }
    
    return null;
  }

  /**
   * 获取月份索引
   */
  getMonthIndex(monthName) {
    const lowerMonthName = monthName.toLowerCase();
    
    // 英文月份
    const enFullIndex = this.monthNames.en.full.findIndex(m => m.toLowerCase() === lowerMonthName);
    if (enFullIndex !== -1) return enFullIndex;
    
    const enShortIndex = this.monthNames.en.short.findIndex(m => m.toLowerCase() === lowerMonthName);
    if (enShortIndex !== -1) return enShortIndex;
    
    // 中文月份
    const zhFullIndex = this.monthNames.zh.full.findIndex(m => m === monthName);
    if (zhFullIndex !== -1) return zhFullIndex;
    
    const zhShortIndex = this.monthNames.zh.short.findIndex(m => m === monthName);
    if (zhShortIndex !== -1) return zhShortIndex;
    
    return -1;
  }

  /**
   * 解析标准格式
   */
  parseStandardFormats(dateStr) {
    // DD/MM/YYYY 格式
    const ddmmyyyyPattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const ddmmyyyyMatch = dateStr.match(ddmmyyyyPattern);
    if (ddmmyyyyMatch) {
      const day = parseInt(ddmmyyyyMatch[1]);
      const month = parseInt(ddmmyyyyMatch[2]) - 1;
      const year = parseInt(ddmmyyyyMatch[3]);
      
      // 验证日期有效性
      const date = new Date(year, month, day);
      if (date.getDate() === day && date.getMonth() === month && date.getFullYear() === year) {
        return date;
      }
    }
    
    // YYYY-MM-DD 格式
    const yyyymmddPattern = /^(\d{4})-(\d{1,2})-(\d{1,2})$/;
    const yyyymmddMatch = dateStr.match(yyyymmddPattern);
    if (yyyymmddMatch) {
      const year = parseInt(yyyymmddMatch[1]);
      const month = parseInt(yyyymmddMatch[2]) - 1;
      const day = parseInt(yyyymmddMatch[3]);
      
      const date = new Date(year, month, day);
      if (date.getDate() === day && date.getMonth() === month && date.getFullYear() === year) {
        return date;
      }
    }
    
    // MM/DD/YYYY 格式
    const mmddyyyyPattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const mmddyyyyMatch = dateStr.match(mmddyyyyPattern);
    if (mmddyyyyMatch) {
      const month = parseInt(mmddyyyyMatch[1]) - 1;
      const day = parseInt(mmddyyyyMatch[2]);
      const year = parseInt(mmddyyyyMatch[3]);
      
      // 只有当月份 <= 12 且日期 > 12 时才认为是MM/DD/YYYY格式
      if (month < 12 && day > 12) {
        const date = new Date(year, month, day);
        if (date.getDate() === day && date.getMonth() === month && date.getFullYear() === year) {
          return date;
        }
      }
    }
    
    return null;
  }

  /**
   * 使用JavaScript原生解析
   */
  parseNativeDate(dateStr) {
    try {
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        return date;
      }
    } catch (error) {
      // 忽略解析错误
    }
    
    return null;
  }

  /**
   * 格式化日期为MDAC要求的DD/MM/YYYY格式
   */
  formatForMDAC(date) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return '';
    }
    
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  }

  /**
   * 智能日期解析和格式化
   */
  smartParse(dateStr) {
    console.log(`🔍 智能解析日期: "${dateStr}"`);
    
    const parsedDate = this.parseDate(dateStr);
    if (parsedDate) {
      const formatted = this.formatForMDAC(parsedDate);
      console.log(`✅ 解析成功: "${dateStr}" -> ${formatted}`);
      return formatted;
    } else {
      console.warn(`❌ 解析失败: "${dateStr}"`);
      return dateStr; // 返回原始字符串
    }
  }

  /**
   * 获取支持的格式列表
   */
  getSupportedFormats() {
    return {
      standard: this.supportedFormats,
      relative: {
        english: Object.keys(this.relativePatterns.en),
        chinese: Object.keys(this.relativePatterns.zh)
      },
      natural: [
        'YYYY年MM月DD日',
        'Month DD, YYYY',
        'DDth Month YYYY',
        '中文月份名称'
      ]
    };
  }

  /**
   * 测试日期解析器
   */
  runTests() {
    console.log('🧪 运行日期解析器测试...');
    
    const testCases = [
      // 标准格式
      '31/12/2030',
      '2030-12-31',
      '12/31/2030',
      
      // 相对日期 - 英文
      'today',
      'tomorrow',
      'yesterday',
      'next week',
      'in 7 days',
      '3 days ago',
      'next month',
      'in 2 months',
      
      // 相对日期 - 中文
      '今天',
      '明天',
      '昨天',
      '下周',
      '7天后',
      '3天前',
      '下个月',
      '2个月后',
      
      // 自然语言
      '2025年8月1日',
      'August 1, 2025',
      '1st August 2025',
      
      // 无效格式
      'invalid date',
      '32/13/2025',
      ''
    ];
    
    const results = {
      success: [],
      failure: []
    };
    
    testCases.forEach(testCase => {
      try {
        const result = this.smartParse(testCase);
        if (result && result !== testCase) {
          results.success.push({ input: testCase, output: result });
        } else {
          results.failure.push({ input: testCase, reason: '解析失败或返回原值' });
        }
      } catch (error) {
        results.failure.push({ input: testCase, reason: error.message });
      }
    });
    
    console.log('📊 测试结果:');
    console.log(`✅ 成功: ${results.success.length}`);
    console.log(`❌ 失败: ${results.failure.length}`);
    
    if (results.success.length > 0) {
      console.log('\n✅ 成功解析的日期:');
      results.success.forEach(result => {
        console.log(`   "${result.input}" -> "${result.output}"`);
      });
    }
    
    if (results.failure.length > 0) {
      console.log('\n❌ 解析失败的日期:');
      results.failure.forEach(failure => {
        console.log(`   "${failure.input}": ${failure.reason}`);
      });
    }
    
    return results;
  }
}

// 创建全局日期解析器实例
window.EnhancedDateParser = EnhancedDateParser;
const dateParser = new EnhancedDateParser();
window.dateParser = dateParser;

console.log('📅 增强日期解析器已加载');
console.log('使用 window.dateParser.smartParse(dateStr) 解析日期');
console.log('使用 window.dateParser.runTests() 运行测试');
