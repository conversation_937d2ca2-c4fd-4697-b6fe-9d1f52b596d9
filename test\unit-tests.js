/**
 * MDAC AI Chrome扩展单元测试套件
 * 
 * <AUTHOR> AI Team
 * @version 3.1.0
 */

class MDACTestSuite {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0
        };
        this.startTime = null;
        this.endTime = null;
    }

    /**
     * 添加测试用例
     * @param {string} name - 测试名称
     * @param {Function} testFunction - 测试函数
     */
    addTest(name, testFunction) {
        this.tests.push({
            name: name,
            test: testFunction,
            status: 'pending'
        });
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 [MDACTestSuite] 开始运行单元测试...');
        this.startTime = Date.now();
        
        this.results.total = this.tests.length;
        
        for (const testCase of this.tests) {
            await this.runSingleTest(testCase);
        }
        
        this.endTime = Date.now();
        this.generateReport();
    }

    /**
     * 运行单个测试
     * @param {Object} testCase - 测试用例
     */
    async runSingleTest(testCase) {
        try {
            console.log(`🔍 运行测试: ${testCase.name}`);
            
            const result = await testCase.test();
            
            if (result === true || result === undefined) {
                testCase.status = 'passed';
                this.results.passed++;
                console.log(`✅ 测试通过: ${testCase.name}`);
            } else {
                testCase.status = 'failed';
                testCase.error = result || '测试返回false';
                this.results.failed++;
                console.log(`❌ 测试失败: ${testCase.name} - ${testCase.error}`);
            }
        } catch (error) {
            testCase.status = 'failed';
            testCase.error = error.message;
            this.results.failed++;
            console.log(`❌ 测试异常: ${testCase.name} - ${error.message}`);
        }
    }

    /**
     * 生成测试报告
     */
    generateReport() {
        const duration = this.endTime - this.startTime;
        const successRate = (this.results.passed / this.results.total * 100).toFixed(1);
        
        console.log('\n📊 测试报告');
        console.log('='.repeat(50));
        console.log(`总测试数: ${this.results.total}`);
        console.log(`通过: ${this.results.passed}`);
        console.log(`失败: ${this.results.failed}`);
        console.log(`成功率: ${successRate}%`);
        console.log(`耗时: ${duration}ms`);
        console.log('='.repeat(50));
        
        // 显示失败的测试
        if (this.results.failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.tests.filter(t => t.status === 'failed').forEach(test => {
                console.log(`  - ${test.name}: ${test.error}`);
            });
        }
        
        return {
            summary: this.results,
            successRate: successRate + '%',
            duration: duration,
            details: this.tests
        };
    }

    /**
     * 断言函数
     */
    static assert = {
        /**
         * 断言为真
         */
        isTrue(value, message = '期望值为true') {
            if (value !== true) {
                throw new Error(`${message} - 实际值: ${value}`);
            }
        },

        /**
         * 断言为假
         */
        isFalse(value, message = '期望值为false') {
            if (value !== false) {
                throw new Error(`${message} - 实际值: ${value}`);
            }
        },

        /**
         * 断言相等
         */
        equals(actual, expected, message = '期望值相等') {
            if (actual !== expected) {
                throw new Error(`${message} - 期望: ${expected}, 实际: ${actual}`);
            }
        },

        /**
         * 断言不相等
         */
        notEquals(actual, expected, message = '期望值不相等') {
            if (actual === expected) {
                throw new Error(`${message} - 值: ${actual}`);
            }
        },

        /**
         * 断言存在
         */
        exists(value, message = '期望值存在') {
            if (value === null || value === undefined) {
                throw new Error(`${message} - 实际值: ${value}`);
            }
        },

        /**
         * 断言不存在
         */
        notExists(value, message = '期望值不存在') {
            if (value !== null && value !== undefined) {
                throw new Error(`${message} - 实际值: ${value}`);
            }
        },

        /**
         * 断言类型
         */
        isType(value, expectedType, message = '期望类型匹配') {
            const actualType = typeof value;
            if (actualType !== expectedType) {
                throw new Error(`${message} - 期望类型: ${expectedType}, 实际类型: ${actualType}`);
            }
        },

        /**
         * 断言包含
         */
        contains(container, item, message = '期望包含项目') {
            if (Array.isArray(container)) {
                if (!container.includes(item)) {
                    throw new Error(`${message} - 数组不包含: ${item}`);
                }
            } else if (typeof container === 'string') {
                if (!container.includes(item)) {
                    throw new Error(`${message} - 字符串不包含: ${item}`);
                }
            } else {
                throw new Error(`${message} - 不支持的容器类型: ${typeof container}`);
            }
        },

        /**
         * 断言抛出异常
         */
        async throws(asyncFunction, message = '期望抛出异常') {
            try {
                await asyncFunction();
                throw new Error(`${message} - 函数未抛出异常`);
            } catch (error) {
                // 期望的异常
                if (error.message.includes('期望抛出异常')) {
                    throw error;
                }
            }
        }
    };
}

// 创建具体的测试用例
function createMDACTests() {
    const testSuite = new MDACTestSuite();
    const assert = MDACTestSuite.assert;

    // 测试性能工具
    testSuite.addTest('PerformanceUtils - 防抖功能', () => {
        if (!window.PerformanceUtils) {
            throw new Error('PerformanceUtils未加载');
        }

        let callCount = 0;
        const testFunc = () => { callCount++; };
        const debouncedFunc = window.PerformanceUtils.debounce(testFunc, 100);

        // 快速调用多次
        debouncedFunc();
        debouncedFunc();
        debouncedFunc();

        // 立即检查，应该还没有执行
        assert.equals(callCount, 0, '防抖函数应该延迟执行');

        return new Promise(resolve => {
            setTimeout(() => {
                assert.equals(callCount, 1, '防抖函数应该只执行一次');
                resolve();
            }, 150);
        });
    });

    testSuite.addTest('PerformanceUtils - 内存管理器', () => {
        if (!window.PerformanceUtils) {
            throw new Error('PerformanceUtils未加载');
        }

        const memoryManager = new window.PerformanceUtils.MemoryManager();
        assert.exists(memoryManager, '内存管理器应该存在');
        assert.isType(memoryManager.addTimer, 'function', 'addTimer应该是函数');
        assert.isType(memoryManager.cleanup, 'function', 'cleanup应该是函数');

        // 测试统计功能
        const stats = memoryManager.getStats();
        assert.isType(stats, 'object', '统计信息应该是对象');
        assert.exists(stats.timers, '统计信息应该包含timers');
        assert.exists(stats.listeners, '统计信息应该包含listeners');
    });

    testSuite.addTest('UIEnhancements - 消息系统', () => {
        if (!window.UIEnhancements) {
            throw new Error('UIEnhancements未加载');
        }

        const messageSystem = new window.UIEnhancements.MessageSystem();
        assert.exists(messageSystem, '消息系统应该存在');

        // 测试显示消息
        const messageId = messageSystem.show('success', '测试消息', { duration: 1000 });
        assert.exists(messageId, '消息ID应该存在');
        assert.isType(messageId, 'string', '消息ID应该是字符串');

        // 测试消息容器是否创建
        const container = document.getElementById('mdac-message-container');
        assert.exists(container, '消息容器应该被创建');
    });

    testSuite.addTest('UIEnhancements - 加载管理器', () => {
        if (!window.UIEnhancements) {
            throw new Error('UIEnhancements未加载');
        }

        const loadingManager = new window.UIEnhancements.LoadingManager();
        assert.exists(loadingManager, '加载管理器应该存在');

        // 测试显示加载状态
        const loader = loadingManager.show('test', '测试加载...', { timeout: 1000 });
        assert.exists(loader, '加载器应该存在');
        assert.isType(loader.hide, 'function', '加载器应该有hide方法');
        assert.isType(loader.updateText, 'function', '加载器应该有updateText方法');

        // 清理
        loader.hide();
    });

    testSuite.addTest('AutoParseManager - 基本功能', () => {
        if (!window.AutoParseManager) {
            throw new Error('AutoParseManager未加载');
        }

        const memoryManager = new window.PerformanceUtils.MemoryManager();
        const errorHandler = window.PerformanceUtils.ErrorHandler;
        const messageSystem = new window.UIEnhancements.MessageSystem();

        const autoParseManager = new window.AutoParseManager(memoryManager, errorHandler, messageSystem);
        assert.exists(autoParseManager, 'AutoParseManager应该存在');
        assert.isType(autoParseManager.handleAutoParseInput, 'function', '应该有handleAutoParseInput方法');
        assert.isType(autoParseManager.cancelAutoParse, 'function', '应该有cancelAutoParse方法');
    });

    testSuite.addTest('CitySearchManager - 基本功能', () => {
        if (!window.CitySearchManager) {
            throw new Error('CitySearchManager未加载');
        }

        const memoryManager = new window.PerformanceUtils.MemoryManager();
        const errorHandler = window.PerformanceUtils.ErrorHandler;
        const messageSystem = new window.UIEnhancements.MessageSystem();

        const citySearchManager = new window.CitySearchManager(memoryManager, errorHandler, messageSystem);
        assert.exists(citySearchManager, 'CitySearchManager应该存在');
        assert.isType(citySearchManager.fuzzySearchCities, 'function', '应该有fuzzySearchCities方法');
        assert.isType(citySearchManager.addCitySearchFunctionality, 'function', '应该有addCitySearchFunctionality方法');
    });

    testSuite.addTest('CitySearchManager - 模糊搜索', () => {
        if (!window.CitySearchManager) {
            throw new Error('CitySearchManager未加载');
        }

        const memoryManager = new window.PerformanceUtils.MemoryManager();
        const errorHandler = window.PerformanceUtils.ErrorHandler;
        const messageSystem = new window.UIEnhancements.MessageSystem();

        const citySearchManager = new window.CitySearchManager(memoryManager, errorHandler, messageSystem);
        
        // 测试数据
        const testCities = [
            { name: '吉隆坡', nameEn: 'Kuala Lumpur', code: 'KL001' },
            { name: '槟城', nameEn: 'Penang', code: 'PG001' },
            { name: '新山', nameEn: 'Johor Bahru', code: 'JB001' }
        ];

        // 测试搜索功能
        const results1 = citySearchManager.fuzzySearchCities('kuala', testCities);
        assert.equals(results1.length, 1, '搜索"kuala"应该返回1个结果');
        assert.equals(results1[0].code, 'KL001', '应该返回吉隆坡');

        const results2 = citySearchManager.fuzzySearchCities('', testCities);
        assert.equals(results2.length, 3, '空搜索应该返回所有结果');
    });

    testSuite.addTest('AIParseManager - 基本功能', () => {
        if (!window.AIParseManager) {
            throw new Error('AIParseManager未加载');
        }

        const memoryManager = new window.PerformanceUtils.MemoryManager();
        const errorHandler = window.PerformanceUtils.ErrorHandler;
        const messageSystem = new window.UIEnhancements.MessageSystem();
        const loadingManager = new window.UIEnhancements.LoadingManager();

        const aiParseManager = new window.AIParseManager(memoryManager, errorHandler, messageSystem, loadingManager);
        assert.exists(aiParseManager, 'AIParseManager应该存在');
        assert.isType(aiParseManager.parsePersonalInfo, 'function', '应该有parsePersonalInfo方法');
        assert.isType(aiParseManager.parseTravelInfo, 'function', '应该有parseTravelInfo方法');
        assert.isType(aiParseManager.getStats, 'function', '应该有getStats方法');
    });

    return testSuite;
}

// 运行测试的函数
async function runMDACTests() {
    console.log('🚀 开始MDAC单元测试...');
    
    try {
        const testSuite = createMDACTests();
        const report = await testSuite.runAllTests();
        
        console.log('\n🎉 测试完成！');
        return report;
    } catch (error) {
        console.error('❌ 测试运行失败:', error);
        return null;
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MDACTestSuite, createMDACTests, runMDACTests };
} else {
    window.MDACTestSuite = MDACTestSuite;
    window.createMDACTests = createMDACTests;
    window.runMDACTests = runMDACTests;
}
