/**
 * MDAC字段修复测试脚本
 * 用于验证字段修复的有效性
 */

// 测试数据
const TEST_DATA = {
  passExpDte: '31/12/2030',
  region: '+60',
  mobile: '167372551',
  arrDt: '01/08/2025',
  depDt: '15/08/2025',
  embark: 'KLIA'
};

/**
 * 运行字段修复测试
 */
async function runFieldFixTest() {
  console.log('🧪 开始MDAC字段修复测试...');
  console.log('='.repeat(60));
  
  // 1. 查找问题字段
  const problemFields = findProblemFields();
  
  // 2. 测试修复前的填充
  await testBeforeFix(problemFields);
  
  // 3. 应用修复
  await applyFixes(problemFields);
  
  // 4. 测试修复后的填充
  await testAfterFix(problemFields);
  
  // 5. 生成测试报告
  generateTestReport();
  
  console.log('🎉 MDAC字段修复测试完成');
}

/**
 * 查找问题字段
 */
function findProblemFields() {
  console.log('🔍 查找问题字段...');
  
  const problemFieldIds = [
    'passExpDte',  // 护照到期日期
    'region',      // 国家代码
    'mobile',      // 手机号码
    'arrDt',       // 到达日期
    'depDt',       // 离开日期
    'embark'       // 最后登机港口
  ];
  
  const foundFields = {};
  
  problemFieldIds.forEach(fieldId => {
    const element = document.getElementById(fieldId);
    if (element) {
      foundFields[fieldId] = {
        element,
        type: element.tagName.toLowerCase() + (element.type ? `[${element.type}]` : ''),
        originalValue: element.value
      };
      console.log(`✅ 找到字段 ${fieldId}: ${foundFields[fieldId].type}`);
    } else {
      console.warn(`⚠️ 未找到字段 ${fieldId}`);
    }
  });
  
  console.log(`📊 找到 ${Object.keys(foundFields).length}/${problemFieldIds.length} 个问题字段`);
  return foundFields;
}

/**
 * 测试修复前的填充
 */
async function testBeforeFix(problemFields) {
  console.log('\n🧪 测试修复前的填充...');
  
  const results = {
    success: [],
    failure: []
  };
  
  for (const [fieldId, field] of Object.entries(problemFields)) {
    const testValue = TEST_DATA[fieldId];
    if (!testValue) continue;
    
    console.log(`🧪 测试填充字段 ${fieldId}: "${testValue}"`);
    
    try {
      // 保存原始值
      const originalValue = field.element.value;
      
      // 基本填充
      field.element.value = testValue;
      field.element.dispatchEvent(new Event('input', { bubbles: true }));
      field.element.dispatchEvent(new Event('change', { bubbles: true }));
      
      // 等待可能的异步验证
      await delay(300);
      
      // 检查填充结果
      if (field.element.value === testValue) {
        results.success.push(fieldId);
        console.log(`✅ 字段 ${fieldId} 基本填充成功`);
      } else {
        results.failure.push({
          fieldId,
          expected: testValue,
          actual: field.element.value
        });
        console.warn(`❌ 字段 ${fieldId} 基本填充失败: 期望"${testValue}", 实际"${field.element.value}"`);
      }
      
      // 恢复原始值
      field.element.value = originalValue;
    } catch (error) {
      results.failure.push({
        fieldId,
        error: error.message
      });
      console.error(`❌ 字段 ${fieldId} 填充异常:`, error);
    }
  }
  
  console.log(`📊 修复前填充测试结果: ${results.success.length} 成功, ${results.failure.length} 失败`);
  window.beforeFixResults = results;
  return results;
}

/**
 * 应用修复
 */
async function applyFixes(problemFields) {
  console.log('\n🔧 应用字段修复...');
  
  // 创建修复工具实例
  if (!window.MDACFieldFixer) {
    console.log('🔧 加载字段修复工具...');
    
    // 注入修复工具
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('field-fix-solutions.js');
    document.head.appendChild(script);
    
    // 等待工具加载
    await new Promise(resolve => {
      script.onload = () => {
        console.log('✅ 字段修复工具加载完成');
        resolve();
      };
      script.onerror = () => {
        console.error('❌ 字段修复工具加载失败');
        resolve();
      };
    });
    
    // 等待工具初始化
    if (window.MDACFieldFixer) {
      await window.MDACFieldFixer.init();
    }
  }
  
  console.log('✅ 修复工具准备就绪');
}

/**
 * 测试修复后的填充
 */
async function testAfterFix(problemFields) {
  console.log('\n🧪 测试修复后的填充...');
  
  const results = {
    success: [],
    failure: []
  };
  
  // 如果修复工具可用，使用修复工具测试
  if (window.MDACFieldFixer) {
    const fixResults = await window.MDACFieldFixer.fixAllFields(TEST_DATA);
    window.afterFixResults = fixResults;
    return fixResults;
  }
  
  // 否则使用内置的修复方法测试
  for (const [fieldId, field] of Object.entries(problemFields)) {
    const testValue = TEST_DATA[fieldId];
    if (!testValue) continue;
    
    console.log(`🧪 测试修复后填充字段 ${fieldId}: "${testValue}"`);
    
    try {
      // 保存原始值
      const originalValue = field.element.value;
      
      // 根据字段类型选择修复方法
      let success = false;
      
      switch (fieldId) {
        case 'passExpDte':
          success = await fixPassportExpiryField(field.element, testValue);
          break;
          
        case 'region':
          success = await fixRegionField(field.element, testValue);
          break;
          
        case 'mobile':
          success = await fixMobileField(field.element, testValue);
          break;
          
        case 'arrDt':
          success = await fixArrivalDateField(field.element, testValue);
          break;
          
        case 'depDt':
          success = await fixDepartureDateField(field.element, testValue);
          break;
          
        case 'embark':
          success = await fixEmbarkField(field.element, testValue);
          break;
      }
      
      if (success) {
        results.success.push(fieldId);
        console.log(`✅ 字段 ${fieldId} 修复后填充成功`);
      } else {
        results.failure.push({
          fieldId,
          reason: '修复方法未能成功填充字段'
        });
        console.warn(`❌ 字段 ${fieldId} 修复后填充失败`);
      }
      
      // 恢复原始值
      field.element.value = originalValue;
    } catch (error) {
      results.failure.push({
        fieldId,
        reason: error.message
      });
      console.error(`❌ 字段 ${fieldId} 修复后填充异常:`, error);
    }
  }
  
  console.log(`📊 修复后填充测试结果: ${results.success.length} 成功, ${results.failure.length} 失败`);
  window.afterFixResults = results;
  return results;
}

/**
 * 生成测试报告
 */
function generateTestReport() {
  console.log('\n📊 MDAC字段修复测试报告');
  console.log('='.repeat(60));
  
  const beforeResults = window.beforeFixResults || { success: [], failure: [] };
  const afterResults = window.afterFixResults || { success: [], failure: [] };
  
  const beforeSuccessRate = (beforeResults.success.length / (beforeResults.success.length + beforeResults.failure.length) * 100).toFixed(1);
  const afterSuccessRate = (afterResults.success.length / (afterResults.success.length + afterResults.failure.length) * 100).toFixed(1);
  
  console.log(`📈 修复前填充成功率: ${beforeSuccessRate}%`);
  console.log(`📈 修复后填充成功率: ${afterSuccessRate}%`);
  console.log(`📊 改进: ${(afterSuccessRate - beforeSuccessRate).toFixed(1)}%`);
  
  console.log('\n✅ 修复成功的字段:');
  afterResults.success.forEach(fieldId => {
    console.log(`   - ${fieldId}`);
  });
  
  if (afterResults.failure && afterResults.failure.length > 0) {
    console.log('\n❌ 仍然存在问题的字段:');
    afterResults.failure.forEach(failure => {
      if (typeof failure === 'string') {
        console.log(`   - ${failure}`);
      } else {
        console.log(`   - ${failure.fieldId}: ${failure.reason || 'Unknown reason'}`);
      }
    });
  }
  
  console.log('='.repeat(60));
}

/**
 * 修复护照到期日期字段
 */
async function fixPassportExpiryField(field, value) {
  console.log('🔧 修复护照到期日期字段...');
  
  try {
    // 确保日期格式为DD/MM/YYYY
    const formattedDate = formatDateForMDAC(value);
    
    // 清空字段
    field.value = '';
    
    // 触发焦点事件
    field.focus();
    field.dispatchEvent(new Event('focus', { bubbles: true }));
    await delay(100);
    
    // 设置值
    field.value = formattedDate;
    
    // 触发事件序列
    field.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('blur', { bubbles: true }));
    
    // 验证填充结果
    return field.value === formattedDate;
  } catch (error) {
    console.error('修复护照到期日期字段失败:', error);
    return false;
  }
}

/**
 * 修复国家代码字段
 */
async function fixRegionField(field, value) {
  console.log('🔧 修复国家代码字段...');
  
  try {
    // 直接设置值
    field.value = value;
    
    // 触发change事件
    field.dispatchEvent(new Event('change', { bubbles: true }));
    
    // 检查是否使用Select2
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
      try {
        $(field).val(value).trigger('change');
      } catch (error) {
        console.warn('Select2设置失败:', error);
      }
    }
    
    // 等待可能的依赖字段更新
    await delay(200);
    
    return field.value === value;
  } catch (error) {
    console.error('修复国家代码字段失败:', error);
    return false;
  }
}

/**
 * 修复手机号码字段
 */
async function fixMobileField(field, value) {
  console.log('🔧 修复手机号码字段...');
  
  try {
    // 清除现有值
    field.value = '';
    
    // 确保只包含数字
    const numericValue = value.replace(/\D/g, '');
    
    // 触发焦点事件
    field.focus();
    field.dispatchEvent(new Event('focus', { bubbles: true }));
    await delay(100);
    
    // 设置值
    field.value = numericValue;
    
    // 触发事件序列
    field.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('blur', { bubbles: true }));
    
    return field.value === numericValue;
  } catch (error) {
    console.error('修复手机号码字段失败:', error);
    return false;
  }
}

/**
 * 修复到达日期字段
 */
async function fixArrivalDateField(field, value) {
  console.log('🔧 修复到达日期字段...');
  
  try {
    // 确保日期格式为DD/MM/YYYY
    const formattedDate = formatDateForMDAC(value);
    
    // 清空字段
    field.value = '';
    
    // 触发焦点事件
    field.focus();
    field.dispatchEvent(new Event('focus', { bubbles: true }));
    await delay(100);
    
    // 设置值
    field.value = formattedDate;
    
    // 触发事件序列
    field.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('blur', { bubbles: true }));
    
    // 验证填充结果
    return field.value === formattedDate;
  } catch (error) {
    console.error('修复到达日期字段失败:', error);
    return false;
  }
}

/**
 * 修复离开日期字段
 */
async function fixDepartureDateField(field, value) {
  console.log('🔧 修复离开日期字段...');
  
  try {
    // 确保日期格式为DD/MM/YYYY
    const formattedDate = formatDateForMDAC(value);
    
    // 清空字段
    field.value = '';
    
    // 触发焦点事件
    field.focus();
    field.dispatchEvent(new Event('focus', { bubbles: true }));
    await delay(100);
    
    // 设置值
    field.value = formattedDate;
    
    // 触发事件序列
    field.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('blur', { bubbles: true }));
    
    // 验证填充结果
    return field.value === formattedDate;
  } catch (error) {
    console.error('修复离开日期字段失败:', error);
    return false;
  }
}

/**
 * 修复最后登机港口字段
 */
async function fixEmbarkField(field, value) {
  console.log('🔧 修复最后登机港口字段...');
  
  try {
    // 机场/港口关键词
    const airportKeywords = [
      'KLIA', 'Kuala Lumpur', 'KUL', 'Malaysia', 
      'Airport', 'International', 'Sepang'
    ];
    
    // 1. 尝试直接匹配值
    field.value = value;
    field.dispatchEvent(new Event('change', { bubbles: true }));
    
    if (field.value === value) {
      console.log(`✅ 直接匹配成功: ${value}`);
      return true;
    }
    
    // 2. 尝试匹配包含关键词的选项
    for (let i = 0; i < field.options.length; i++) {
      const option = field.options[i];
      const optionText = option.text.toLowerCase();
      
      // 检查value是否包含在选项文本中
      if (optionText.includes(value.toLowerCase())) {
        field.value = option.value;
        field.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✅ 找到匹配选项: ${option.value} - ${option.text}`);
        return true;
      }
      
      // 检查选项文本是否包含任何机场关键词
      for (const keyword of airportKeywords) {
        if (optionText.includes(keyword.toLowerCase())) {
          field.value = option.value;
          field.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✅ 找到机场关键词匹配: ${option.value} - ${option.text} (关键词: ${keyword})`);
          return true;
        }
      }
    }
    
    // 3. 如果仍未找到匹配，使用第一个非空选项
    if (field.options.length > 1) {
      // 跳过第一个选项（通常是空选项或"请选择"）
      field.value = field.options[1].value;
      field.dispatchEvent(new Event('change', { bubbles: true }));
      console.log(`⚠️ 未找到匹配，使用默认选项: ${field.options[1].value} - ${field.options[1].text}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('修复最后登机港口字段失败:', error);
    return false;
  }
}

/**
 * 格式化日期为MDAC网站要求的DD/MM/YYYY格式
 */
function formatDateForMDAC(dateStr) {
  if (!dateStr) return '';
  
  // 如果已经是DD/MM/YYYY格式，直接返回
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
    return dateStr;
  }
  
  // 如果是YYYY-MM-DD格式，转换为DD/MM/YYYY
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    const [year, month, day] = dateStr.split('-');
    return `${day}/${month}/${year}`;
  }
  
  // 尝试解析其他格式的日期
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      console.warn(`⚠️ 无法解析日期: ${dateStr}`);
      return dateStr; // 如果无法解析，返回原始值
    }
    
    // 格式化为DD/MM/YYYY
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error(`❌ 日期格式化失败: ${dateStr}`, error);
    return dateStr; // 出错时返回原始值
  }
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 导出测试函数
window.runFieldFixTest = runFieldFixTest;

// 自动运行测试
console.log('🧪 MDAC字段修复测试脚本已加载');
console.log('使用 window.runFieldFixTest() 运行测试');
