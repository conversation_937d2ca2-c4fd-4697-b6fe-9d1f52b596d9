/**
 * 城市搜索管理器模块
 * 负责处理城市模糊搜索和州属城市同步功能
 * 
 * <AUTHOR> AI Team
 * @version 3.1.0
 */

class CitySearchManager {
    constructor(memoryManager, errorHandler, messageSystem) {
        this.memoryManager = memoryManager;
        this.errorHandler = errorHandler;
        this.messageSystem = messageSystem;
        
        // 数据存储
        this.cityData = null;
        this.fieldMappings = null;
        
        // 搜索配置
        this.searchConfig = {
            maxResults: 50,
            minQueryLength: 1,
            debounceDelay: 200
        };
        
        // 缓存
        this.searchCache = new Map();
        this.maxCacheSize = 100;
    }

    /**
     * 初始化城市数据
     * @param {Object} cityData - 城市数据
     * @param {Object} fieldMappings - 字段映射
     */
    initialize(cityData, fieldMappings) {
        this.cityData = cityData;
        this.fieldMappings = fieldMappings;
        
        // 整合数据源
        this.integrateCityDataSources();
        
        console.log('✅ [CitySearchManager] 初始化完成');
    }

    /**
     * 整合城市数据源
     */
    integrateCityDataSources() {
        console.log('🔄 [CitySearchManager] 整合城市数据源...');

        try {
            if (!this.cityData) {
                this.cityData = {};
            }

            // 如果有字段映射中的城市数据，将其整合到主城市数据中
            if (this.fieldMappings && this.fieldMappings.cities) {
                const mappingCities = this.fieldMappings.cities;
                
                Object.keys(mappingCities).forEach(cityKey => {
                    const cityInfo = mappingCities[cityKey];
                    const stateCode = cityInfo.state;
                    
                    if (stateCode) {
                        // 确保州属存在
                        if (!this.cityData[stateCode]) {
                            this.cityData[stateCode] = [];
                        }
                        
                        // 检查城市是否已存在
                        const existingCity = this.cityData[stateCode].find(city => 
                            city.code === cityInfo.code || city.name === cityInfo.name
                        );
                        
                        if (!existingCity) {
                            this.cityData[stateCode].push({
                                code: cityInfo.code || cityKey,
                                name: cityInfo.name || cityKey,
                                nameEn: cityInfo.nameEn || cityInfo.name || cityKey
                            });
                        }
                    }
                });
            }

            // 为每个州属的城市列表排序
            Object.keys(this.cityData).forEach(stateCode => {
                if (Array.isArray(this.cityData[stateCode])) {
                    this.cityData[stateCode].sort((a, b) => {
                        const nameA = a.nameEn || a.name || a;
                        const nameB = b.nameEn || b.name || b;
                        return nameA.localeCompare(nameB);
                    });
                }
            });

            console.log('✅ [CitySearchManager] 城市数据源整合完成');
        } catch (error) {
            this.errorHandler.handle(error, 'CityDataIntegration', '城市数据源整合失败');
        }
    }

    /**
     * 设置州属城市联动监听器
     * @param {Object} elements - UI元素对象
     */
    setupStateChangeListener(elements) {
        console.log('🌏 [CitySearchManager] 设置州属城市联动监听器...');

        try {
            if (elements.accommodationState && elements.accommodationCity) {
                const stateChangeHandler = (e) => {
                    const selectedState = e.target.value;
                    this.updateCityOptions(selectedState, elements.accommodationCity);
                };
                
                this.memoryManager.addListener(elements.accommodationState, 'change', stateChangeHandler);
                
                console.log('✅ [CitySearchManager] 州属城市联动监听器设置完成');
            }
        } catch (error) {
            this.errorHandler.handle(error, 'StateChangeListener', '州属城市联动监听器设置失败');
        }
    }

    /**
     * 更新城市选项
     * @param {string} stateCode - 州属代码
     * @param {HTMLSelectElement} cityField - 城市选择字段
     */
    updateCityOptions(stateCode, cityField) {
        try {
            console.log(`🔄 [CitySearchManager] 更新城市选项: ${stateCode}`);

            // 清空现有选项
            cityField.innerHTML = '<option value="">请选择城市</option>';

            if (!stateCode || !this.cityData[stateCode]) {
                return;
            }

            const cities = this.cityData[stateCode];
            this.populateCityDropdown(cityField, cities);

            console.log(`✅ [CitySearchManager] 城市选项更新完成，共${cities.length}个城市`);
        } catch (error) {
            this.errorHandler.handle(error, 'UpdateCityOptions', '城市选项更新失败');
        }
    }

    /**
     * 填充城市下拉框
     * @param {HTMLSelectElement} cityField - 城市字段
     * @param {Array} cities - 城市列表
     */
    populateCityDropdown(cityField, cities) {
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city.code || city.name;
            option.textContent = city.nameEn || city.name;
            
            // 添加中文名称作为title
            if (city.name !== city.nameEn) {
                option.title = city.name;
            }
            
            cityField.appendChild(option);
        });
    }

    /**
     * 添加城市搜索功能
     * @param {HTMLSelectElement} cityField - 城市选择字段
     */
    addCitySearchFunctionality(cityField) {
        try {
            if (!cityField) {
                console.warn('⚠️ [CitySearchManager] 城市字段不存在，无法添加搜索功能');
                return;
            }

            console.log('🔍 [CitySearchManager] 开始添加城市模糊搜索功能...');

            // 创建搜索容器
            const searchContainer = this.createSearchableDropdown(cityField);
            
            // 替换原有的select元素
            cityField.parentNode.replaceChild(searchContainer, cityField);

            console.log('✅ [CitySearchManager] 城市模糊搜索功能添加完成');
        } catch (error) {
            this.errorHandler.handle(error, 'CitySearchSetup', '城市搜索功能设置失败');
        }
    }

    /**
     * 创建可搜索的下拉框
     * @param {HTMLSelectElement} originalSelect - 原始select元素
     * @returns {HTMLElement} 搜索容器
     */
    createSearchableDropdown(originalSelect) {
        // 创建容器
        const container = document.createElement('div');
        container.className = 'searchable-dropdown';
        container.style.position = 'relative';

        // 创建搜索输入框
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'input-base field-input searchable-input';
        searchInput.placeholder = '搜索城市...';
        searchInput.id = originalSelect.id;

        // 创建下拉列表
        const dropdown = document.createElement('div');
        dropdown.className = 'searchable-dropdown-list hidden';
        dropdown.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            z-index: 1000;
            box-shadow: var(--shadow-md);
        `;

        // 获取当前州属的城市数据
        const currentState = this.getCurrentSelectedState();
        const cities = this.getCitiesForState(currentState);

        // 搜索功能
        const searchHandler = (e) => {
            const query = e.target.value.toLowerCase().trim();
            this.updateDropdownOptions(dropdown, cities, query, searchInput);
        };

        // 使用防抖优化搜索
        const debouncedSearch = window.PerformanceUtils.debounce(searchHandler, this.searchConfig.debounceDelay);
        this.memoryManager.addListener(searchInput, 'input', debouncedSearch);

        // 焦点事件
        const focusHandler = () => {
            dropdown.classList.remove('hidden');
            this.updateDropdownOptions(dropdown, cities, '', searchInput);
        };
        this.memoryManager.addListener(searchInput, 'focus', focusHandler);

        // 失焦事件（延迟隐藏，允许点击选项）
        const blurHandler = () => {
            setTimeout(() => {
                dropdown.classList.add('hidden');
            }, 200);
        };
        this.memoryManager.addListener(searchInput, 'blur', blurHandler);

        // 组装容器
        container.appendChild(searchInput);
        container.appendChild(dropdown);

        return container;
    }

    /**
     * 更新下拉选项
     * @param {HTMLElement} dropdown - 下拉容器
     * @param {Array} cities - 城市数据
     * @param {string} query - 搜索查询
     * @param {HTMLInputElement} input - 输入框
     */
    updateDropdownOptions(dropdown, cities, query, input) {
        try {
            // 清空现有选项
            dropdown.innerHTML = '';

            // 过滤城市
            const filteredCities = this.fuzzySearchCities(query, cities);

            // 限制显示数量
            const citiesToShow = filteredCities.slice(0, this.searchConfig.maxResults);

            if (citiesToShow.length === 0) {
                const noResults = document.createElement('div');
                noResults.className = 'dropdown-item no-results';
                noResults.textContent = '未找到匹配的城市';
                noResults.style.cssText = `
                    padding: var(--spacing-sm);
                    color: var(--text-muted);
                    font-style: italic;
                `;
                dropdown.appendChild(noResults);
                return;
            }

            // 创建选项
            citiesToShow.forEach(city => {
                const option = this.createDropdownOption(city, query, input, dropdown);
                dropdown.appendChild(option);
            });

        } catch (error) {
            console.error('❌ 更新下拉选项失败:', error);
        }
    }

    /**
     * 创建下拉选项
     * @param {Object} city - 城市信息
     * @param {string} query - 搜索查询
     * @param {HTMLInputElement} input - 输入框
     * @param {HTMLElement} dropdown - 下拉容器
     * @returns {HTMLElement} 选项元素
     */
    createDropdownOption(city, query, input, dropdown) {
        const option = document.createElement('div');
        option.className = 'dropdown-item';
        option.style.cssText = `
            padding: var(--spacing-sm);
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s ease;
        `;

        // 高亮匹配文本
        const displayText = this.highlightMatch(city.nameEn || city.name, query);
        option.innerHTML = `
            <div class="city-name">${displayText}</div>
            <div class="city-details" style="font-size: 12px; color: var(--text-secondary);">
                ${city.name !== city.nameEn ? city.name : ''} | ${city.code}
            </div>
        `;

        // 点击选择
        const selectHandler = () => {
            input.value = city.nameEn || city.name;
            input.setAttribute('data-city-code', city.code);
            dropdown.classList.add('hidden');
            
            // 触发change事件
            input.dispatchEvent(new Event('change', { bubbles: true }));
        };
        this.memoryManager.addListener(option, 'click', selectHandler);

        // 悬停效果
        const mouseEnterHandler = () => {
            option.style.backgroundColor = 'var(--bg-tertiary)';
        };
        const mouseLeaveHandler = () => {
            option.style.backgroundColor = '';
        };
        this.memoryManager.addListener(option, 'mouseenter', mouseEnterHandler);
        this.memoryManager.addListener(option, 'mouseleave', mouseLeaveHandler);

        return option;
    }

    /**
     * 模糊搜索城市
     * @param {string} query - 搜索查询
     * @param {Array} cities - 城市数据
     * @returns {Array} 过滤后的城市列表
     */
    fuzzySearchCities(query, cities) {
        if (!query || query.length < this.searchConfig.minQueryLength) {
            return cities || [];
        }

        // 检查缓存
        const cacheKey = `${query}_${cities ? cities.length : 0}`;
        if (this.searchCache.has(cacheKey)) {
            return this.searchCache.get(cacheKey);
        }

        const normalizedQuery = query.toLowerCase();
        
        const results = cities.filter(city => {
            const nameMatch = (city.name || '').toLowerCase().includes(normalizedQuery);
            const nameEnMatch = (city.nameEn || '').toLowerCase().includes(normalizedQuery);
            const codeMatch = (city.code || '').toLowerCase().includes(normalizedQuery);
            
            return nameMatch || nameEnMatch || codeMatch;
        }).sort((a, b) => {
            // 优先显示完全匹配的结果
            const aNameEn = (a.nameEn || '').toLowerCase();
            const bNameEn = (b.nameEn || '').toLowerCase();
            
            const aExact = aNameEn.startsWith(normalizedQuery);
            const bExact = bNameEn.startsWith(normalizedQuery);
            
            if (aExact && !bExact) return -1;
            if (!aExact && bExact) return 1;
            
            // 按字母顺序排序
            return aNameEn.localeCompare(bNameEn);
        });

        // 缓存结果
        this.cacheSearchResult(cacheKey, results);
        
        return results;
    }

    /**
     * 缓存搜索结果
     * @param {string} key - 缓存键
     * @param {Array} results - 搜索结果
     */
    cacheSearchResult(key, results) {
        // 如果缓存已满，删除最旧的条目
        if (this.searchCache.size >= this.maxCacheSize) {
            const firstKey = this.searchCache.keys().next().value;
            this.searchCache.delete(firstKey);
        }
        
        this.searchCache.set(key, results);
    }

    /**
     * 高亮匹配文本
     * @param {string} text - 原始文本
     * @param {string} query - 搜索查询
     * @returns {string} 高亮后的HTML
     */
    highlightMatch(text, query) {
        if (!query || !text) return text;

        const normalizedQuery = query.toLowerCase();
        const normalizedText = text.toLowerCase();
        const index = normalizedText.indexOf(normalizedQuery);

        if (index === -1) return text;

        const before = text.substring(0, index);
        const match = text.substring(index, index + query.length);
        const after = text.substring(index + query.length);

        return `${before}<mark style="background: var(--warning-color); color: white; padding: 1px 2px; border-radius: 2px;">${match}</mark>${after}`;
    }

    /**
     * 获取当前选中的州属
     * @returns {string} 州属代码
     */
    getCurrentSelectedState() {
        try {
            const stateField = document.getElementById('accommodationState');
            return stateField ? stateField.value : '';
        } catch (error) {
            console.warn('⚠️ 获取当前州属失败:', error);
            return '';
        }
    }

    /**
     * 获取指定州属的城市列表
     * @param {string} stateCode - 州属代码
     * @returns {Array} 城市列表
     */
    getCitiesForState(stateCode) {
        if (!stateCode || !this.cityData || !this.cityData[stateCode]) {
            return [];
        }
        return this.cityData[stateCode];
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.searchCache.clear();
        console.log('✅ [CitySearchManager] 资源清理完成');
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CitySearchManager;
} else {
    window.CitySearchManager = CitySearchManager;
}
