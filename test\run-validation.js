/**
 * MDAC AI Chrome扩展验证启动脚本
 * 使用Chrome MCP工具运行自动化验证
 */

// 验证启动函数
async function runMDACValidation() {
    console.log('🚀 启动MDAC AI Chrome扩展验证...');
    
    try {
        // 检查是否在正确的环境中
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            throw new Error('请在Chrome扩展环境中运行此验证');
        }
        
        // 创建验证系统实例
        const validator = new MDACValidationSystem();
        
        // 开始验证
        await validator.startValidation();
        
        console.log('✅ MDAC验证完成');
        
    } catch (error) {
        console.error('❌ MDAC验证失败:', error);
    }
}

// 如果在浏览器环境中，自动运行验证
if (typeof window !== 'undefined') {
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runMDACValidation);
    } else {
        runMDACValidation();
    }
}

// 导出函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runMDACValidation };
} else if (typeof window !== 'undefined') {
    window.runMDACValidation = runMDACValidation;
}
