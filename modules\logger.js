/**
 * MDAC Logger模块 - 兼容性包装器
 * 提供向后兼容的日志功能，支持模块化日志记录和性能监控
 * 
 * 功能特性：
 * - 分级日志记录（debug, info, warn, error）
 * - 日志管理和查询
 * - 性能监控和计时
 * - 全局实例自动创建
 * - 向后兼容现有代码
 * 
 * 创建时间: 2025-01-12
 * 作者: MDAC AI智能分析工具
 */

(function() {
    'use strict';
    
    /**
     * MDAC日志记录器类
     * 提供完整的日志记录、管理和性能监控功能
     */
    class MDACLogger {
        constructor() {
            // 日志存储数组
            this.logs = [];
            
            // 当前日志级别（debug < info < warn < error）
            this.level = 'info';
            
            // 日志级别优先级映射
            this.levelPriority = {
                'debug': 0,
                'info': 1,
                'warn': 2,
                'error': 3
            };
            
            // 性能监控计时器存储
            this.performanceTimers = new Map();
            
            // 最大日志条数限制（防止内存溢出）
            this.maxLogs = 1000;
            
            console.log('🔧 [MDACLogger] 日志系统初始化完成');
        }
        
        /**
         * 通用日志记录方法
         * @param {string} level - 日志级别 (debug|info|warn|error)
         * @param {string} module - 模块名称
         * @param {string} message - 日志消息
         * @param {*} data - 附加数据（可选）
         */
        log(level, module, message, data = null) {
            // 检查日志级别是否应该记录
            if (this.levelPriority[level] < this.levelPriority[this.level]) {
                return; // 跳过低优先级日志
            }
            
            // 创建日志条目
            const logEntry = {
                timestamp: new Date().toISOString(),
                level: level.toUpperCase(),
                module: module || 'UNKNOWN',
                message: message || '',
                data: data,
                id: this.generateLogId()
            };
            
            // 添加到日志存储
            this.logs.push(logEntry);
            
            // 限制日志数量，移除最旧的日志
            if (this.logs.length > this.maxLogs) {
                this.logs.shift();
            }
            
            // 输出到浏览器控制台
            this.outputToConsole(logEntry);
        }
        
        /**
         * 调试级别日志
         * @param {string} module - 模块名称
         * @param {string} message - 日志消息
         * @param {*} data - 附加数据（可选）
         */
        debug(module, message, data) {
            this.log('debug', module, message, data);
        }
        
        /**
         * 信息级别日志
         * @param {string} module - 模块名称
         * @param {string} message - 日志消息
         * @param {*} data - 附加数据（可选）
         */
        info(module, message, data) {
            this.log('info', module, message, data);
        }
        
        /**
         * 警告级别日志
         * @param {string} module - 模块名称
         * @param {string} message - 日志消息
         * @param {*} data - 附加数据（可选）
         */
        warn(module, message, data) {
            this.log('warn', module, message, data);
        }
        
        /**
         * 错误级别日志
         * @param {string} module - 模块名称
         * @param {string} message - 日志消息
         * @param {*} data - 附加数据（可选）
         */
        error(module, message, data) {
            this.log('error', module, message, data);
        }
        
        /**
         * 开始性能监控计时
         * @param {string} label - 性能监控标签
         */
        startPerformance(label) {
            if (!label) {
                this.warn('MDACLogger', '性能监控标签不能为空');
                return;
            }
            
            const startTime = performance.now();
            this.performanceTimers.set(label, startTime);
            
            this.debug('Performance', `开始监控: ${label}`, { startTime });
        }
        
        /**
         * 结束性能监控计时并记录结果
         * @param {string} label - 性能监控标签
         * @returns {number} 执行时间（毫秒）
         */
        endPerformance(label) {
            if (!label) {
                this.warn('MDACLogger', '性能监控标签不能为空');
                return 0;
            }
            
            const startTime = this.performanceTimers.get(label);
            if (startTime === undefined) {
                this.warn('Performance', `未找到性能监控标签: ${label}`);
                return 0;
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // 移除计时器
            this.performanceTimers.delete(label);
            
            // 记录性能结果
            this.info('Performance', `${label} 执行完成`, {
                duration: `${duration.toFixed(2)}ms`,
                startTime,
                endTime
            });
            
            return duration;
        }
        
        /**
         * 获取日志记录
         * @param {string} level - 过滤的日志级别（可选）
         * @param {string} module - 过滤的模块名称（可选）
         * @returns {Array} 过滤后的日志数组
         */
        getLogs(level = null, module = null) {
            let filteredLogs = [...this.logs];
            
            // 按级别过滤
            if (level) {
                filteredLogs = filteredLogs.filter(log => 
                    log.level.toLowerCase() === level.toLowerCase()
                );
            }
            
            // 按模块过滤
            if (module) {
                filteredLogs = filteredLogs.filter(log => 
                    log.module.toLowerCase().includes(module.toLowerCase())
                );
            }
            
            return filteredLogs;
        }
        
        /**
         * 清空所有日志记录
         */
        clearLogs() {
            const logCount = this.logs.length;
            this.logs = [];
            this.info('MDACLogger', `已清空 ${logCount} 条日志记录`);
        }
        
        /**
         * 设置日志级别
         * @param {string} level - 日志级别 ('debug', 'info', 'warn', 'error')
         */
        setLevel(level) {
            const validLevels = ['debug', 'info', 'warn', 'error'];
            if (validLevels.includes(level.toLowerCase())) {
                this.level = level.toLowerCase();
                this.info('Logger', `日志级别已设置为: ${this.level}`);
            } else {
                this.warn('Logger', `无效的日志级别: ${level}，使用默认级别: info`);
                this.level = 'info';
            }
        }
        
        /**
         * 获取当前日志统计信息
         * @returns {Object} 日志统计对象
         */
        getStats() {
            const stats = {
                total: this.logs.length,
                byLevel: {},
                byModule: {},
                activeTimers: this.performanceTimers.size
            };
            
            // 按级别统计
            this.logs.forEach(log => {
                stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
            });
            
            // 按模块统计
            this.logs.forEach(log => {
                stats.byModule[log.module] = (stats.byModule[log.module] || 0) + 1;
            });
            
            return stats;
        }
        
        /**
         * 生成唯一的日志ID
         * @returns {string} 日志ID
         */
        generateLogId() {
            return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        /**
         * 输出日志到浏览器控制台
         * @param {Object} logEntry - 日志条目
         */
        outputToConsole(logEntry) {
            const { level, module, message, data } = logEntry;
            const consoleMethod = level.toLowerCase();
            
            // 格式化输出
            const formattedMessage = `[${module}] ${message}`;
            
            if (data !== null && data !== undefined) {
                console[consoleMethod](formattedMessage, data);
            } else {
                console[consoleMethod](formattedMessage);
            }
        }
    }
    
    // 检查是否已存在MDACLogger，避免重复定义
    if (typeof window.MDACLogger === 'undefined') {
        // 将MDACLogger类挂载到全局window对象
        window.MDACLogger = MDACLogger;
        
        // 创建全局日志实例供其他模块使用
        window.mdacLogger = new MDACLogger();
        
        console.log('✅ [MDACLogger] 全局日志实例已创建并挂载到 window.mdacLogger');
    } else {
        console.log('⚠️ [MDACLogger] 检测到已存在的MDACLogger实例，跳过重复初始化');
    }
    
    // 向后兼容性检查
    if (typeof window.mdacLogger !== 'undefined') {
        // 验证所有必需的方法是否存在
        const requiredMethods = ['debug', 'info', 'warn', 'error', 'getLogs', 'clearLogs', 'setLevel', 'startPerformance', 'endPerformance'];
        const missingMethods = requiredMethods.filter(method => typeof window.mdacLogger[method] !== 'function');
        
        if (missingMethods.length === 0) {
            console.log('✅ [MDACLogger] 向后兼容性验证通过，所有必需方法均可用');
        } else {
            console.warn('⚠️ [MDACLogger] 向后兼容性警告，缺少方法:', missingMethods);
        }
    }
    
})();
