/**
 * MDAC AI智能填充工具 - 内容脚本
 * 负责与MDAC网站页面进行交互
 */

// 统一的消息监听器
let contentScriptInstance = null; // 用于存储MDACContentScript的实例

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // 1. 立即响应心跳检测
    if (message.action === 'ping') {
        console.log(`💓 [ContentScript] 收到来自 ${sender.id} 的 ping 请求, 立即发送 pong...`);
        sendResponse({ status: 'ready', from: 'content-script-unified-listener' });
        return true; // 保持通道开放
    }

    console.log(`📨 [ContentScript] 收到消息:`, message, `来自:`, sender);

    // 2. 将所有其他消息委托给主类的实例处理
    if (contentScriptInstance && contentScriptInstance.isInitialized) {
        console.log(`👍 [ContentScript] 实例已就绪，正在处理 "${message.action}"...`);
        contentScriptInstance.handleMessage(message, sender, sendResponse);
        return true; // 保持通道开放以进行异步响应
    }

    // 3. 如果脚本未完全初始化，则记录警告 (安全网)
    if (message.action !== 'ping') {
        console.warn(`⏳ [ContentScript] 收到消息 "${message.action}" 但实例尚未完全初始化。`);
        sendResponse({ success: false, error: 'Content script not ready.' });
    }
    
    // 返回true以处理异步sendResponse
    return true;
});


class MDACContentScript {
    constructor() {
        this.isInitialized = false;
        this.formData = {};
        this.aiAssistant = null;
        this.fieldDetector = null;
        this.detectedFields = {};
        this.errorRecoveryManager = null;
        this.fillMonitor = null;
        this.progressVisualizer = null;
        this.currentFillSession = null;

        contentScriptInstance = this; // 将实例赋值给全局变量
        this.init();
    }

    /**
     * 初始化内容脚本
     */
    async init() {
        if (this.isInitialized) return;

        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
     * 设置内容脚本
     */
    async setup() {
        const setupStartTime = performance.now();

        try {
            console.log('🚀 [ContentScript] 开始执行 setup...');
            console.log('📍 [ContentScript] 当前页面:', window.location.href);
            console.log('📄 [ContentScript] 页面状态:', document.readyState);

            // 步骤1: 检测页面类型
            console.log('🔍 [ContentScript] 步骤1: 检测页面类型');
            this.detectPageType();
            console.log('✅ [ContentScript] 页面类型检测完成:', this.pageType);

            // 步骤2: 动态加载所有必需的模块
            console.log('📦 [ContentScript] 步骤2: 开始模块加载');
            const moduleLoadStart = performance.now();
            await this.loadAllModules();
            const moduleLoadTime = performance.now() - moduleLoadStart;
            console.log(`✅ [ContentScript] 模块加载完成，耗时: ${moduleLoadTime.toFixed(2)}ms`);

            // 步骤3: 初始化依赖模块的功能
            console.log('🛠️ [ContentScript] 步骤3: 初始化工具');
            const toolsInitStart = performance.now();
            await this.initializeTools();
            const toolsInitTime = performance.now() - toolsInitStart;
            console.log(`✅ [ContentScript] 工具初始化完成，耗时: ${toolsInitTime.toFixed(2)}ms`);

            // 步骤4: 检测表单字段
            console.log('🔍 [ContentScript] 步骤4: 检测表单字段');
            const fieldDetectStart = performance.now();
            await this.detectFormFields();
            const fieldDetectTime = performance.now() - fieldDetectStart;
            console.log(`✅ [ContentScript] 字段检测完成，耗时: ${fieldDetectTime.toFixed(2)}ms`);

            // 步骤5: 注入AI助手界面
            console.log('🤖 [ContentScript] 步骤5: 注入AI助手');
            this.injectAIAssistant();

            // 步骤6: 观察表单变化
            console.log('👀 [ContentScript] 步骤6: 设置表单观察器');
            this.observeFormChanges();

            // 完成初始化
            this.isInitialized = true;
            const totalSetupTime = performance.now() - setupStartTime;

            console.log('🎉 [ContentScript] 所有模块和工具均已成功初始化!');
            console.log(`📊 [ContentScript] 总初始化时间: ${totalSetupTime.toFixed(2)}ms`);
            console.log('📈 [ContentScript] 性能统计:', {
                模块加载: `${moduleLoadTime.toFixed(2)}ms`,
                工具初始化: `${toolsInitTime.toFixed(2)}ms`,
                字段检测: `${fieldDetectTime.toFixed(2)}ms`,
                总耗时: `${totalSetupTime.toFixed(2)}ms`
            });

            // 通知背景脚本内容脚本已就绪
            chrome.runtime.sendMessage({
                action: 'content-script-ready',
                setupTime: totalSetupTime,
                pageType: this.pageType,
                fieldsDetected: Object.keys(this.formFields || {}).length
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('⚠️ 通知背景脚本失败:', chrome.runtime.lastError.message);
                } else {
                    console.log('✅ 已通知背景脚本内容脚本就绪');
                }
            });

            this.notifyExtension('contentScriptReady', {
                setupTime: totalSetupTime,
                pageType: this.pageType,
                fieldsDetected: Object.keys(this.formFields || {}).length
            });

        } catch (error) {
            console.error('❌ [ContentScript] 初始化过程中发生严重错误:', error);
            console.error('🔍 [ContentScript] 错误详细信息:', {
                message: error.message,
                stack: error.stack,
                name: error.name,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent
            });

            this.isInitialized = false;

            // 尝试显示用户友好的错误信息
            this.showInitializationError(error);

            // 通知扩展初始化失败
            this.notifyExtension('contentScriptError', {
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 动态加载所有必需的模块 - 分层串行加载
     */
    async loadAllModules() {
        console.log('📦 [ContentScript] 开始分层动态加载所有模块...');

        // 定义模块加载层级 - 使用新的模块化架构
        const loadingLayers = [
            // 第零层：核心适配器 - 必须最先加载
            {
                name: '核心适配器层',
                modules: [
                    'content/content-script-adapter.js'  // Content Script兼容性适配器
                ],
                delay: 300 // 确保适配器完全初始化
            },

            // 第一层：基础依赖 - 配置和兼容性适配器
            {
                name: '基础依赖层',
                modules: [
                    'config/ai-config.js',  // 全局配置
                    'modules/logger.js'     // 兼容性日志包装器
                ],
                delay: 200 // 确保全局变量完全初始化
            },

            // 第二层：核心工具 - 保留的功能模块
            {
                name: '核心工具层',
                modules: [
                    'modules/form-field-detector.js',  // 表单字段检测
                    'modules/google-maps-integration.js' // Google Maps集成
                ],
                delay: 150
            },

            // 第三层：功能模块 - 保留的功能模块
            {
                name: '功能模块层',
                modules: [
                    'modules/error-recovery-manager.js', // 错误恢复管理
                    'modules/fill-monitor.js'           // 填充监控
                ],
                delay: 100
            },

            // 第四层：UI组件 - 保留的UI模块
            {
                name: 'UI组件层',
                modules: [
                    'modules/field-status-display.js' // 字段状态显示
                ],
                delay: 100
            }
        ];

        try {
            // 按层级串行加载模块
            for (let layerIndex = 0; layerIndex < loadingLayers.length; layerIndex++) {
                const layer = loadingLayers[layerIndex];
                console.log(`📦 [ContentScript] 加载第${layerIndex + 1}层 - ${layer.name}:`, layer.modules);

                // 并行加载同一层的模块（同层内无依赖关系）
                const loadPromises = layer.modules.map(async (module) => {
                    try {
                        const moduleUrl = chrome.runtime.getURL(module);
                        console.log(`📦 [ContentScript] 正在加载模块: ${moduleUrl}`);
                        await this.loadScript(moduleUrl);
                        console.log(`✅ [ContentScript] 模块加载成功: ${module}`);
                    } catch (error) {
                        console.error(`❌ [ContentScript] 模块加载失败: ${module}`, error);
                        throw error; // 重新抛出错误以中止整个加载过程
                    }
                });

                await Promise.all(loadPromises);

                // 层间延迟，确保模块完全初始化
                if (layer.delay) {
                    await this.delay(layer.delay);
                }

                console.log(`✅ [ContentScript] 第${layerIndex + 1}层模块加载完成`);
            }

            console.log('🎉 [ContentScript] 所有模块按层级加载完成');
        } catch (error) {
            console.error('🔥 [ContentScript] 分层加载模块时发生错误:', error);
            console.error('💡 [ContentScript] 错误详情:', {
                message: error.message,
                stack: error.stack,
                url: error.filename || 'unknown'
            });
            throw error; // 重新抛出错误以中止setup
        }
    }

    /**
     * 初始化所有工具
     */
    async initializeTools() {
        console.log('🛠️ [ContentScript] 开始初始化所有工具...');
        await this.initializeLogger();
        await this.initializeFieldDetector();
        await this.initializeErrorRecoveryManager();
        await this.initializeFillMonitor();
        await this.initializeProgressVisualizer();
        console.log('👍 [ContentScript] 所有工具均已初始化');
    }

    /**
     * 初始化日志记录器并接管控制台
     */
    async initializeLogger() {
        try {
            // 确保MDACLogger类已加载
            if (typeof MDACLogger === 'undefined') {
                console.warn('⚠️ MDACLogger类未定义，尝试重新加载...');
                await this.loadScript(chrome.runtime.getURL('modules/logger.js'));
                
                // 再次检查
                if (typeof MDACLogger === 'undefined') {
                    throw new Error('MDACLogger类加载失败');
                }
            }
            
            // 检查是否已经存在全局日志实例（logger.js 会自动创建）
            if (window.mdacLogger) {
                console.log('✅ [ContentScript] 使用已存在的全局日志实例');
                this.logger = window.mdacLogger;
            } else {
                // 如果不存在，创建新实例
                console.log('🔧 [ContentScript] 创建新的日志实例');
                this.logger = new MDACLogger();
                window.mdacLogger = this.logger;
            }

            this.captureConsoleLogs();
            console.log('✅ [ContentScript] 日志记录器初始化并成功接管控制台');
        } catch (e) {
            console.error('❌ [ContentScript] 日志记录器初始化失败:', e);
            // 创建一个简单的降级日志器
            this.logger = {
                log: (level, module, message, data) => console.log(`[${level}] ${module}: ${message}`, data),
                debug: (module, message, data) => console.debug(`[DEBUG] ${module}: ${message}`, data),
                info: (module, message, data) => console.info(`[INFO] ${module}: ${message}`, data),
                warn: (module, message, data) => console.warn(`[WARN] ${module}: ${message}`, data),
                error: (module, message, data) => console.error(`[ERROR] ${module}: ${message}`, data)
            };
            window.mdacLogger = this.logger;
        }
    }

    /**
     * 捕获并重定向所有原生console调用
     */
    captureConsoleLogs() {
        if (!this.logger) return;
        const logger = this.logger;
        const originalConsole = { ...console }; // 备份原生console

        const levels = ['log', 'info', 'warn', 'error', 'debug'];

        levels.forEach(level => {
            const originalMethod = originalConsole[level];
            console[level] = (...args) => {
                // 1. 仍然调用原生方法，确保不在开发者工具中丢失日志
                originalMethod.apply(console, args);

                // 2. 防止我们自己的logger无限递归
                const stack = new Error().stack;
                if (stack && stack.includes('MDACLogger.outputToConsole')) {
                    return;
                }

                // 3. 将日志格式化并发送到我们的统一日志系统
                try {
                    const message = args.map(arg => {
                        if (typeof arg === 'object' && arg !== null) {
                            try {
                                if (arg instanceof Error) return arg.stack || arg.message;
                                return JSON.stringify(arg);
                            } catch (e) { return '[Unserializable Object]'; }
                        }
                        return String(arg);
                    }).join(' ');
                    
                    const logLevel = (level === 'log' ? 'info' : level).toUpperCase();
                    logger.log(logLevel, 'Console', message);
                } catch (e) {
                    originalConsole.error('Error in custom console wrapper:', e);
                }
            };
        });
    }

    /**
     * 初始化智能字段检测器
     */
    async initializeFieldDetector() {
        try {
            console.log('🔍 [ContentScript] 开始初始化字段检测器...');

            // 检查FormFieldDetector类是否已加载
            if (typeof FormFieldDetector === 'undefined') {
                console.warn('⚠️ FormFieldDetector类未定义，检查模块加载状态...');

                // 检查window对象上的FormFieldDetector
                if (typeof window.FormFieldDetector === 'undefined') {
                    console.error('❌ window.FormFieldDetector也未定义，adapter可能未正确加载');

                    // 尝试重新加载adapter
                    console.log('🔄 尝试重新加载content-script-adapter...');
                    await this.loadScript(chrome.runtime.getURL('content/content-script-adapter.js'));
                    await this.delay(500); // 等待adapter初始化

                    if (typeof window.FormFieldDetector === 'undefined') {
                        throw new Error('FormFieldDetector类在adapter重新加载后仍然未定义');
                    }
                } else {
                    // 如果window.FormFieldDetector存在，将其赋值给全局
                    window.FormFieldDetector = window.FormFieldDetector;
                    console.log('✅ 从window对象恢复FormFieldDetector');
                }
            }

            // 实例化字段检测器
            this.fieldDetector = new FormFieldDetector();

            // 验证实例是否有必要的方法
            if (typeof this.fieldDetector.detectFormFields !== 'function') {
                throw new Error('FormFieldDetector实例缺少detectFormFields方法');
            }

            if (typeof this.fieldDetector.validateDetection !== 'function') {
                throw new Error('FormFieldDetector实例缺少validateDetection方法');
            }

            console.log('✅ 智能字段检测器初始化完成，方法验证通过');
        } catch (error) {
            console.error('❌ 字段检测器初始化失败:', error);
            console.error('🔍 调试信息:', {
                'typeof FormFieldDetector': typeof FormFieldDetector,
                'typeof window.FormFieldDetector': typeof window.FormFieldDetector,
                'window.FormFieldDetector': window.FormFieldDetector
            });
            this.fieldDetector = null;
        }
    }

    /**
     * 动态加载脚本 - 增强版本，包含详细错误处理
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            console.log(`📥 [ContentScript] 开始加载模块: ${src}`);

            const script = document.createElement('script');
            script.src = src;

            // 成功加载处理
            script.onload = () => {
                console.log(`✅ [ContentScript] 模块加载成功: ${src}`);
                resolve();
            };

            // 错误处理
            script.onerror = (error) => {
                const errorMsg = `模块加载失败: ${src}`;
                console.error(`❌ [ContentScript] ${errorMsg}`, error);

                // 创建详细的错误信息
                const detailedError = new Error(errorMsg);
                detailedError.originalError = error;
                detailedError.scriptSrc = src;
                detailedError.timestamp = new Date().toISOString();

                reject(detailedError);
            };

            // 设置超时处理
            const timeout = setTimeout(() => {
                const timeoutError = new Error(`模块加载超时: ${src}`);
                timeoutError.scriptSrc = src;
                timeoutError.timeout = true;
                console.error(`⏰ [ContentScript] 模块加载超时: ${src}`);
                reject(timeoutError);
            }, 10000); // 10秒超时

            // 清理超时
            script.onload = () => {
                clearTimeout(timeout);
                console.log(`✅ [ContentScript] 模块加载成功: ${src}`);
                resolve();
            };

            script.onerror = (error) => {
                clearTimeout(timeout);
                const errorMsg = `模块加载失败: ${src}`;
                console.error(`❌ [ContentScript] ${errorMsg}`, error);

                const detailedError = new Error(errorMsg);
                detailedError.originalError = error;
                detailedError.scriptSrc = src;
                detailedError.timestamp = new Date().toISOString();

                reject(detailedError);
            };

            document.head.appendChild(script);
        });
    }

    /**
     * 初始化错误恢复管理器
     */
    async initializeErrorRecoveryManager() {
        try {
            // 确保ErrorRecoveryManager类已加载
            if (typeof ErrorRecoveryManager === 'undefined') {
                console.warn('⚠️ ErrorRecoveryManager类未定义，尝试重新加载...');
                await this.loadScript(chrome.runtime.getURL('modules/error-recovery-manager.js'));
                
                // 再次检查
                if (typeof ErrorRecoveryManager === 'undefined') {
                    throw new Error('ErrorRecoveryManager类加载失败');
                }
            }
            
            // 模块已由 loadAllModules 加载, 这里直接实例化
            this.errorRecoveryManager = new ErrorRecoveryManager();
            await this.errorRecoveryManager.loadErrorHistory();
            console.log('✅ 错误恢复管理器初始化完成');
        } catch (error) {
            console.error('❌ 错误恢复管理器初始化失败:', error);
            this.errorRecoveryManager = null;
        }
    }

    /**
     * 初始化填充监控器
     */
    async initializeFillMonitor() {
        try {
            // 确保FillMonitor类已加载
            if (typeof FillMonitor === 'undefined') {
                console.warn('⚠️ FillMonitor类未定义，尝试重新加载...');
                await this.loadScript(chrome.runtime.getURL('modules/fill-monitor.js'));
                
                // 再次检查
                if (typeof FillMonitor === 'undefined') {
                    throw new Error('FillMonitor类加载失败');
                }
            }
            
            // 模块已由 loadAllModules 加载, 这里直接实例化
            this.fillMonitor = new FillMonitor();
            console.log('✅ 填充监控器初始化完成');
        } catch (error) {
            console.error('❌ 填充监控器初始化失败:', error);
            this.fillMonitor = null;
        }
    }

    /**
     * 初始化进度可视化器
     */
    async initializeProgressVisualizer() {
        try {
            // 确保ProgressVisualizer类已加载
            if (typeof ProgressVisualizer === 'undefined') {
                console.warn('⚠️ ProgressVisualizer类未定义，尝试重新加载...');
                await this.loadScript(chrome.runtime.getURL('modules/progress-visualizer.js'));
                
                // 再次检查
                if (typeof ProgressVisualizer === 'undefined') {
                    throw new Error('ProgressVisualizer类加载失败');
                }
            }
            
            // 模块已由 loadAllModules 加载, 这里直接实例化
            this.progressVisualizer = new ProgressVisualizer();
            console.log('✅ 进度可视化器初始化完成');
        } catch (error) {
            console.error('❌ 进度可视化器初始化失败:', error);
            this.progressVisualizer = null;
        }
    }

    /**
     * 检测页面类型
     */
    detectPageType() {
        const url = window.location.href;

        if (url.includes('registerMain')) {
            this.pageType = 'registration';
            this.setupRegistrationPage();
        } else if (url.includes('confirmation')) {
            this.pageType = 'confirmation';
        } else {
            this.pageType = 'unknown';
        }
    }

    /**
     * 等待AI配置加载完成 - 优化版本
     */
    async waitForAIConfig(maxWaitTime = 10000) {
        const startTime = Date.now();
        let retryCount = 0;
        const maxRetries = 3;

        // 预加载配置文件
        await this.preloadAIConfig();

        while (Date.now() - startTime < maxWaitTime) {
            if (window.MDAC_AI_CONFIG && window.MDAC_AI_CONFIG.AI_PROMPTS) {
                console.log('✅ [ContentScript] AI配置已加载');
                return true;
            }

            // 等待100ms后重试
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.warn('⚠️ [ContentScript] AI配置加载超时，尝试重新加载配置文件...');

        // 多次重试加载配置文件
        while (retryCount < maxRetries) {
            try {
                retryCount++;
                console.log(`🔄 [ContentScript] 重试加载AI配置 (${retryCount}/${maxRetries})`);
                
                await this.loadScript(chrome.runtime.getURL('config/ai-config.js'));
                await this.loadScript(chrome.runtime.getURL('config/enhanced-ai-config.js'));
                await new Promise(resolve => setTimeout(resolve, 800)); // 增加等待时间

                if (window.MDAC_AI_CONFIG && window.MDAC_AI_CONFIG.AI_PROMPTS) {
                    console.log('✅ [ContentScript] AI配置重新加载成功');
                    return true;
                }
            } catch (error) {
                console.error(`❌ [ContentScript] AI配置重新加载失败 (尝试${retryCount}):`, error);
                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 递增等待时间
            }
        }

        // 如果仍然失败，创建最小化的AI配置
        console.warn('⚠️ [ContentScript] 创建最小化AI配置作为后备方案');
        this.createFallbackAIConfig();
        return false;
    }

    /**
     * 预加载AI配置文件
     */
    async preloadAIConfig() {
        try {
            console.log('🔄 [ContentScript] 预加载AI配置文件...');
            
            // 并行加载配置文件
            const configPromises = [
                this.loadScript(chrome.runtime.getURL('config/ai-config.js')),
                this.loadScript(chrome.runtime.getURL('config/enhanced-ai-config.js'))
            ];

            await Promise.allSettled(configPromises);
            await new Promise(resolve => setTimeout(resolve, 300)); // 等待配置初始化
            
            if (window.MDAC_AI_CONFIG && window.MDAC_AI_CONFIG.AI_PROMPTS) {
                console.log('✅ [ContentScript] AI配置预加载成功');
            }
        } catch (error) {
            console.warn('⚠️ [ContentScript] AI配置预加载失败，将在后续重试:', error);
        }
    }

    /**
     * 创建后备AI配置
     */
    createFallbackAIConfig() {
        if (!window.MDAC_AI_CONFIG) {
            window.MDAC_AI_CONFIG = {};
        }

        if (!window.MDAC_AI_CONFIG.AI_PROMPTS) {
            window.MDAC_AI_CONFIG.AI_PROMPTS = {
                FORM_OPTIMIZATION: '请分析以下表单数据并提供优化建议：{formData}',
                CONTENT_PARSING: '请解析以下内容并提取相关信息：{content}'
            };
        }

        if (!window.MDAC_AI_CONFIG.AI_CONTEXTS) {
            window.MDAC_AI_CONFIG.AI_CONTEXTS = {
                FORM_AUDITOR: '你是一个表单数据审核专家，请提供专业的建议。',
                CONTENT_PARSER: '你是一个内容解析专家，请提取关键信息。'
            };
        }

        if (!window.MDAC_AI_CONFIG.AI_FEATURES) {
            window.MDAC_AI_CONFIG.AI_FEATURES = {
                CONTENT_PARSING: { enabled: true },
                FORM_OPTIMIZATION: { enabled: true }
            };
        }

        console.log('✅ [ContentScript] 后备AI配置已创建');
    }

    /**
     * 设置注册页面
     */
    setupRegistrationPage() {
        // 检查表单是否存在
        const form = document.querySelector('form[name="permohonan"]');
        if (form) {
            this.form = form;
            this.identifyFormFields();
            this.addFormValidation();
        }
    }

    /**
     * 验证字段检测结果
     * @param {Object} detectedFields - 检测到的字段对象
     * @returns {Object} 标准化的验证结果对象
     */
    validateFieldDetection(detectedFields) {
        console.log('📊 开始验证字段检测结果...');

        let validation = null; // 声明validation变量，解决作用域问题

        try {
            // 检查fieldDetector是否存在validateDetection方法
            if (typeof this.fieldDetector.validateDetection !== 'function') {
                console.warn('⚠️ validateDetection方法不存在，使用默认验证逻辑');

                // 创建默认验证结果
                validation = {
                    isValid: true,
                    fieldCount: Object.keys(detectedFields).length,
                    warnings: [],
                    errors: []
                };

                // 基础验证：检查是否检测到字段
                if (validation.fieldCount === 0) {
                    validation.isValid = false;
                    validation.errors.push('未检测到任何表单字段');
                }

                console.log('📊 字段检测验证结果（默认逻辑）:', validation);
            } else {
                // 调用fieldDetector的validateDetection方法
                validation = this.fieldDetector.validateDetection(detectedFields);
                console.log('📊 字段检测验证结果（智能验证）:', validation);
            }

            // 增强validation对象，添加UI需要的字段
            if (!validation.confidence) {
                // 根据字段数量计算置信度
                const fieldCount = Object.keys(detectedFields).length;
                validation.confidence = Math.min(95, Math.max(60, fieldCount * 4)); // 基于字段数量的置信度计算
            }

            if (!validation.missingCriticalFields) {
                validation.missingCriticalFields = []; // 默认为空数组
            }

            console.log('✅ 字段验证完成，最终结果:', validation);
            return validation;

        } catch (error) {
            console.error('❌ 字段验证过程中发生错误:', error);

            // 返回错误状态的validation对象
            return {
                isValid: false,
                fieldCount: Object.keys(detectedFields).length,
                confidence: 0,
                warnings: [],
                errors: [`验证过程出错: ${error.message}`],
                missingCriticalFields: []
            };
        }
    }

    /**
     * 获取检测统计信息
     * @returns {Object} 统计信息对象
     */
    getDetectionStats() {
        try {
            // 检查fieldDetector是否存在getDetectionStats方法
            if (typeof this.fieldDetector.getDetectionStats === 'function') {
                const stats = this.fieldDetector.getDetectionStats();
                console.log('📈 获取到智能检测统计:', stats);
                return stats;
            } else {
                console.warn('⚠️ getDetectionStats方法不存在，使用默认统计');

                // 创建默认统计信息
                const defaultStats = {
                    successRate: 85, // 默认成功率
                    detectionTime: 0,
                    fieldTypes: {},
                    confidence: 85
                };

                console.log('📈 使用默认检测统计:', defaultStats);
                return defaultStats;
            }
        } catch (error) {
            console.error('❌ 获取检测统计失败:', error);

            // 返回错误状态的统计信息
            return {
                successRate: 0,
                detectionTime: 0,
                fieldTypes: {},
                confidence: 0,
                error: error.message
            };
        }
    }

    /**
     * 智能检测表单字段
     */
    async detectFormFields() {
        console.log('🔍 开始智能表单字段检测...');

        if (this.fieldDetector) {
            try {
                // 验证fieldDetector实例和方法
                if (typeof this.fieldDetector.detectFormFields !== 'function') {
                    throw new Error('fieldDetector.detectFormFields方法不存在');
                }

                console.log('🔍 调用fieldDetector.detectFormFields()...');
                // 使用智能检测器
                this.detectedFields = await this.fieldDetector.detectFormFields();
                console.log('✅ detectFormFields调用成功，结果:', this.detectedFields);

                // 使用新的验证方法，解决作用域问题
                const validation = this.validateFieldDetection(this.detectedFields);

                // 如果检测成功，使用检测结果
                if (validation.isValid || Object.keys(this.detectedFields).length > 5) {
                    this.formFields = this.detectedFields;
                    console.log(`✅ 智能检测成功，发现 ${Object.keys(this.detectedFields).length} 个字段`);

                    // 获取检测统计信息
                    const stats = this.getDetectionStats();

                    // 显示检测结果UI
                    this.showFieldDetectionStatus(validation, stats);

                    return;
                }
            } catch (error) {
                console.error('❌ 智能字段检测失败:', error);
            }
        }

        // 降级到传统检测方法
        console.log('🔄 降级到传统字段检测方法');
        this.identifyFormFields();
    }

    /**
     * 识别表单字段（传统方法）- 修正了MDAC网站的实际字段ID
     */
    identifyFormFields() {
        this.formFields = {
            // 个人信息 - 使用MDAC网站的实际字段ID
            name: document.getElementById('fullName'),
            passNo: document.getElementById('passportNumber'),
            dob: document.getElementById('dob'),
            nationality: document.getElementById('nationality'),
            sex: document.getElementById('sex'),
            passExpiry: document.getElementById('passExpDte'), // 修正：护照到期日期的正确字段ID
            email: document.getElementById('email'),
            confirmEmail: document.getElementById('confirmEmail'),
            countryCode: document.getElementById('countryCode'),
            mobileNo: document.getElementById('mobileNumber'),

            // 旅行信息 - 使用MDAC网站的实际字段ID
            arrivalDate: document.getElementById('arrDt'), // 修正：到达日期的正确字段ID
            departureDate: document.getElementById('depDt'), // 修正：离开日期的正确字段ID
            vesselNm: document.getElementById('vesselNm'),
            trvlMode: document.getElementById('trvlMode'),
            embark: document.getElementById('embark'), // Last Port of Embarkation

            // 住宿信息
            accommodationStay: document.getElementById('accommodationStay'),
            accommodationAddress1: document.getElementById('accommodationAddress1'),
            accommodationAddress2: document.getElementById('accommodationAddress2'),
            accommodationState: document.getElementById('accommodationState'),
            accommodationPostcode: document.getElementById('accommodationPostcode'),
            accommodationCity: document.getElementById('accommodationCity'),

            // 提交按钮
            submitBtn: document.getElementById('submit'),
            resetBtn: document.getElementById('reset')
        };

        // 过滤掉不存在的字段
        Object.keys(this.formFields).forEach(key => {
            if (!this.formFields[key]) {
                delete this.formFields[key];
            }
        });

        console.log(`📋 传统检测完成，发现 ${Object.keys(this.formFields).length} 个字段`);
        console.log('🔍 检测到的字段详情:', Object.keys(this.formFields));
        
        // 详细输出每个字段的状态
        Object.keys(this.formFields).forEach(key => {
            const field = this.formFields[key];
            console.log(`  - ${key}: ${field ? '✅ 存在' : '❌ 不存在'} (${field ? field.tagName : 'null'})`);
        });
    }

    /**
     * 添加表单验证功能
     */
    addFormValidation() {
        if (!this.form || !this.formFields) {
            console.warn('⚠️ 表单或字段未初始化，跳过验证设置');
            return;
        }

        console.log('🔧 正在添加表单验证功能...');

        // 为必填字段添加验证 - 使用正确的字段ID
        const requiredFields = [
            'name', 'passNo', 'dob', 'nationality', 'sex', 'passExpiry',
            'email', 'confirmEmail', 'countryCode', 'mobileNo',
            'arrivalDate', 'departureDate', 'accommodationStay'
        ];

        requiredFields.forEach(fieldKey => {
            const field = this.formFields[fieldKey];
            if (field) {
                // 添加必填标识
                this.markFieldAsRequired(field);

                // 添加实时验证
                field.addEventListener('blur', () => this.validateField(fieldKey, field));
                field.addEventListener('input', () => this.clearFieldError(field));
            }
        });

        // 添加表单提交验证
        this.form.addEventListener('submit', (e) => this.validateFormOnSubmit(e));

        console.log('✅ 表单验证功能已添加');
    }

    /**
     * 标记字段为必填
     */
    markFieldAsRequired(field) {
        if (!field) return;

        // 添加必填样式
        field.classList.add('mdac-required-field');

        // 查找标签并添加红色星号
        const label = document.querySelector(`label[for="${field.id}"]`);
        if (label && !label.querySelector('.required-asterisk')) {
            const asterisk = document.createElement('span');
            asterisk.className = 'required-asterisk';
            asterisk.textContent = ' *';
            asterisk.style.color = 'red';
            label.appendChild(asterisk);
        }
    }

    /**
     * 验证单个字段
     */
    validateField(fieldKey, field) {
        if (!field) return true;

        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // 基础必填验证
        if (!value) {
            isValid = false;
            errorMessage = '此字段为必填项';
        } else {
            // 特定字段验证
            switch (fieldKey) {
                case 'email':
                case 'confirmEmail':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = '请输入有效的邮箱地址';
                    }
                    break;
                case 'passNo':
                    if (value.length < 6) {
                        isValid = false;
                        errorMessage = '护照号码长度不足';
                    }
                    break;
                case 'mobileNo':
                    const phoneRegex = /^\d{7,15}$/;
                    if (!phoneRegex.test(value)) {
                        isValid = false;
                        errorMessage = '请输入有效的手机号码';
                    }
                    break;
            }
        }

        // 显示验证结果
        if (isValid) {
            this.clearFieldError(field);
        } else {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    /**
     * 显示字段错误
     */
    showFieldError(field, message) {
        this.clearFieldError(field);

        field.classList.add('mdac-field-error');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'mdac-field-error-message';
        errorDiv.textContent = message;
        errorDiv.style.cssText = 'color: red; font-size: 12px; margin-top: 4px;';

        field.parentNode.insertBefore(errorDiv, field.nextSibling);
    }

    /**
     * 清除字段错误
     */
    clearFieldError(field) {
        field.classList.remove('mdac-field-error');

        const errorMessage = field.parentNode.querySelector('.mdac-field-error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    /**
     * 表单提交时验证 - 更新了必填字段列表
     */
    validateFormOnSubmit(event) {
        console.log('🔍 正在验证表单提交...');

        let isFormValid = true;
        // 更新必填字段列表，使用正确的字段ID
        const requiredFields = [
            'name',                // 全名
            'passNo',              // 护照号码
            'dob',                 // 出生日期
            'nationality',         // 国籍
            'sex',                 // 性别
            'passExpiry',          // 护照到期日期
            'email',               // 电子邮箱
            'countryCode',         // 国家代码
            'mobileNo',            // 手机号码
            'arrivalDate',         // 到达日期
            'departureDate',       // 离开日期
            'embark'               // 最后登机港口
        ];

        requiredFields.forEach(fieldKey => {
            const field = this.formFields[fieldKey];
            if (field && !this.validateField(fieldKey, field)) {
                isFormValid = false;
            }
        });

        // 验证邮箱确认
        if (this.formFields.email && this.formFields.confirmEmail) {
            if (this.formFields.email.value !== this.formFields.confirmEmail.value) {
                this.showFieldError(this.formFields.confirmEmail, '邮箱确认不匹配');
                isFormValid = false;
            }
        }

        if (!isFormValid) {
            event.preventDefault();
            console.warn('⚠️ 表单验证失败，阻止提交');

            // 滚动到第一个错误字段
            const firstError = this.form.querySelector('.mdac-field-error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        } else {
            console.log('✅ 表单验证通过');
        }

        return isFormValid;
    }

    /**
     * 显示字段检测状态
     */
    showFieldDetectionStatus(validation, stats) {
        // 创建状态显示容器
        const statusContainer = document.createElement('div');
        statusContainer.id = 'mdac-field-detection-status';
        statusContainer.className = 'mdac-detection-status';

        const confidence = parseFloat(validation.confidence);
        const statusClass = confidence >= 80 ? 'success' : confidence >= 60 ? 'warning' : 'error';

        statusContainer.innerHTML = `
            <div class="detection-header">
                <span class="detection-icon">🔍</span>
                <span class="detection-title">智能字段检测</span>
                <button class="detection-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="detection-content">
                <div class="detection-stats">
                    <div class="stat-item ${statusClass}">
                        <span class="stat-label">检测置信度</span>
                        <span class="stat-value">${confidence}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">检测字段</span>
                        <span class="stat-value">${Object.keys(this.detectedFields).length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">成功率</span>
                        <span class="stat-value">${stats.successRate}%</span>
                    </div>
                </div>
                ${validation.missingCriticalFields.length > 0 ? `
                    <div class="detection-warnings">
                        <div class="warning-title">⚠️ 缺失关键字段:</div>
                        <div class="warning-list">${validation.missingCriticalFields.join(', ')}</div>
                    </div>
                ` : ''}
                <div class="detection-actions">
                    <button class="detection-btn primary" onclick="window.mdacContentScript.showDetectedFields()">
                        查看检测结果
                    </button>
                    <button class="detection-btn secondary" onclick="window.mdacContentScript.redetectFields()">
                        重新检测
                    </button>
                </div>
            </div>
        `;

        // 添加样式
        this.addDetectionStatusStyles();

        // 插入到页面
        document.body.appendChild(statusContainer);

        // 5秒后自动隐藏（如果没有警告）
        if (validation.missingCriticalFields.length === 0) {
            setTimeout(() => {
                if (statusContainer.parentElement) {
                    statusContainer.remove();
                }
            }, 5000);
        }

        // 暴露方法到全局
        window.mdacContentScript = this;
    }

    /**
     * 处理来自扩展的消息
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'quickFill':
                    await this.quickFill();
                    sendResponse({ success: true });
                    break;

                case 'getFormData':
                    const formData = this.getFormData();
                    sendResponse({ success: true, data: formData });
                    break;

                case 'fillForm':
                case 'fillFormData':
                    await this.fillFormData(message.data, message.sessionId, message.fieldList);
                    sendResponse({ success: true });
                    break;

                case 'validateForm':
                    const validation = await this.validateForm();
                    sendResponse({ success: true, data: validation });
                    break;

                case 'analyzeAPI':
                    await this.analyzeAPI();
                    sendResponse({ success: true });
                    break;

                case 'getPageInfo':
                    const pageInfo = this.getPageInfo();
                    sendResponse({ success: true, data: pageInfo });
                    break;

                default:
                    sendResponse({ success: false, error: '未知的操作类型' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * AI智能填充表单
     */
    async quickFill() {
        try {
            // 获取用户的默认数据
            const userData = await this.getUserData();

            if (!userData) {
                this.showNotification('请先在扩展中设置默认数据', 'warning');
                return;
            }

            // 使用AI智能填充
            await this.smartFill(userData);
            this.showNotification('AI智能填充完成', 'success');

        } catch (error) {
            console.error('AI智能填充失败:', error);
            this.showNotification('填充失败: ' + error.message, 'error');
        }
    }

    /**
     * AI智能填充
     */
    async smartFill(userData) {
        // 首先进行基础填充
        await this.basicFill(userData);

        // 然后进行AI验证和优化
        await this.aiValidateAndOptimize();
    }



    /**
     * 基础填充 - 修正了字段映射以匹配MDAC网站的实际字段ID
     */
    async basicFill(userData) {
        const fieldMapping = {
            name: userData.name,
            passNo: userData.passportNo,
            dob: userData.dateOfBirth,
            nationality: userData.nationality,
            sex: userData.sex,
            passExpiry: userData.passportExpiry, // 对应passExpDte字段
            email: userData.email,
            confirmEmail: userData.email,
            countryCode: userData.countryCode,
            mobileNo: userData.mobileNo,
            arrivalDate: userData.arrivalDate, // 对应arrDt字段
            departureDate: userData.departureDate, // 对应depDt字段
            vesselNm: userData.flightNo,
            trvlMode: userData.modeOfTravel,
            embark: userData.lastPort, // Last Port of Embarkation
            accommodationStay: userData.accommodation,
            accommodationAddress1: userData.address,
            accommodationAddress2: userData.address2,
            accommodationState: userData.state,
            accommodationPostcode: userData.postcode,
            accommodationCity: userData.city
        };

        for (const [fieldId, value] of Object.entries(fieldMapping)) {
            if (value && this.formFields[fieldId]) {
                await this.fillField(fieldId, value);
                await this.delay(100); // 避免填充过快
            }
        }
    }

    /**
     * 填充单个字段 - 增强版，修复了特殊字段的填充问题
     */
    async fillField(fieldId, value) {
        const field = this.formFields[fieldId];
        if (!field) {
            console.warn(`字段 ${fieldId} 不存在，跳过填充`);
            return;
        }

        try {
            console.log(`🔧 开始填充字段: ${fieldId} = "${value}"`);

            // 根据字段ID选择特殊处理方法
            switch (fieldId) {
                case 'passExpiry':
                    return await this.fillPassportExpiryField(field, value);

                case 'countryCode':
                    return await this.fillRegionField(field, value);

                case 'mobileNo':
                    return await this.fillMobileField(field, value);

                case 'arrivalDate':
                    return await this.fillArrivalDateField(field, value);

                case 'departureDate':
                    return await this.fillDepartureDateField(field, value);

                case 'embark':
                    return await this.fillEmbarkField(field, value);

                default:
                    // 标准填充处理
                    if (field.tagName === 'SELECT') {
                        // 下拉选择框
                        return await this.fillSelectField(field, value, fieldId);
                    } else if (field.type === 'text' || field.type === 'email' || field.type === 'tel') {
                        // 文本输入框
                        let finalValue = value;

                        // 日期字段特殊处理
                        if (fieldId === 'dob') {
                            finalValue = this.formatDateForMDAC(value);
                            console.log(`📅 日期格式转换: ${value} -> ${finalValue}`);
                        }

                        // 清空并填充
                        field.focus();
                        field.value = '';
                        field.value = finalValue;

                        // 触发事件
                        field.dispatchEvent(new Event('input', { bubbles: true }));
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                        field.dispatchEvent(new Event('blur', { bubbles: true }));

                        // 添加成功样式
                        field.style.backgroundColor = '#e8f5e8';
                        field.style.border = '2px solid #4CAF50';

                        console.log(`✅ 字段 ${fieldId} 填充成功: "${finalValue}"`);
                        return true;
                    }
            }
        } catch (error) {
            console.error(`❌ 填充字段 ${fieldId} 失败:`, error);
            return false;
        }
    }

    /**
     * 填充护照到期日期字段 - 修复passExpDte字段填充问题
     */
    async fillPassportExpiryField(field, value) {
        console.log('🔧 填充护照到期日期字段...');

        try {
            // 确保日期格式为DD/MM/YYYY
            const formattedDate = this.formatDateForMDAC(value);

            // 清空字段
            field.value = '';

            // 触发焦点事件
            field.focus();
            field.dispatchEvent(new Event('focus', { bubbles: true }));
            await this.delay(100);

            // 设置值
            field.value = formattedDate;

            // 触发事件序列
            field.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('change', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('blur', { bubbles: true }));

            // 添加成功样式
            field.style.backgroundColor = '#e8f5e8';
            field.style.border = '2px solid #4CAF50';

            console.log(`✅ 护照到期日期填充成功: "${formattedDate}"`);
            return true;
        } catch (error) {
            console.error('❌ 填充护照到期日期字段失败:', error);
            return false;
        }
    }

    /**
     * 填充国家代码字段 - 修复region字段填充问题
     */
    async fillRegionField(field, value) {
        console.log('🔧 填充国家代码字段...');

        try {
            // 直接设置值
            field.value = value;

            // 触发change事件
            field.dispatchEvent(new Event('change', { bubbles: true }));

            // 检查是否使用Select2
            if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
                try {
                    $(field).val(value).trigger('change');
                } catch (error) {
                    console.warn('Select2设置失败:', error);
                }
            }

            // 等待可能的依赖字段更新
            await this.delay(200);

            // 添加成功样式
            field.style.backgroundColor = '#e8f5e8';
            field.style.border = '2px solid #4CAF50';

            console.log(`✅ 国家代码填充成功: "${value}"`);
            return field.value === value;
        } catch (error) {
            console.error('❌ 填充国家代码字段失败:', error);
            return false;
        }
    }

    /**
     * 填充手机号码字段 - 修复mobile字段填充问题
     */
    async fillMobileField(field, value) {
        console.log('🔧 填充手机号码字段...');

        try {
            // 清除现有值
            field.value = '';

            // 确保只包含数字
            const numericValue = value.replace(/\D/g, '');

            // 触发焦点事件
            field.focus();
            field.dispatchEvent(new Event('focus', { bubbles: true }));
            await this.delay(100);

            // 设置值
            field.value = numericValue;

            // 触发事件序列
            field.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('change', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('blur', { bubbles: true }));

            // 添加成功样式
            field.style.backgroundColor = '#e8f5e8';
            field.style.border = '2px solid #4CAF50';

            console.log(`✅ 手机号码填充成功: "${numericValue}"`);
            return field.value === numericValue;
        } catch (error) {
            console.error('❌ 填充手机号码字段失败:', error);
            return false;
        }
    }

    /**
     * 填充到达日期字段 - 修复arrDt字段填充问题
     */
    async fillArrivalDateField(field, value) {
        console.log('🔧 填充到达日期字段...');

        try {
            // 确保日期格式为DD/MM/YYYY
            const formattedDate = this.formatDateForMDAC(value);

            // 首先尝试检测是否为日历控件
            const isCalendarWidget = await this.detectCalendarWidget(field);

            if (isCalendarWidget) {
                console.log('🗓️ 检测到日历控件，使用日历填充方式');
                const success = await this.fillCalendarWidget(field, formattedDate);
                if (success) {
                    console.log(`✅ 到达日期日历填充成功: "${formattedDate}"`);
                    return true;
                }
                console.log('⚠️ 日历填充失败，回退到文本输入方式');
            }

            // 回退到标准文本输入方式
            console.log('📝 使用文本输入方式填充日期');

            // 清空字段
            field.value = '';

            // 触发焦点事件
            field.focus();
            field.dispatchEvent(new Event('focus', { bubbles: true }));
            await this.delay(100);

            // 设置值
            field.value = formattedDate;

            // 触发事件序列
            field.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('change', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('blur', { bubbles: true }));

            // 添加成功样式
            field.style.backgroundColor = '#e8f5e8';
            field.style.border = '2px solid #4CAF50';

            console.log(`✅ 到达日期填充成功: "${formattedDate}"`);
            return field.value === formattedDate;
        } catch (error) {
            console.error('❌ 填充到达日期字段失败:', error);
            return false;
        }
    }

    /**
     * 填充离开日期字段 - 修复depDt字段填充问题
     */
    async fillDepartureDateField(field, value) {
        console.log('🔧 填充离开日期字段...');

        try {
            // 确保日期格式为DD/MM/YYYY
            const formattedDate = this.formatDateForMDAC(value);

            // 首先尝试检测是否为日历控件
            const isCalendarWidget = await this.detectCalendarWidget(field);

            if (isCalendarWidget) {
                console.log('🗓️ 检测到日历控件，使用日历填充方式');
                const success = await this.fillCalendarWidget(field, formattedDate);
                if (success) {
                    console.log(`✅ 离开日期日历填充成功: "${formattedDate}"`);
                    return true;
                }
                console.log('⚠️ 日历填充失败，回退到文本输入方式');
            }

            // 回退到标准文本输入方式
            console.log('📝 使用文本输入方式填充日期');

            // 清空字段
            field.value = '';

            // 触发焦点事件
            field.focus();
            field.dispatchEvent(new Event('focus', { bubbles: true }));
            await this.delay(100);

            // 设置值
            field.value = formattedDate;

            // 触发事件序列
            field.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('change', { bubbles: true }));
            await this.delay(50);
            field.dispatchEvent(new Event('blur', { bubbles: true }));

            // 添加成功样式
            field.style.backgroundColor = '#e8f5e8';
            field.style.border = '2px solid #4CAF50';

            console.log(`✅ 离开日期填充成功: "${formattedDate}"`);
            return field.value === formattedDate;
        } catch (error) {
            console.error('❌ 填充离开日期字段失败:', error);
            return false;
        }
    }

    /**
     * 检测字段是否为日历控件 - 改进版
     * @param {HTMLElement} field - 日期字段元素
     * @returns {boolean} 是否为日历控件
     */
    async detectCalendarWidget(field) {
        try {
            // 快速检测策略 - 避免复杂的DOM操作
            const quickDetectionResult = this.quickCalendarDetection(field);
            if (quickDetectionResult !== null) {
                return quickDetectionResult;
            }

            // 深度检测（仅在快速检测无结果时使用）
            return await this.deepCalendarDetection(field);

        } catch (error) {
            console.error('❌ 检测日历控件失败:', error);
            return false;
        }
    }

    /**
     * 快速日历控件检测
     * @param {HTMLElement} field - 日期字段元素
     * @returns {boolean|null} 检测结果，null表示需要深度检测
     */
    quickCalendarDetection(field) {
        try {
            // 1. 检查字段属性
            if (field.type === 'date' || field.type === 'datetime-local') {
                console.log('🗓️ 检测到HTML5日期输入，使用标准处理');
                return false; // HTML5日期输入不需要特殊处理
            }

            // 2. 检查常见的日历控件类名
            const calendarClasses = [
                'datepicker', 'date-picker', 'calendar-input',
                'hasDatepicker', 'ui-datepicker-trigger'
            ];

            for (const className of calendarClasses) {
                if (field.classList.contains(className)) {
                    console.log(`🗓️ 通过类名检测到日历控件: ${className}`);
                    return true;
                }
            }

            // 3. 检查data属性
            const dataAttributes = [
                'data-toggle', 'data-datepicker', 'data-calendar',
                'data-provide', 'data-date'
            ];

            for (const attr of dataAttributes) {
                const value = field.getAttribute(attr);
                if (value && (value.includes('date') || value.includes('calendar'))) {
                    console.log(`🗓️ 通过data属性检测到日历控件: ${attr}=${value}`);
                    return true;
                }
            }

            // 4. 检查只读字段（可能需要深度检测）
            if (field.readOnly) {
                console.log('🔍 字段为只读，需要进一步检测');
                return null; // 需要深度检测
            }

            // 5. 检查父容器的类名
            const parent = field.parentElement;
            if (parent) {
                const parentClasses = [
                    'date-picker', 'datepicker-container', 'calendar-wrapper',
                    'input-group-date', 'date-input-group'
                ];

                for (const className of parentClasses) {
                    if (parent.classList.contains(className)) {
                        console.log(`🗓️ 通过父容器检测到日历控件: ${className}`);
                        return true;
                    }
                }
            }

            return false; // 快速检测未发现日历控件
        } catch (error) {
            console.warn('⚠️ 快速日历检测失败:', error);
            return null; // 出错时进行深度检测
        }
    }

    /**
     * 深度日历控件检测
     * @param {HTMLElement} field - 日期字段元素
     * @returns {boolean} 是否为日历控件
     */
    async deepCalendarDetection(field) {
        try {
            console.log('🔍 开始深度日历控件检测...');

            const parent = field.parentElement;
            if (!parent) return false;

            // 查找相邻的日历图标或按钮
            const calendarTriggers = parent.querySelectorAll(
                '.fa-calendar, .glyphicon-calendar, .calendar-icon, ' +
                '[class*="calendar"], [class*="date-picker"], ' +
                'button[type="button"], .input-group-addon, ' +
                '.btn-calendar, .date-trigger'
            );

            if (calendarTriggers.length > 0) {
                console.log('🗓️ 找到日历触发器，确认为日历控件');
                return true;
            }

            // 检查字段的点击事件是否会打开日历（谨慎使用）
            if (field.readOnly) {
                return await this.testCalendarClickSafe(field);
            }

            return false;
        } catch (error) {
            console.error('❌ 深度日历检测失败:', error);
            return false;
        }
    }

    /**
     * 安全的日历点击测试
     * @param {HTMLElement} field - 日期字段元素
     * @returns {boolean} 是否为日历控件
     */
    async testCalendarClickSafe(field) {
        try {
            console.log('🧪 开始安全日历点击测试...');

            // 保存当前焦点元素
            const originalActiveElement = document.activeElement;

            // 记录点击前的DOM状态
            const beforeState = this.captureCalendarState();

            // 创建一个非侵入性的测试
            const testResult = await this.performNonIntrusiveCalendarTest(field);

            // 恢复原始状态
            if (originalActiveElement && originalActiveElement.focus) {
                originalActiveElement.focus();
            }

            return testResult;

        } catch (error) {
            console.error('❌ 安全日历点击测试失败:', error);
            return false;
        }
    }

    /**
     * 捕获日历状态
     * @returns {Object} 当前日历状态
     */
    captureCalendarState() {
        const calendarSelectors = [
            '.datepicker', '.ui-datepicker', '.calendar-popup',
            '.bootstrap-datepicker', '.flatpickr-calendar',
            '[class*="calendar"]', '[class*="datepicker"]'
        ];

        const state = {
            visibleCalendars: 0,
            calendarElements: []
        };

        calendarSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (el.offsetParent !== null) { // 检查是否可见
                    state.visibleCalendars++;
                    state.calendarElements.push({
                        element: el,
                        selector: selector,
                        visible: true
                    });
                }
            });
        });

        return state;
    }

    /**
     * 执行非侵入性日历测试
     * @param {HTMLElement} field - 日期字段元素
     * @returns {boolean} 是否为日历控件
     */
    async performNonIntrusiveCalendarTest(field) {
        try {
            // 1. 检查字段是否有关联的日历触发器
            const triggers = this.findCalendarTriggers(field);
            if (triggers.length > 0) {
                console.log('🗓️ 找到日历触发器，确认为日历控件');
                return true;
            }

            // 2. 检查字段的事件监听器（如果可能）
            if (this.hasCalendarEventListeners(field)) {
                console.log('🗓️ 检测到日历相关事件监听器');
                return true;
            }

            // 3. 最后才进行谨慎的点击测试
            return await this.performMinimalClickTest(field);

        } catch (error) {
            console.error('❌ 非侵入性测试失败:', error);
            return false;
        }
    }

    /**
     * 查找日历触发器
     * @param {HTMLElement} field - 日期字段元素
     * @returns {Array} 找到的触发器元素
     */
    findCalendarTriggers(field) {
        const triggers = [];
        const parent = field.parentElement;

        if (!parent) return triggers;

        // 查找同级或父级的触发器
        const triggerSelectors = [
            '.fa-calendar', '.glyphicon-calendar', '.calendar-icon',
            '.btn-calendar', '.date-trigger', '.input-group-addon',
            'button[type="button"]', '.ui-datepicker-trigger'
        ];

        triggerSelectors.forEach(selector => {
            // 在父容器中查找
            const elements = parent.querySelectorAll(selector);
            elements.forEach(el => {
                // 检查是否与字段相关
                if (this.isRelatedToField(el, field)) {
                    triggers.push(el);
                }
            });
        });

        return triggers;
    }

    /**
     * 检查元素是否与字段相关
     * @param {HTMLElement} element - 要检查的元素
     * @param {HTMLElement} field - 日期字段
     * @returns {boolean} 是否相关
     */
    isRelatedToField(element, field) {
        // 检查是否在同一个容器中
        const fieldParent = field.parentElement;
        const elementParent = element.parentElement;

        return fieldParent === elementParent ||
               fieldParent.contains(element) ||
               elementParent.contains(field);
    }

    /**
     * 检查字段是否有日历相关的事件监听器
     * @param {HTMLElement} field - 日期字段元素
     * @returns {boolean} 是否有日历事件监听器
     */
    hasCalendarEventListeners(field) {
        try {
            // 检查jQuery事件（如果jQuery可用）
            if (window.jQuery && window.jQuery.fn.jquery) {
                const $field = window.jQuery(field);
                const events = $field.data('events') || {};

                const calendarEvents = ['focus', 'click', 'dateChange', 'datepicker'];
                return calendarEvents.some(event => events[event]);
            }

            // 检查常见的日历库标记
            const calendarMarkers = [
                '_datepicker', 'datepicker', 'calendar',
                'flatpickr', 'pikaday'
            ];

            return calendarMarkers.some(marker =>
                field.hasAttribute(`data-${marker}`) ||
                field.classList.contains(marker) ||
                field[marker] // 检查属性
            );

        } catch (error) {
            console.warn('⚠️ 检查事件监听器失败:', error);
            return false;
        }
    }

    /**
     * 执行最小化点击测试
     * @param {HTMLElement} field - 日期字段元素
     * @returns {boolean} 是否为日历控件
     */
    async performMinimalClickTest(field) {
        try {
            console.log('🧪 执行最小化点击测试...');

            const beforeState = this.captureCalendarState();

            // 非常短暂的焦点测试
            field.focus();
            await this.delay(100);

            const afterFocusState = this.captureCalendarState();

            // 检查焦点是否触发了日历
            if (afterFocusState.visibleCalendars > beforeState.visibleCalendars) {
                console.log('🗓️ 焦点触发了日历，确认为日历控件');
                this.closeAnyOpenCalendars();
                return true;
            }

            return false;

        } catch (error) {
            console.error('❌ 最小化点击测试失败:', error);
            return false;
        }
    }

    /**
     * 关闭任何打开的日历
     */
    closeAnyOpenCalendars() {
        try {
            // 点击页面其他地方关闭日历
            document.body.click();

            // 按ESC键关闭日历
            const escEvent = new KeyboardEvent('keydown', {
                key: 'Escape',
                code: 'Escape',
                keyCode: 27
            });
            document.dispatchEvent(escEvent);

        } catch (error) {
            console.warn('⚠️ 关闭日历失败:', error);
        }
    }

    /**
     * 填充日历控件 - 改进版
     * @param {HTMLElement} field - 日期字段元素
     * @param {string} dateValue - 格式化的日期值 (DD/MM/YYYY)
     * @returns {boolean} 填充是否成功
     */
    async fillCalendarWidget(field, dateValue) {
        try {
            console.log(`🗓️ 开始填充日历控件: ${dateValue}`);

            // 解析日期
            const dateInfo = this.parseDateValue(dateValue);
            if (!dateInfo) {
                console.error('❌ 日期格式解析失败');
                return false;
            }

            // 尝试多种填充策略
            const strategies = [
                () => this.fillCalendarByDirectInput(field, dateValue, dateInfo),
                () => this.fillCalendarByAPI(field, dateValue, dateInfo),
                () => this.fillCalendarByInteraction(field, dateValue, dateInfo)
            ];

            for (const strategy of strategies) {
                try {
                    const result = await strategy();
                    if (result) {
                        console.log(`✅ 日历控件填充成功: ${dateValue}`);
                        return true;
                    }
                } catch (error) {
                    console.warn('⚠️ 填充策略失败，尝试下一个:', error.message);
                }
            }

            console.log('❌ 所有填充策略都失败了');
            return false;

        } catch (error) {
            console.error('❌ 填充日历控件失败:', error);
            return false;
        }
    }

    /**
     * 解析日期值
     * @param {string} dateValue - 日期字符串
     * @returns {Object|null} 解析后的日期信息
     */
    parseDateValue(dateValue) {
        try {
            const [day, month, year] = dateValue.split('/').map(num => parseInt(num, 10));

            if (isNaN(day) || isNaN(month) || isNaN(year)) {
                return null;
            }

            return {
                day: day,
                month: month,
                year: year,
                date: new Date(year, month - 1, day) // JavaScript月份从0开始
            };
        } catch (error) {
            console.error('❌ 日期解析失败:', error);
            return null;
        }
    }

    /**
     * 策略1: 直接输入填充
     * @param {HTMLElement} field - 日期字段
     * @param {string} dateValue - 日期值
     * @param {Object} dateInfo - 解析后的日期信息
     * @returns {boolean} 是否成功
     */
    async fillCalendarByDirectInput(field, dateValue, dateInfo) {
        try {
            console.log('📝 尝试直接输入填充...');

            // 尝试直接设置值
            field.value = dateValue;
            field.dispatchEvent(new Event('input', { bubbles: true }));
            field.dispatchEvent(new Event('change', { bubbles: true }));

            await this.delay(200);

            // 验证是否成功
            if (field.value === dateValue || this.isDateValueEquivalent(field.value, dateValue)) {
                console.log('✅ 直接输入填充成功');
                return true;
            }

            return false;
        } catch (error) {
            console.warn('⚠️ 直接输入填充失败:', error);
            return false;
        }
    }

    /**
     * 策略2: 使用日历API填充
     * @param {HTMLElement} field - 日期字段
     * @param {string} dateValue - 日期值
     * @param {Object} dateInfo - 解析后的日期信息
     * @returns {boolean} 是否成功
     */
    async fillCalendarByAPI(field, dateValue, dateInfo) {
        try {
            console.log('🔌 尝试API填充...');

            // 检查jQuery UI Datepicker
            if (window.jQuery && window.jQuery.fn.datepicker) {
                const $field = window.jQuery(field);
                if ($field.hasClass('hasDatepicker') || $field.data('datepicker')) {
                    $field.datepicker('setDate', dateInfo.date);
                    console.log('✅ jQuery UI Datepicker API填充成功');
                    return true;
                }
            }

            // 检查Bootstrap Datepicker
            if (window.jQuery && window.jQuery.fn.datepicker && field.getAttribute('data-provide') === 'datepicker') {
                const $field = window.jQuery(field);
                $field.datepicker('setDate', dateInfo.date);
                console.log('✅ Bootstrap Datepicker API填充成功');
                return true;
            }

            // 检查Flatpickr
            if (field._flatpickr) {
                field._flatpickr.setDate(dateInfo.date);
                console.log('✅ Flatpickr API填充成功');
                return true;
            }

            // 检查Pikaday
            if (field._pikaday) {
                field._pikaday.setDate(dateInfo.date);
                console.log('✅ Pikaday API填充成功');
                return true;
            }

            return false;
        } catch (error) {
            console.warn('⚠️ API填充失败:', error);
            return false;
        }
    }

    /**
     * 策略3: 交互式填充
     * @param {HTMLElement} field - 日期字段
     * @param {string} dateValue - 日期值
     * @param {Object} dateInfo - 解析后的日期信息
     * @returns {boolean} 是否成功
     */
    async fillCalendarByInteraction(field, dateValue, dateInfo) {
        try {
            console.log('🖱️ 尝试交互式填充...');

            // 打开日历
            const calendar = await this.openCalendar(field);
            if (!calendar) {
                return false;
            }

            // 设置日期
            const success = await this.setCalendarDate(calendar, dateInfo);

            if (success) {
                // 关闭日历
                await this.closeCalendar(calendar);
                console.log('✅ 交互式填充成功');
                return true;
            }

            return false;
        } catch (error) {
            console.warn('⚠️ 交互式填充失败:', error);
            return false;
        }
    }

    /**
     * 打开日历
     * @param {HTMLElement} field - 日期字段
     * @returns {HTMLElement|null} 日历容器
     */
    async openCalendar(field) {
        try {
            // 尝试多种方式打开日历
            const openMethods = [
                () => field.click(),
                () => field.focus(),
                () => {
                    const trigger = this.findCalendarTriggers(field)[0];
                    if (trigger) trigger.click();
                }
            ];

            for (const method of openMethods) {
                method();
                await this.delay(300);

                // 查找日历容器
                const calendar = this.findCalendarContainer();
                if (calendar) {
                    console.log('✅ 日历已打开');
                    return calendar;
                }
            }

            console.log('❌ 无法打开日历');
            return null;
        } catch (error) {
            console.error('❌ 打开日历失败:', error);
            return null;
        }
    }

    /**
     * 查找日历容器
     * @returns {HTMLElement|null} 日历容器
     */
    findCalendarContainer() {
        const selectors = [
            '.datepicker:not(.hidden)', '.ui-datepicker:not(.hidden)',
            '.calendar-popup:not(.hidden)', '.bootstrap-datepicker:not(.hidden)',
            '.flatpickr-calendar.open', '.pika-single:not(.is-hidden)',
            '[class*="calendar"]:not(.hidden)', '[class*="datepicker"]:not(.hidden)'
        ];

        for (const selector of selectors) {
            const calendar = document.querySelector(selector);
            if (calendar && calendar.offsetParent !== null) {
                return calendar;
            }
        }

        return null;
    }

    /**
     * 设置日历年份
     * @param {HTMLElement} calendar - 日历容器元素
     * @param {number} year - 目标年份
     * @returns {boolean} 设置是否成功
     */
    async setCalendarYear(calendar, year) {
        try {
            // 查找年份选择器（常见的选择器）
            const yearSelector = calendar.querySelector(
                'select[class*="year"], .ui-datepicker-year, ' +
                '[class*="year-select"], .datepicker-year'
            ) || calendar.querySelector('select:nth-of-type(2)'); // 通常年份是第二个select

            if (yearSelector) {
                console.log(`🗓️ 找到年份选择器，设置年份: ${year}`);
                yearSelector.value = year.toString();
                yearSelector.dispatchEvent(new Event('change', { bubbles: true }));
                await this.delay(200);
                return true;
            }

            // 查找年份按钮或链接
            const yearButton = calendar.querySelector(`[data-year="${year}"], [title="${year}"]`);
            if (yearButton) {
                console.log(`🗓️ 找到年份按钮，点击年份: ${year}`);
                yearButton.click();
                await this.delay(200);
                return true;
            }

            // 尝试通过导航按钮设置年份
            return await this.navigateToYear(calendar, year);
        } catch (error) {
            console.error('❌ 设置日历年份失败:', error);
            return false;
        }
    }

    /**
     * 设置日历月份
     * @param {HTMLElement} calendar - 日历容器元素
     * @param {number} month - 目标月份 (1-12)
     * @returns {boolean} 设置是否成功
     */
    async setCalendarMonth(calendar, month) {
        try {
            // 查找月份选择器
            const monthSelector = calendar.querySelector(
                'select[class*="month"], .ui-datepicker-month, ' +
                '[class*="month-select"], .datepicker-month'
            ) || calendar.querySelector('select:first-of-type'); // 通常月份是第一个select

            if (monthSelector) {
                console.log(`🗓️ 找到月份选择器，设置月份: ${month}`);
                // 月份值可能是0-11或1-12，尝试两种
                monthSelector.value = (month - 1).toString(); // 0-11
                if (monthSelector.value !== (month - 1).toString()) {
                    monthSelector.value = month.toString(); // 1-12
                }
                monthSelector.dispatchEvent(new Event('change', { bubbles: true }));
                await this.delay(200);
                return true;
            }

            // 查找月份按钮
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const monthButton = calendar.querySelector(`[data-month="${month-1}"], [title*="${monthNames[month-1]}"]`);
            if (monthButton) {
                console.log(`🗓️ 找到月份按钮，点击月份: ${monthNames[month-1]}`);
                monthButton.click();
                await this.delay(200);
                return true;
            }

            return true; // 如果没有月份选择器，可能已经在正确月份
        } catch (error) {
            console.error('❌ 设置日历月份失败:', error);
            return false;
        }
    }

    /**
     * 设置日历日期
     * @param {HTMLElement} calendar - 日历容器元素
     * @param {number} day - 目标日期
     * @returns {boolean} 设置是否成功
     */
    async setCalendarDay(calendar, day) {
        try {
            // 查找日期按钮
            const dayButton = calendar.querySelector(
                `td[data-day="${day}"] a, td a:contains("${day}"), ` +
                `[data-date="${day}"], .ui-state-default:contains("${day}")`
            );

            if (dayButton) {
                console.log(`🗓️ 找到日期按钮，点击日期: ${day}`);
                dayButton.click();
                await this.delay(200);
                return true;
            }

            // 备用方法：查找所有日期单元格
            const dayCells = calendar.querySelectorAll('td a, .day, [class*="date"]');
            for (const cell of dayCells) {
                if (cell.textContent.trim() === day.toString()) {
                    console.log(`🗓️ 通过文本匹配找到日期，点击: ${day}`);
                    cell.click();
                    await this.delay(200);
                    return true;
                }
            }

            console.log(`❌ 未找到日期 ${day} 的按钮`);
            return false;
        } catch (error) {
            console.error('❌ 设置日历日期失败:', error);
            return false;
        }
    }

    /**
     * 通过导航按钮导航到指定年份
     * @param {HTMLElement} calendar - 日历容器元素
     * @param {number} targetYear - 目标年份
     * @returns {boolean} 导航是否成功
     */
    async navigateToYear(calendar, targetYear) {
        try {
            const currentYear = new Date().getFullYear();
            const yearDiff = targetYear - currentYear;

            if (Math.abs(yearDiff) > 10) {
                console.log('⚠️ 年份差距过大，跳过导航');
                return true; // 跳过，避免无限循环
            }

            const nextButton = calendar.querySelector('.ui-datepicker-next, .next, [class*="next"]');
            const prevButton = calendar.querySelector('.ui-datepicker-prev, .prev, [class*="prev"]');

            if (yearDiff > 0 && nextButton) {
                // 需要向前导航
                for (let i = 0; i < Math.abs(yearDiff); i++) {
                    nextButton.click();
                    await this.delay(100);
                }
                return true;
            } else if (yearDiff < 0 && prevButton) {
                // 需要向后导航
                for (let i = 0; i < Math.abs(yearDiff); i++) {
                    prevButton.click();
                    await this.delay(100);
                }
                return true;
            }

            return true;
        } catch (error) {
            console.error('❌ 年份导航失败:', error);
            return false;
        }
    }

    /**
     * 填充最后登机港口字段 - 增强版，支持智能机场匹配
     */
    async fillEmbarkField(field, value) {
        console.log('🔧 填充最后登机港口字段...');

        try {
            // 1. 尝试直接匹配值
            field.value = value;
            field.dispatchEvent(new Event('change', { bubbles: true }));

            if (field.value === value) {
                console.log(`✅ 直接匹配成功: ${value}`);
                field.style.backgroundColor = '#e8f5e8';
                field.style.border = '2px solid #4CAF50';
                return true;
            }

            // 2. 使用机场数据库进行智能匹配
            if (window.airportDB && window.airportDB.initialized) {
                const searchResults = window.airportDB.searchAirports(value, 5);

                if (searchResults.length > 0) {
                    console.log(`🔍 找到 ${searchResults.length} 个匹配的机场:`, searchResults);

                    // 尝试匹配下拉框选项
                    for (const result of searchResults) {
                        const airport = result.airport;

                        // 尝试匹配的关键词列表
                        const searchTerms = [
                            airport.iata,
                            airport.icao,
                            airport.name,
                            airport.city,
                            airport.country,
                            ...(airport.aliases || [])
                        ];

                        // 在下拉框选项中查找匹配
                        for (let i = 0; i < field.options.length; i++) {
                            const option = field.options[i];
                            const optionText = option.text.toLowerCase();
                            const optionValue = option.value.toLowerCase();

                            // 检查是否匹配
                            for (const term of searchTerms) {
                                if (term && (
                                    optionText.includes(term.toLowerCase()) ||
                                    optionValue.includes(term.toLowerCase()) ||
                                    term.toLowerCase().includes(optionText) ||
                                    term.toLowerCase().includes(optionValue)
                                )) {
                                    field.value = option.value;
                                    field.dispatchEvent(new Event('change', { bubbles: true }));

                                    console.log(`✅ 智能匹配成功: ${option.value} - ${option.text} (匹配词: ${term})`);
                                    field.style.backgroundColor = '#e8f5e8';
                                    field.style.border = '2px solid #4CAF50';
                                    return true;
                                }
                            }
                        }
                    }
                }
            }

            // 3. 传统关键词匹配
            const airportKeywords = [
                'KLIA', 'Kuala Lumpur', 'KUL', 'Malaysia', 'Airport', 'International',
                'Sepang', 'Changi', 'Singapore', 'Bangkok', 'Suvarnabhumi',
                'Jakarta', 'Manila', 'Ho Chi Minh', 'Hanoi', 'Beijing', 'Shanghai',
                'Hong Kong', 'Taipei', 'Tokyo', 'Seoul', 'Mumbai', 'Delhi',
                'Sydney', 'Melbourne', 'London', 'Paris', 'Frankfurt', 'Amsterdam',
                'Dubai', 'Doha', 'Los Angeles', 'New York', 'San Francisco'
            ];

            // 尝试匹配包含关键词的选项
            for (let i = 0; i < field.options.length; i++) {
                const option = field.options[i];
                const optionText = option.text.toLowerCase();

                // 检查value是否包含在选项文本中
                if (optionText.includes(value.toLowerCase())) {
                    field.value = option.value;
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(`✅ 找到匹配选项: ${option.value} - ${option.text}`);
                    field.style.backgroundColor = '#e8f5e8';
                    field.style.border = '2px solid #4CAF50';
                    return true;
                }

                // 检查选项文本是否包含任何机场关键词
                for (const keyword of airportKeywords) {
                    if (optionText.includes(keyword.toLowerCase())) {
                        field.value = option.value;
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log(`✅ 找到机场关键词匹配: ${option.value} - ${option.text} (关键词: ${keyword})`);
                        field.style.backgroundColor = '#e8f5e8';
                        field.style.border = '2px solid #4CAF50';
                        return true;
                    }
                }
            }

            // 4. 如果仍未找到匹配，使用第一个非空选项
            if (field.options.length > 1) {
                // 跳过第一个选项（通常是空选项或"请选择"）
                field.value = field.options[1].value;
                field.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`⚠️ 未找到匹配，使用默认选项: ${field.options[1].value} - ${field.options[1].text}`);
                field.style.backgroundColor = '#fff3cd';
                field.style.border = '2px solid #ffc107';
                return true;
            }

            return false;
        } catch (error) {
            console.error('❌ 填充最后登机港口字段失败:', error);
            return false;
        }
    }

    /**
     * AI验证和优化
     */
    async aiValidateAndOptimize() {
        try {
            console.log('🤖 [ContentScript] 开始AI验证和优化...');

            // 安全检查AI配置是否已加载
            if (!window.MDAC_AI_CONFIG || !window.MDAC_AI_CONFIG.AI_PROMPTS) {
                console.warn('⚠️ [ContentScript] AI配置未加载，尝试等待...');
                const configLoaded = await this.waitForAIConfig();

                // 再次检查配置是否已加载
                if (!configLoaded || !window.MDAC_AI_CONFIG || !window.MDAC_AI_CONFIG.AI_PROMPTS) {
                    console.error('❌ [ContentScript] AI配置加载失败，使用基础验证模式');
                    // 使用基础验证模式而不是抛出错误
                    await this.basicValidateAndOptimize();
                    return;
                }
            }

            // 验证必要的配置项
            if (!window.MDAC_AI_CONFIG.AI_PROMPTS.FORM_OPTIMIZATION) {
                throw new Error('AI_PROMPTS.FORM_OPTIMIZATION配置缺失');
            }

            if (!window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_AUDITOR) {
                throw new Error('AI_CONTEXTS.FORM_AUDITOR配置缺失');
            }

            const formData = this.getFormData();
            console.log('📋 [ContentScript] 获取表单数据:', formData);

            // 使用配置文件中的提示词模板
            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.FORM_OPTIMIZATION.replace(
                '{formData}',
                JSON.stringify(formData, null, 2)
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_AUDITOR;
            console.log('🤖 [ContentScript] 准备发送AI请求...');

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                this.showAISuggestions(`✨ AI优化建议：<br><br>${response.data.replace(/\n/g, '<br>')}`);
                this.showNotification('AI分析完成，请查看建议', 'success');
            } else {
                this.showAISuggestions('AI分析暂时不可用，请检查网络连接和API配置。');
                this.showNotification('AI服务暂时不可用', 'error');
            }
        } catch (error) {
            console.error('AI验证失败:', error);
            this.showNotification('AI验证失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        const data = {};
        
        Object.keys(this.formFields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field && field.value) {
                data[fieldId] = field.value;
            }
        });

        return data;
    }

    /**
     * 获取用户数据
     */
    async getUserData() {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getUserData'
            });
            
            if (response.success) {
                return response.data;
            }
            
            // 如果没有用户数据，返回默认测试数据
            return this.getDefaultTestData();
        } catch (error) {
            console.error('获取用户数据失败:', error);
            return this.getDefaultTestData();
        }
    }

    /**
     * 基础验证和优化模式 - 当AI配置加载失败时使用
     */
    async basicValidateAndOptimize() {
        try {
            console.log('🔧 [ContentScript] 使用基础验证模式...');
            
            // 基础字段验证
            const fields = ['name', 'passNo', 'email', 'mobile'];
            let validationResults = [];
            
            for (const fieldId of fields) {
                const field = document.getElementById(fieldId);
                if (field && field.value) {
                    const isValid = this.basicFieldValidation(fieldId, field.value);
                    validationResults.push({ fieldId, isValid, value: field.value });
                    
                    // 基础样式标识
                    if (isValid) {
                        field.style.backgroundColor = '#e8f5e8';
                        field.style.border = '1px solid #4CAF50';
                    } else {
                        field.style.backgroundColor = '#ffeaa7';
                        field.style.border = '1px solid #fdcb6e';
                    }
                }
            }
            
            console.log('✅ [ContentScript] 基础验证完成', validationResults);
            
            // 显示简单的完成提示
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #2ecc71; color: white; padding: 10px 20px;
                border-radius: 5px; font-size: 14px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            notification.textContent = '✅ 基础验证完成';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
            
        } catch (error) {
            console.error('❌ [ContentScript] 基础验证失败:', error);
        }
    }
    
    /**
     * 基础字段验证
     */
    basicFieldValidation(fieldId, value) {
        switch (fieldId) {
            case 'name':
                return /^[A-Za-z\s]+$/.test(value) && value.trim().length > 1;
            case 'passNo':
                return /^[A-Z0-9]+$/.test(value) && value.length >= 6;
            case 'email':
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
            case 'mobile':
                return /^\d{8,15}$/.test(value);
            default:
                return true;
        }
    }

    /**
     * 获取默认测试数据 - 更新了字段映射以匹配MDAC网站的实际字段ID
     */
    getDefaultTestData() {
        return {
            name: 'ZHANG SAN',
            passportNo: 'A12345678',
            dateOfBirth: '01/01/1990',
            nationality: 'CHN',
            sex: '1',
            passportExpiry: '31/12/2030', // 对应passExpDte字段
            email: '<EMAIL>',
            countryCode: '+60',
            mobileNo: '*********',
            arrivalDate: '10/01/2025', // 对应arrDt字段
            departureDate: '15/01/2025', // 对应depDt字段
            flightNo: 'MH123',
            modeOfTravel: 'AIR',
            lastPort: 'KLIA', // 尝试使用KLIA作为机场关键词
            accommodation: '01',
            address: 'KUALA LUMPUR CITY CENTER HOTEL',
            address2: 'JALAN BUKIT BINTANG',
            state: '14', // WP KUALA LUMPUR
            postcode: '50000',
            city: '1400'
        };
    }

    /**
     * 注入AI助手界面
     */
    injectAIAssistant() {
        // 创建AI助手浮动面板
        const aiPanel = document.createElement('div');
        aiPanel.id = 'mdac-ai-assistant';
        aiPanel.innerHTML = `
            <div class="ai-header">
                <span class="ai-icon">🤖</span>
                <span class="ai-title">MDAC AI助手</span>
                <button class="ai-toggle" id="aiToggle">−</button>
            </div>
            <div class="ai-content" id="aiContent">
                <div class="ai-status">准备就绪</div>
                <div class="ai-suggestions" id="aiSuggestions"></div>
                <div class="ai-actions">
                    <button class="ai-btn" id="aiValidate">验证表单</button>
                    <button class="ai-btn" id="aiOptimize">优化建议</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(aiPanel);
        this.setupAIAssistantEvents();
    }

    /**
     * 设置AI助手事件
     */
    setupAIAssistantEvents() {
        const toggle = document.getElementById('aiToggle');
        const content = document.getElementById('aiContent');
        const validateBtn = document.getElementById('aiValidate');
        const optimizeBtn = document.getElementById('aiOptimize');

        toggle.addEventListener('click', () => {
            const isVisible = content.style.display !== 'none';
            content.style.display = isVisible ? 'none' : 'block';
            toggle.textContent = isVisible ? '+' : '−';
        });

        validateBtn.addEventListener('click', () => this.validateForm());
        optimizeBtn.addEventListener('click', () => this.aiValidateAndOptimize());
    }

    /**
     * 显示AI建议
     */
    showAISuggestions(suggestions) {
        const suggestionsDiv = document.getElementById('aiSuggestions');
        if (suggestionsDiv) {
            suggestionsDiv.innerHTML = suggestions.replace(/\n/g, '<br>');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `mdac-notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    /**
     * 格式化日期为MDAC网站要求的DD/MM/YYYY格式 - 增强版
     * @param {string} dateStr - 输入的日期字符串
     * @returns {string} - 格式化后的日期字符串
     */
    formatDateForMDAC(dateStr) {
        if (!dateStr) return '';

        // 使用增强日期解析器
        if (window.dateParser) {
            return window.dateParser.smartParse(dateStr);
        }

        // 回退到基础解析
        return this.basicDateFormat(dateStr);
    }

    /**
     * 基础日期格式化（回退方案）
     * @param {string} dateStr - 输入的日期字符串
     * @returns {string} - 格式化后的日期字符串
     */
    basicDateFormat(dateStr) {
        try {
            // 如果已经是DD/MM/YYYY格式，直接返回
            if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
                return dateStr;
            }

            // 如果是YYYY-MM-DD格式，转换为DD/MM/YYYY
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                const [year, month, day] = dateStr.split('-');
                return `${day}/${month}/${year}`;
            }

            // 尝试解析其他格式的日期
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                console.warn(`⚠️ 无法解析日期: ${dateStr}`);
                return dateStr; // 如果无法解析，返回原始值
            }

            // 格式化为DD/MM/YYYY
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();

            return `${day}/${month}/${year}`;
        } catch (error) {
            console.error(`❌ 日期格式化失败: ${dateStr}`, error);
            return dateStr; // 出错时返回原始值
        }
    }

    /**
     * 填充下拉选择框 - 智能选项匹配
     * @param {HTMLElement} field - 下拉框元素
     * @param {string} value - 要填充的值
     * @param {string} fieldId - 字段ID
     */
    async fillSelectField(field, value, fieldId) {
        if (!field || !field.options || field.options.length === 0) {
            console.warn(`⚠️ 下拉框 ${fieldId} 没有选项`);
            return;
        }

        console.log(`🔍 为下拉框 ${fieldId} 查找匹配选项: "${value}"`);

        // 特殊处理embark字段（Last Port of Embarkation）
        if (fieldId === 'embark') {
            await this.fillEmbarkField(field, value);
            return;
        }

        // 尝试直接匹配值
        if (this.setSelectByValue(field, value)) {
            console.log(`✅ 直接匹配成功: ${value}`);
            return;
        }

        // 尝试模糊匹配文本
        if (this.setSelectByText(field, value)) {
            console.log(`✅ 文本模糊匹配成功`);
            return;
        }

        // 如果都失败，记录所有选项供参考
        console.warn(`⚠️ 未找到匹配选项: ${value}`);
        console.log('📋 可用选项:');
        for (let i = 0; i < Math.min(10, field.options.length); i++) {
            console.log(`   ${i}: ${field.options[i].value} - ${field.options[i].text}`);
        }
    }

    /**
     * 填充embark字段（Last Port of Embarkation）
     * @param {HTMLElement} field - 下拉框元素
     * @param {string} value - 要填充的值
     */
    async fillEmbarkField(field, value) {
        console.log(`🔍 开始智能匹配机场/港口: "${value}"`);

        // 机场/港口关键词
        const airportKeywords = [
            'KLIA', 'Kuala Lumpur', 'KUL', 'Malaysia',
            'Airport', 'International', 'Sepang'
        ];

        // 如果value包含机场关键词，优先匹配
        let matchFound = false;

        // 1. 尝试直接匹配值
        if (this.setSelectByValue(field, value)) {
            console.log(`✅ 直接匹配成功: ${value}`);
            return true;
        }

        // 2. 尝试匹配包含关键词的选项
        for (let i = 0; i < field.options.length; i++) {
            const option = field.options[i];
            const optionText = option.text.toLowerCase();

            // 检查value是否包含在选项文本中
            if (optionText.includes(value.toLowerCase())) {
                field.value = option.value;
                field.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`✅ 找到匹配选项: ${option.value} - ${option.text}`);
                matchFound = true;
                break;
            }

            // 检查选项文本是否包含任何机场关键词
            for (const keyword of airportKeywords) {
                if (optionText.includes(keyword.toLowerCase())) {
                    field.value = option.value;
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(`✅ 找到机场关键词匹配: ${option.value} - ${option.text} (关键词: ${keyword})`);
                    matchFound = true;
                    break;
                }
            }

            if (matchFound) break;
        }

        // 3. 如果仍未找到匹配，使用第一个非空选项
        if (!matchFound && field.options.length > 1) {
            // 跳过第一个选项（通常是空选项或"请选择"）
            field.value = field.options[1].value;
            field.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`⚠️ 未找到匹配，使用默认选项: ${field.options[1].value} - ${field.options[1].text}`);
        }

        // 添加成功样式
        field.style.backgroundColor = '#e8f5e8';
        field.style.border = '2px solid #4CAF50';

        return matchFound;
    }

    /**
     * 根据值设置下拉框选项
     * @param {HTMLElement} selectElement - 下拉框元素
     * @param {string} value - 要设置的值
     * @returns {boolean} - 是否成功设置
     */
    setSelectByValue(selectElement, value) {
        if (!selectElement || !value) return false;

        // 尝试直接设置值
        selectElement.value = value;

        // 检查是否成功设置
        if (selectElement.value === value) {
            selectElement.dispatchEvent(new Event('change', { bubbles: true }));

            // 添加成功样式
            selectElement.style.backgroundColor = '#e8f5e8';
            selectElement.style.border = '2px solid #4CAF50';

            return true;
        }

        return false;
    }

    /**
     * 根据文本模糊匹配设置下拉框选项
     * @param {HTMLElement} selectElement - 下拉框元素
     * @param {string} text - 要匹配的文本
     * @returns {boolean} - 是否成功设置
     */
    setSelectByText(selectElement, text) {
        if (!selectElement || !text || !selectElement.options) return false;

        const lowerText = text.toLowerCase();

        // 遍历所有选项查找匹配
        for (let i = 0; i < selectElement.options.length; i++) {
            const option = selectElement.options[i];
            const optionText = option.text.toLowerCase();

            // 检查选项文本是否包含要匹配的文本
            if (optionText.includes(lowerText) || lowerText.includes(optionText)) {
                selectElement.value = option.value;
                selectElement.dispatchEvent(new Event('change', { bubbles: true }));

                // 添加成功样式
                selectElement.style.backgroundColor = '#e8f5e8';
                selectElement.style.border = '2px solid #4CAF50';

                return true;
            }
        }

        return false;
    }

    /**
     * 观察表单变化
     */
    observeFormChanges() {
        if (!this.form) return;

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    this.handleFormChange();
                }
            });
        });

        observer.observe(this.form, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['value', 'disabled']
        });
    }

    /**
     * 处理表单变化
     */
    handleFormChange() {
        // AI实时验证和建议
        this.debounce(() => {
            this.realtimeValidation();
        }, 500)();
    }

    /**
     * 实时验证
     */
    async realtimeValidation() {
        // 获取所有已填写的字段
        const filledFields = {};
        Object.keys(this.formFields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field && field.value) {
                filledFields[fieldId] = field.value;
            }
        });

        // 如果有足够的字段，进行AI验证
        if (Object.keys(filledFields).length >= 3) {
            await this.validateFieldsWithAI(filledFields);
        }
    }

    /**
     * 使用AI验证字段
     */
    async validateFieldsWithAI(fields) {
        try {
            // 检查是否启用实时验证
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.REALTIME_VALIDATION.enabled) {
                return;
            }

            const prompt = `请验证以下MDAC表单字段的数据：

${Object.entries(fields).map(([key, value]) => `${key}: ${value}`).join('\n')}

请检查：
1. 数据格式是否正确
2. 字段间的逻辑关系
3. 是否符合马来西亚入境要求

请简洁回复验证结果和建议。`;

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_VALIDATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                this.showAISuggestions(`🔍 实时验证：${response.data}`);
                this.highlightValidatedFields(fields, response.data);
            }
        } catch (error) {
            console.error('AI实时验证失败:', error);
        }
    }

    /**
     * 验证单个字段
     */
    async validateSingleField(fieldId, value) {
        try {
            // 安全检查AI配置是否已加载
            if (!window.MDAC_AI_CONFIG || !window.MDAC_AI_CONFIG.AI_PROMPTS) {
                console.warn('⚠️ [ContentScript] AI配置未加载，跳过字段验证');
                return;
            }

            const promptTemplate = window.MDAC_AI_CONFIG.AI_PROMPTS.FIELD_VALIDATION[fieldId];
            if (!promptTemplate) {
                return; // 没有对应的验证模板
            }

            const prompt = promptTemplate(value);
            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_VALIDATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                const field = this.formFields[fieldId];
                if (field) {
                    this.showFieldTooltip(field, response.data);
                    this.updateFieldValidationStatus(field, response.data);
                }
            }
        } catch (error) {
            console.error(`字段 ${fieldId} AI验证失败:`, error);
        }
    }

    /**
     * 高亮验证过的字段
     */
    highlightValidatedFields(fields, aiResult) {
        const isPositive = aiResult.toLowerCase().includes('正确') ||
                          aiResult.toLowerCase().includes('有效') ||
                          aiResult.toLowerCase().includes('符合');

        Object.keys(fields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field) {
                field.classList.remove('mdac-field-success', 'mdac-field-error');
                field.classList.add(isPositive ? 'mdac-field-success' : 'mdac-field-error');
            }
        });
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 通知扩展
     */
    notifyExtension(event, data = {}) {
        chrome.runtime.sendMessage({
            action: 'contentScriptEvent',
            event: event,
            data: data
        });
    }



    /**
     * 获取页面信息
     */
    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            pageType: this.pageType,
            formExists: !!this.form,
            fieldsCount: Object.keys(this.formFields).length
        };
    }

    /**
     * AI智能内容解析
     */
    async parseContentWithAI(content) {
        try {
            console.log('🤖 [ContentScript] 开始AI内容解析...');

            // 安全检查AI配置是否已加载
            if (!window.MDAC_AI_CONFIG || !window.MDAC_AI_CONFIG.AI_PROMPTS) {
                console.warn('⚠️ [ContentScript] AI配置未加载，尝试等待...');
                await this.waitForAIConfig();

                // 再次检查配置是否已加载
                if (!window.MDAC_AI_CONFIG || !window.MDAC_AI_CONFIG.AI_PROMPTS) {
                    throw new Error('AI配置加载失败，无法进行AI内容解析');
                }
            }

            // 检查是否启用内容解析功能
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.CONTENT_PARSING.enabled) {
                this.showNotification('内容解析功能已禁用', 'warning');
                return null;
            }

            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.CONTENT_PARSING.replace(
                '{content}',
                content
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.DATA_EXTRACTOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                try {
                    // 尝试解析JSON
                    const cleanResult = response.data.replace(/```json|```/g, '').trim();
                    const extractedData = JSON.parse(cleanResult);

                    // 填充提取的数据
                    await this.fillExtractedData(extractedData);
                    this.showNotification('AI内容解析完成', 'success');

                    return extractedData;
                } catch (parseError) {
                    console.error('解析AI返回的JSON失败:', parseError);
                    this.showNotification('AI解析结果格式异常', 'error');
                }
            }
        } catch (error) {
            console.error('AI内容解析失败:', error);
            this.showNotification('AI内容解析失败', 'error');
        }
        return null;
    }

    /**
     * 填充提取的数据
     */
    async fillExtractedData(data) {
        for (const [fieldId, value] of Object.entries(data)) {
            if (value && this.formFields[fieldId]) {
                await this.fillField(fieldId, value);
                await this.delay(100);
            }
        }
    }

    /**
     * 填充表单数据（从popup调用）
     */
    async fillFormData(data, sessionId = null, fieldList = null) {
        let fillSessionId = sessionId;

        try {
            this.showNotification('开始填充表单数据...', 'info');

            // 准备字段列表
            const fields = fieldList || Object.entries(data).map(([key, value]) => ({
                key,
                label: this.getFieldLabel(key),
                value: value
            }));

            // 启动进度可视化
            if (this.progressVisualizer) {
                this.progressVisualizer.startProgress({
                    totalFields: fields.length,
                    fieldList: fields,
                    title: 'MDAC表单填充进度',
                    showInSidepanel: false,
                    position: 'bottom-right'
                });
            }

            // 启动填充监控会话
            if (this.fillMonitor && !fillSessionId) {
                fillSessionId = this.fillMonitor.startFillSession(
                    data,
                    this.formFields,
                    (progress) => this.updateFillProgress(progress)
                );
                this.currentFillSession = fillSessionId;
                console.log(`🚀 启动填充监控会话: ${fillSessionId}`);
            }

            // 显示填充进度
            this.showProgressIndicator();

            const fieldMapping = {
                name: 'name',
                passportNo: 'passNo',
                dateOfBirth: 'dob',
                nationality: 'nationality',
                sex: 'sex',
                passportExpiry: 'passExpiry',
                email: 'email',
                confirmEmail: 'confirmEmail',
                countryCode: 'countryCode',
                mobileNo: 'mobileNo',
                arrivalDate: 'arrivalDate',
                departureDate: 'departureDate',
                flightNo: 'vesselNm',
                modeOfTravel: 'trvlMode',
                lastPort: 'embark',
                accommodation: 'accommodationStay',
                address: 'accommodationAddress1',
                address2: 'accommodationAddress2',
                state: 'accommodationState',
                postcode: 'accommodationPostcode',
                city: 'accommodationCity'
            };

            let filledCount = 0;
            let failedCount = 0;
            const totalFields = Object.keys(data).filter(key => data[key]).length;

            for (const [dataKey, formFieldId] of Object.entries(fieldMapping)) {
                const value = data[dataKey];
                if (value && this.formFields[formFieldId]) {
                    try {
                        // 开始字段填充监控
                        if (this.fillMonitor && fillSessionId) {
                            this.fillMonitor.startFieldFill(dataKey);
                        }

                        // 更新进度可视化器 - 开始处理
                        if (this.progressVisualizer) {
                            this.progressVisualizer.updateFieldStatus(dataKey, 'processing');
                        }

                        await this.fillField(formFieldId, value);
                        filledCount++;

                        // 标记字段填充成功
                        if (this.fillMonitor && fillSessionId) {
                            this.fillMonitor.markFieldSuccess(dataKey, value);
                        }

                        // 更新进度可视化器 - 成功
                        if (this.progressVisualizer) {
                            this.progressVisualizer.updateFieldStatus(dataKey, 'success', { value });
                        }

                        console.log(`✅ 字段填充成功: ${dataKey} = ${value}`);

                    } catch (fieldError) {
                        failedCount++;

                        // 标记字段填充失败
                        if (this.fillMonitor && fillSessionId) {
                            this.fillMonitor.markFieldFailure(dataKey, fieldError.message);
                        }

                        // 更新进度可视化器 - 失败
                        if (this.progressVisualizer) {
                            this.progressVisualizer.updateFieldStatus(dataKey, 'failed', {
                                error: fieldError.message
                            });
                        }

                        console.error(`❌ 字段填充失败: ${dataKey}`, fieldError);
                    }

                    // 更新进度
                    this.updateProgress((filledCount / totalFields) * 100);
                    await this.delay(150); // 稍慢的填充速度，更自然
                } else {
                    // 字段不存在或值为空，标记为跳过
                    if (this.progressVisualizer) {
                        this.progressVisualizer.updateFieldStatus(dataKey, 'skipped', {
                            reason: value ? '字段不存在' : '值为空'
                        });
                    }
                }
            }

            // 隐藏进度指示器
            this.hideProgressIndicator();

            // 进行AI验证
            await this.aiValidateAndOptimize();

            // 结束填充会话
            if (this.fillMonitor && fillSessionId) {
                const finalStatus = failedCount === 0 ? 'success' :
                                  filledCount > 0 ? 'partial' : 'failed';
                this.fillMonitor.endFillSession(finalStatus);
                this.currentFillSession = null;
            }

            const successMessage = failedCount > 0
                ? `AI智能填充完成！成功填写${filledCount}个字段，${failedCount}个字段失败`
                : `AI智能填充完成！已填写${filledCount}个字段`;

            this.showNotification(successMessage, failedCount > 0 ? 'warning' : 'success');

            // 显示字段状态和保留数据（如果有增强表单填充器的结果）
            if (window.fieldStatusDisplay && this.lastFillResult) {
                // 显示填充状态
                if (this.lastFillResult.fillStats) {
                    window.fieldStatusDisplay.showStatus(
                        this.lastFillResult.fillStats,
                        this.lastFillResult.filledFields || [],
                        this.lastFillResult.failedFields || []
                    );
                }

                // 显示保留数据
                if (this.lastFillResult.preservedData) {
                    window.fieldStatusDisplay.showPreservedData(this.lastFillResult.preservedData);
                }
            }

        } catch (error) {
            console.error('填充表单数据失败:', error);
            this.hideProgressIndicator();

            // 结束填充会话（失败状态）
            if (this.fillMonitor && fillSessionId) {
                this.fillMonitor.endFillSession('failed');
                this.currentFillSession = null;
            }

            this.showNotification('填充失败: ' + error.message, 'error');

            // 使用错误恢复管理器处理错误
            if (this.errorRecoveryManager) {
                this.errorRecoveryManager.handleError(error, {
                    operation: 'fillFormData',
                    data: data,
                    sessionId: fillSessionId
                });
            }
        }
    }

    /**
     * 更新填充进度（FillMonitor回调）
     * @param {Object} progress 进度信息
     */
    updateFillProgress(progress) {
        console.log('📊 填充进度更新:', progress);

        // 更新现有的进度指示器
        const progressElement = document.querySelector('.mdac-progress-indicator .progress-text');
        if (progressElement) {
            progressElement.textContent = `填充进度: ${progress.percentage}% (${progress.successful}/${progress.total})`;
        }

        const progressBar = document.querySelector('.mdac-progress-indicator .progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progress.percentage}%`;
        }

        // 显示详细状态
        const statusElement = document.querySelector('.mdac-progress-indicator .progress-status');
        if (statusElement) {
            statusElement.innerHTML = `
                <span class="status-item success">✅ ${progress.successful}</span>
                <span class="status-item failed">❌ ${progress.failed}</span>
                <span class="status-item skipped">⏭️ ${progress.skipped}</span>
                <span class="status-item progress">⏳ ${progress.inProgress}</span>
            `;
        }
    }

    /**
     * 显示填充进度指示器
     */
    showProgressIndicator() {
        // 移除现有的进度指示器
        const existingIndicator = document.querySelector('.mdac-progress-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // 创建新的进度指示器
        const indicator = document.createElement('div');
        indicator.className = 'mdac-progress-indicator';
        indicator.innerHTML = `
            <div class="progress-content">
                <div class="progress-icon">🤖</div>
                <div class="progress-text">AI正在智能填充表单...</div>
                <div class="mdac-progress-bar">
                    <div class="progress-bar" id="mdacProgressBar"></div>
                </div>
                <div class="progress-percentage" id="mdacProgressPercentage">0%</div>
                <div class="progress-status" id="mdacProgressStatus">
                    <span class="status-item success">✅ 0</span>
                    <span class="status-item failed">❌ 0</span>
                    <span class="status-item skipped">⏭️ 0</span>
                    <span class="status-item progress">⏳ 0</span>
                </div>
            </div>
        `;

        document.body.appendChild(indicator);
    }

    /**
     * 更新填充进度
     */
    updateProgress(percentage) {
        const progressBar = document.getElementById('mdacProgressBar');
        const progressPercentage = document.getElementById('mdacProgressPercentage');

        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }

        if (progressPercentage) {
            progressPercentage.textContent = Math.round(percentage) + '%';
        }
    }

    /**
     * 隐藏填充进度指示器
     */
    hideProgressIndicator() {
        const indicator = document.querySelector('.mdac-progress-indicator');
        if (indicator) {
            setTimeout(() => {
                indicator.remove();
            }, 1000); // 延迟1秒移除，让用户看到完成状态
        }
    }

    /**
     * 智能地址翻译
     */
    async translateAddressWithAI(chineseAddress) {
        try {
            // 安全检查AI配置是否已加载
            if (!window.MDAC_AI_CONFIG || !window.MDAC_AI_CONFIG.AI_PROMPTS) {
                console.warn('⚠️ [ContentScript] AI配置未加载，跳过地址翻译');
                return chineseAddress;
            }

            // 检查是否启用地址翻译功能
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.enabled) {
                this.showNotification('地址翻译功能已禁用', 'warning');
                return chineseAddress;
            }

            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.ADDRESS_TRANSLATION.replace(
                '{address}',
                chineseAddress
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.ADDRESS_TRANSLATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                let result = response.data;

                // 如果启用了结果清理
                if (window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.cleanResult) {
                    result = result.replace(/[^\w\s,.-]/g, '').trim();
                }

                this.showNotification('地址翻译完成', 'success');
                return result;
            }
        } catch (error) {
            console.error('地址翻译失败:', error);
            this.showNotification('地址翻译失败', 'error');
        }
        return chineseAddress; // 如果翻译失败，返回原地址
    }

    /**
     * 验证表单
     */
    async validateForm() {
        const formData = this.getFormData();
        const filledCount = Object.keys(formData).length;
        const totalFields = Object.keys(this.formFields).length;

        if (filledCount === 0) {
            this.showNotification('请先填写表单数据', 'warning');
            return;
        }

        // 使用AI进行全面验证
        await this.aiValidateAndOptimize();

        return {
            isValid: filledCount >= totalFields * 0.8, // 至少填写80%的字段
            filledCount: filledCount,
            totalFields: totalFields,
            completeness: (filledCount / totalFields * 100).toFixed(1)
        };
    }

    /**
     * 显示字段提示
     */
    showFieldTooltip(field, message) {
        // 移除现有的提示
        const existingTooltip = document.querySelector('.mdac-field-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }

        // 创建新的提示
        const tooltip = document.createElement('div');
        tooltip.className = 'mdac-field-tooltip';
        tooltip.textContent = message;

        // 确定提示类型
        const isPositive = message.toLowerCase().includes('正确') ||
                          message.toLowerCase().includes('有效') ||
                          message.toLowerCase().includes('符合');

        tooltip.classList.add(isPositive ? 'success' : 'warning');

        // 定位提示
        const rect = field.getBoundingClientRect();
        tooltip.style.position = 'absolute';
        tooltip.style.top = (rect.bottom + window.scrollY + 5) + 'px';
        tooltip.style.left = (rect.left + window.scrollX) + 'px';
        tooltip.style.zIndex = '10003';

        document.body.appendChild(tooltip);

        // 3秒后自动移除
        setTimeout(() => {
            tooltip.remove();
        }, 3000);
    }

    /**
     * 更新字段验证状态
     */
    updateFieldValidationStatus(field, aiResult) {
        const isPositive = aiResult.toLowerCase().includes('正确') ||
                          aiResult.toLowerCase().includes('有效') ||
                          aiResult.toLowerCase().includes('符合');

        field.classList.remove('mdac-field-success', 'mdac-field-error', 'mdac-field-highlight');
        field.classList.add(isPositive ? 'mdac-field-success' : 'mdac-field-error');
    }

    /**
     * 检测中文内容
     */
    containsChinese(text) {
        return /[\u4e00-\u9fff]/.test(text);
    }

    /**
     * 自动检测并翻译地址
     */
    async autoTranslateAddress(field) {
        if (!window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.autoDetect) {
            return;
        }

        const value = field.value.trim();
        if (value && this.containsChinese(value)) {
            const translatedAddress = await this.translateAddressWithAI(value);
            if (translatedAddress !== value) {
                field.value = translatedAddress;
                field.dispatchEvent(new Event('change', { bubbles: true }));
                this.showNotification('地址已自动翻译为英文', 'success');
            }
        }
    }

    /**
     * 添加检测状态样式
     */
    addDetectionStatusStyles() {
        if (document.getElementById('mdac-detection-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-detection-styles';
        styles.textContent = `
            .mdac-detection-status {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                border: 1px solid #e1e5e9;
            }

            .detection-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: #f8f9fa;
                border-bottom: 1px solid #e1e5e9;
                border-radius: 8px 8px 0 0;
            }

            .detection-icon {
                font-size: 16px;
                margin-right: 8px;
            }

            .detection-title {
                flex: 1;
                font-weight: 600;
                font-size: 14px;
                color: #333;
            }

            .detection-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .detection-content {
                padding: 16px;
            }

            .detection-stats {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 12px;
                margin-bottom: 16px;
            }

            .stat-item {
                text-align: center;
                padding: 8px;
                border-radius: 4px;
                background: #f8f9fa;
            }

            .stat-item.success {
                background: #d4edda;
                color: #155724;
            }

            .stat-item.warning {
                background: #fff3cd;
                color: #856404;
            }

            .stat-item.error {
                background: #f8d7da;
                color: #721c24;
            }

            .stat-label {
                display: block;
                font-size: 11px;
                margin-bottom: 2px;
                opacity: 0.8;
            }

            .stat-value {
                display: block;
                font-size: 14px;
                font-weight: 600;
            }

            .detection-warnings {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 12px;
                margin-bottom: 16px;
            }

            .warning-title {
                font-weight: 600;
                font-size: 12px;
                color: #856404;
                margin-bottom: 4px;
            }

            .warning-list {
                font-size: 11px;
                color: #856404;
            }

            .detection-actions {
                display: flex;
                gap: 8px;
            }

            .detection-btn {
                flex: 1;
                padding: 8px 12px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .detection-btn.primary {
                background: #007bff;
                color: white;
            }

            .detection-btn.primary:hover {
                background: #0056b3;
            }

            .detection-btn.secondary {
                background: #6c757d;
                color: white;
            }

            .detection-btn.secondary:hover {
                background: #545b62;
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 显示检测到的字段详情
     */
    showDetectedFields() {
        const modal = document.createElement('div');
        modal.className = 'mdac-detection-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>检测到的表单字段</h3>
                    <button class="modal-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="field-list">
                        ${Object.entries(this.detectedFields).map(([fieldType, element]) => `
                            <div class="field-item">
                                <div class="field-type">${fieldType}</div>
                                <div class="field-info">
                                    <span class="field-id">${element.id || element.name || 'N/A'}</span>
                                    <span class="field-tag">${element.tagName.toLowerCase()}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        // 添加模态框样式
        this.addModalStyles();
        document.body.appendChild(modal);
    }

    /**
     * 重新检测字段
     */
    async redetectFields() {
        // 清除缓存
        if (this.fieldDetector) {
            this.fieldDetector.clearCache();
        }

        // 移除现有状态显示
        const existingStatus = document.getElementById('mdac-field-detection-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        // 重新检测
        await this.detectFormFields();
    }

    /**
     * 添加模态框样式
     */
    addModalStyles() {
        if (document.getElementById('mdac-modal-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-modal-styles';
        styles.textContent = `
            .mdac-detection-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10001;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
            }

            .modal-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 8px;
                width: 90%;
                max-width: 600px;
                max-height: 80%;
                overflow: hidden;
            }

            .modal-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px;
                border-bottom: 1px solid #e1e5e9;
                background: #f8f9fa;
            }

            .modal-header h3 {
                margin: 0;
                font-size: 16px;
                color: #333;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 24px;
                height: 24px;
            }

            .modal-body {
                padding: 20px;
                max-height: 400px;
                overflow-y: auto;
            }

            .field-list {
                display: grid;
                gap: 12px;
            }

            .field-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e1e5e9;
            }

            .field-type {
                font-weight: 600;
                color: #333;
                font-size: 14px;
            }

            .field-info {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .field-id {
                font-family: monospace;
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 12px;
                color: #495057;
            }

            .field-tag {
                background: #007bff;
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 11px;
                text-transform: uppercase;
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 统一错误处理方法
     */
    handleError(error, context = {}) {
        if (this.errorRecoveryManager) {
            this.errorRecoveryManager.handleError(error, {
                ...context,
                pageType: this.pageType,
                url: window.location.href
            });
        } else {
            // 降级处理
            console.error('错误处理失败:', error);
            this.showNotification('操作失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(fieldKey) {
        const labels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passportExpiry: '护照到期日',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址',
            address2: '地址2',
            state: '州/省',
            postcode: '邮政编码',
            city: '城市'
        };
        return labels[fieldKey] || fieldKey;
    }

    /**
     * 显示初始化错误信息
     */
    showInitializationError(error) {
        try {
            // 创建错误提示容器
            const errorContainer = document.createElement('div');
            errorContainer.id = 'mdac-initialization-error';
            errorContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 400px;
                background: #fee;
                border: 2px solid #f00;
                border-radius: 8px;
                padding: 16px;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                box-shadow: 0 4px 12px rgba(255,0,0,0.2);
            `;

            errorContainer.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                    <strong style="color: #d00;">MDAC扩展初始化失败</strong>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="margin-left: auto; background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
                </div>
                <div style="color: #666; font-size: 14px; margin-bottom: 12px;">
                    扩展无法正常加载，某些功能可能不可用。
                </div>
                <div style="background: #f9f9f9; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333; margin-bottom: 12px;">
                    错误: ${error.message}
                </div>
                <div style="display: flex; gap: 8px;">
                    <button onclick="window.location.reload()"
                            style="background: #007bff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        重新加载页面
                    </button>
                    <button onclick="console.log('MDAC扩展错误详情:', ${JSON.stringify({
                        message: error.message,
                        stack: error.stack,
                        timestamp: new Date().toISOString()
                    })})"
                            style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        查看详情
                    </button>
                </div>
            `;

            document.body.appendChild(errorContainer);

            // 5秒后自动隐藏
            setTimeout(() => {
                if (errorContainer.parentElement) {
                    errorContainer.remove();
                }
            }, 10000);

        } catch (displayError) {
            console.error('❌ [ContentScript] 显示错误信息失败:', displayError);
        }
    }

    /**
     * 获取模块加载状态
     */
    getModuleLoadStatus() {
        const modules = {
            'MDACLogger': typeof MDACLogger !== 'undefined',
            'MDACDebugConsole': typeof MDACDebugConsole !== 'undefined',
            'FormFieldDetector': typeof FormFieldDetector !== 'undefined',
            'ErrorRecoveryManager': typeof ErrorRecoveryManager !== 'undefined',
            'FillMonitor': typeof FillMonitor !== 'undefined',
            'ProgressVisualizer': typeof ProgressVisualizer !== 'undefined',
            'MDAC_AI_CONFIG': typeof window.MDAC_AI_CONFIG !== 'undefined'
        };

        const loadedCount = Object.values(modules).filter(Boolean).length;
        const totalCount = Object.keys(modules).length;

        return {
            modules,
            loadedCount,
            totalCount,
            loadedPercentage: Math.round((loadedCount / totalCount) * 100),
            allLoaded: loadedCount === totalCount
        };
    }

    /**
     * 运行修复验证测试
     */
    async runFixVerificationTest() {
        console.log('🧪 [ContentScript] 开始运行修复验证测试...');

        const testResults = {
            timestamp: new Date().toISOString(),
            tests: [],
            summary: { passed: 0, failed: 0, total: 0 }
        };

        // 测试1: FormFieldDetector修复验证
        try {
            console.log('🔍 测试1: FormFieldDetector修复验证');

            if (!this.fieldDetector) {
                throw new Error('fieldDetector实例未初始化');
            }

            if (typeof this.fieldDetector.detectFormFields !== 'function') {
                throw new Error('detectFormFields方法不存在');
            }

            if (typeof this.fieldDetector.validateDetection !== 'function') {
                throw new Error('validateDetection方法不存在');
            }

            // 尝试调用detectFormFields方法
            const testFields = await this.fieldDetector.detectFormFields();

            testResults.tests.push({
                name: 'FormFieldDetector修复验证',
                status: 'PASSED',
                details: `detectFormFields方法正常工作，检测到${Object.keys(testFields).length}个字段`
            });
            testResults.summary.passed++;

        } catch (error) {
            testResults.tests.push({
                name: 'FormFieldDetector修复验证',
                status: 'FAILED',
                error: error.message
            });
            testResults.summary.failed++;
        }

        // 测试2: AI配置加载验证
        try {
            console.log('🤖 测试2: AI配置加载验证');

            if (!window.MDAC_AI_CONFIG) {
                throw new Error('MDAC_AI_CONFIG未定义');
            }

            if (!window.MDAC_AI_CONFIG.AI_PROMPTS) {
                throw new Error('AI_PROMPTS未定义');
            }

            if (!window.MDAC_AI_CONFIG.AI_PROMPTS.FORM_OPTIMIZATION) {
                throw new Error('FORM_OPTIMIZATION提示词未定义');
            }

            testResults.tests.push({
                name: 'AI配置加载验证',
                status: 'PASSED',
                details: 'AI配置完整加载，包含所有必要的提示词和上下文'
            });
            testResults.summary.passed++;

        } catch (error) {
            testResults.tests.push({
                name: 'AI配置加载验证',
                status: 'FAILED',
                error: error.message
            });
            testResults.summary.failed++;
        }

        // 测试3: 模块加载状态验证
        try {
            console.log('📦 测试3: 模块加载状态验证');

            const moduleStatus = this.getModuleLoadStatus();
            const requiredModules = ['FormFieldDetector', 'MDAC_AI_CONFIG'];
            const missingModules = requiredModules.filter(module => !moduleStatus.modules[module]);

            if (missingModules.length > 0) {
                throw new Error(`缺少必要模块: ${missingModules.join(', ')}`);
            }

            testResults.tests.push({
                name: '模块加载状态验证',
                status: 'PASSED',
                details: `所有必要模块已正确加载 (${moduleStatus.loadedCount}/${moduleStatus.totalCount})`
            });
            testResults.summary.passed++;

        } catch (error) {
            testResults.tests.push({
                name: '模块加载状态验证',
                status: 'FAILED',
                error: error.message
            });
            testResults.summary.failed++;
        }

        // 测试4: Manifest权限修复验证
        try {
            console.log('🔐 测试4: Manifest权限修复验证');

            // 尝试获取adapter的URL，这应该不会失败
            const adapterUrl = chrome.runtime.getURL('content/content-script-adapter.js');

            if (!adapterUrl || adapterUrl.includes('invalid')) {
                throw new Error('无法获取content-script-adapter.js的有效URL');
            }

            // 检查adapter是否已正确加载（通过检查其导出的类）
            const adapterClasses = ['FormFieldDetector', 'ErrorRecoveryManager', 'FillMonitor', 'ProgressVisualizer'];
            const missingClasses = adapterClasses.filter(className => typeof window[className] === 'undefined');

            if (missingClasses.length > 0) {
                throw new Error(`Adapter类未正确加载: ${missingClasses.join(', ')}`);
            }

            testResults.tests.push({
                name: 'Manifest权限修复验证',
                status: 'PASSED',
                details: `Adapter URL可访问: ${adapterUrl}，所有Adapter类已正确加载`
            });
            testResults.summary.passed++;

        } catch (error) {
            testResults.tests.push({
                name: 'Manifest权限修复验证',
                status: 'FAILED',
                error: error.message
            });
            testResults.summary.failed++;
        }

        testResults.summary.total = testResults.summary.passed + testResults.summary.failed;

        console.log('🎯 [ContentScript] 修复验证测试完成:', testResults);

        // 将测试结果存储到全局对象供调试使用
        window.mdacFixVerificationResults = testResults;

        return testResults;
    }
}

// 初始化内容脚本
const mdacContentScript = new MDACContentScript();

// 将实例暴露到全局对象供调试使用
window.mdacContentScript = mdacContentScript;

// 添加全局测试函数
window.runMDACFixVerificationTest = async function() {
    console.log('🧪 运行MDAC修复验证测试...');
    if (window.mdacContentScript && typeof window.mdacContentScript.runFixVerificationTest === 'function') {
        return await window.mdacContentScript.runFixVerificationTest();
    } else {
        console.error('❌ MDAC Content Script未正确初始化');
        return { error: 'Content Script未正确初始化' };
    }
};
