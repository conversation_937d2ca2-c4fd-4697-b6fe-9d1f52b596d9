/**
 * 机场匹配算法测试脚本
 * 用于验证embark字段的智能匹配算法
 */

// 测试数据
const TEST_AIRPORTS = [
  'KLIA',
  'Kuala Lumpur',
  'KUL',
  'Malaysia Airport',
  'Sepang',
  'Singapore Changi',
  'SIN',
  'Changi',
  'BKK',
  'Bangkok',
  'Suvarnabhumi',
  'Hong Kong',
  'HKG',
  'Beijing',
  'PEK',
  'Shanghai',
  'PVG',
  'Tokyo',
  'NRT',
  'Haneda',
  'Seoul',
  'ICN',
  'Incheon',
  'Sydney',
  'SYD',
  'London',
  'LHR',
  'Heathrow',
  'Paris',
  'CDG',
  'New York',
  'JFK',
  'Los Angeles',
  'LAX',
  'Dubai',
  'DXB',
  '吉隆坡',
  '新加坡',
  '曼谷',
  '香港',
  '北京',
  '上海',
  '东京',
  '首尔',
  '悉尼',
  '伦敦',
  '巴黎',
  '纽约',
  '洛杉矶',
  '迪拜'
];

/**
 * 运行机场匹配测试
 */
async function runAirportMatcherTest() {
  console.log('🧪 开始机场匹配算法测试...');
  console.log('='.repeat(60));
  
  // 1. 初始化机场数据库
  await initAirportDatabase();
  
  // 2. 获取embark字段
  const embarkField = document.getElementById('embark');
  if (!embarkField) {
    console.error('❌ 未找到embark字段');
    return;
  }
  
  console.log(`✅ 找到embark字段: ${embarkField.tagName}[${embarkField.type || 'N/A'}]`);
  console.log(`📋 embark字段有 ${embarkField.options.length} 个选项`);
  
  // 3. 显示前10个选项
  console.log('📝 embark字段的前10个选项:');
  Array.from(embarkField.options).slice(0, 10).forEach((option, index) => {
    console.log(`   ${index + 1}. 值: "${option.value}" - 文本: "${option.text}"`);
  });
  
  // 4. 测试机场匹配算法
  const results = {
    success: [],
    failure: []
  };
  
  // 保存原始值
  const originalValue = embarkField.value;
  
  // 测试每个机场名称
  for (const airport of TEST_AIRPORTS) {
    console.log(`\n🧪 测试匹配机场: "${airport}"`);
    
    try {
      // 使用fillEmbarkField方法
      const success = await window.mdacContentScript.fillEmbarkField(embarkField, airport);
      
      if (success) {
        results.success.push({
          query: airport,
          matched: embarkField.options[embarkField.selectedIndex].text,
          value: embarkField.value
        });
        console.log(`✅ 匹配成功: "${airport}" -> "${embarkField.options[embarkField.selectedIndex].text}" (${embarkField.value})`);
      } else {
        results.failure.push({
          query: airport,
          reason: '匹配方法返回失败'
        });
        console.warn(`❌ 匹配失败: "${airport}"`);
      }
      
      // 恢复原始值
      embarkField.value = originalValue;
      embarkField.dispatchEvent(new Event('change', { bubbles: true }));
    } catch (error) {
      results.failure.push({
        query: airport,
        reason: error.message
      });
      console.error(`❌ 匹配异常: "${airport}"`, error);
      
      // 恢复原始值
      embarkField.value = originalValue;
      embarkField.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }
  
  // 5. 生成测试报告
  generateTestReport(results);
  
  // 恢复原始值
  embarkField.value = originalValue;
  embarkField.dispatchEvent(new Event('change', { bubbles: true }));
  
  console.log('🎉 机场匹配算法测试完成');
}

/**
 * 初始化机场数据库
 */
async function initAirportDatabase() {
  console.log('🔧 初始化机场数据库...');
  
  // 检查是否已加载机场数据库
  if (window.airportDB && window.airportDB.initialized) {
    console.log('✅ 机场数据库已加载');
    return;
  }
  
  // 加载机场数据库脚本
  return new Promise((resolve, reject) => {
    try {
      // 创建脚本元素
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('airport-database.js');
      script.onload = () => {
        console.log('✅ 机场数据库脚本加载成功');
        
        // 等待初始化完成
        const checkInterval = setInterval(() => {
          if (window.airportDB && window.airportDB.initialized) {
            clearInterval(checkInterval);
            console.log('✅ 机场数据库初始化完成');
            resolve();
          }
        }, 100);
        
        // 超时处理
        setTimeout(() => {
          clearInterval(checkInterval);
          console.warn('⚠️ 机场数据库初始化超时');
          resolve();
        }, 5000);
      };
      script.onerror = (error) => {
        console.error('❌ 机场数据库脚本加载失败', error);
        reject(error);
      };
      
      // 添加到页面
      document.head.appendChild(script);
    } catch (error) {
      console.error('❌ 加载机场数据库失败', error);
      reject(error);
    }
  });
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
  console.log('\n📊 机场匹配算法测试报告');
  console.log('='.repeat(60));
  
  const totalTests = results.success.length + results.failure.length;
  const successRate = (results.success.length / totalTests * 100).toFixed(1);
  
  console.log(`📊 总测试数: ${totalTests}`);
  console.log(`✅ 匹配成功: ${results.success.length}`);
  console.log(`❌ 匹配失败: ${results.failure.length}`);
  console.log(`📈 成功率: ${successRate}%`);
  
  if (results.success.length > 0) {
    console.log('\n✅ 成功匹配的机场:');
    results.success.forEach(result => {
      console.log(`   - "${result.query}" -> "${result.matched}" (${result.value})`);
    });
  }
  
  if (results.failure.length > 0) {
    console.log('\n❌ 匹配失败的机场:');
    results.failure.forEach(failure => {
      console.log(`   - "${failure.query}": ${failure.reason || '未知原因'}`);
    });
  }
  
  console.log('='.repeat(60));
}

// 导出测试函数
window.runAirportMatcherTest = runAirportMatcherTest;

// 自动运行测试
console.log('🧪 机场匹配算法测试脚本已加载');
console.log('使用 window.runAirportMatcherTest() 运行测试');
