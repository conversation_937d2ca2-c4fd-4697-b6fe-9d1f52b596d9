/**
 * 机场数据库和智能匹配系统
 * 用于优化embark字段的机场匹配算法
 */

class AirportDatabase {
  constructor() {
    this.airports = [];
    this.airportIndex = new Map();
    this.cityIndex = new Map();
    this.countryIndex = new Map();
    this.codeIndex = new Map();
    this.initialized = false;
  }

  /**
   * 初始化机场数据库
   */
  async init() {
    if (this.initialized) return;
    
    console.log('🛫 初始化机场数据库...');
    
    // 加载机场数据
    this.loadAirportData();
    
    // 建立索引
    this.buildIndexes();
    
    this.initialized = true;
    console.log(`✅ 机场数据库初始化完成，共加载 ${this.airports.length} 个机场`);
  }

  /**
   * 加载机场数据
   */
  loadAirportData() {
    // 主要的亚洲机场数据（重点关注马来西亚和周边国家）
    this.airports = [
      // 马来西亚机场
      {
        iata: 'KUL',
        icao: 'WMKK',
        name: 'Kuala Lumpur International Airport',
        city: 'Kuala Lumpur',
        country: 'Malaysia',
        aliases: ['KLIA', 'KLIA1', 'Sepang', '吉隆坡国际机场', '吉隆坡机场']
      },
      {
        iata: 'SZB',
        icao: 'WMSA',
        name: 'Sultan Abdul Aziz Shah Airport',
        city: 'Subang',
        country: 'Malaysia',
        aliases: ['Subang Airport', 'Subang', '梳邦机场']
      },
      {
        iata: 'JHB',
        icao: 'WMKJ',
        name: 'Senai International Airport',
        city: 'Johor Bahru',
        country: 'Malaysia',
        aliases: ['Senai Airport', 'JB Airport', '新山机场']
      },
      {
        iata: 'PEN',
        icao: 'WMKP',
        name: 'Penang International Airport',
        city: 'Penang',
        country: 'Malaysia',
        aliases: ['Penang Airport', 'Bayan Lepas', '槟城机场']
      },
      {
        iata: 'KCH',
        icao: 'WBGG',
        name: 'Kuching International Airport',
        city: 'Kuching',
        country: 'Malaysia',
        aliases: ['Kuching Airport', '古晋机场']
      },
      {
        iata: 'BKI',
        icao: 'WBKK',
        name: 'Kota Kinabalu International Airport',
        city: 'Kota Kinabalu',
        country: 'Malaysia',
        aliases: ['KK Airport', 'Sabah Airport', '亚庇机场']
      },
      {
        iata: 'LGK',
        icao: 'WMKL',
        name: 'Langkawi International Airport',
        city: 'Langkawi',
        country: 'Malaysia',
        aliases: ['Langkawi Airport', '兰卡威机场']
      },
      {
        iata: 'IPH',
        icao: 'WMKI',
        name: 'Sultan Azlan Shah Airport',
        city: 'Ipoh',
        country: 'Malaysia',
        aliases: ['Ipoh Airport', '怡保机场']
      },
      {
        iata: 'AOR',
        icao: 'WMKA',
        name: 'Sultan Abdul Halim Airport',
        city: 'Alor Setar',
        country: 'Malaysia',
        aliases: ['Alor Setar Airport', '亚罗士打机场']
      },
      {
        iata: 'KBR',
        icao: 'WMKC',
        name: 'Sultan Ismail Petra Airport',
        city: 'Kota Bharu',
        country: 'Malaysia',
        aliases: ['Kota Bharu Airport', '哥打巴鲁机场']
      },
      
      // 新加坡机场
      {
        iata: 'SIN',
        icao: 'WSSS',
        name: 'Singapore Changi Airport',
        city: 'Singapore',
        country: 'Singapore',
        aliases: ['Changi Airport', 'Changi', '樟宜机场', '新加坡机场']
      },
      
      // 泰国机场
      {
        iata: 'BKK',
        icao: 'VTBS',
        name: 'Suvarnabhumi Airport',
        city: 'Bangkok',
        country: 'Thailand',
        aliases: ['Bangkok Airport', 'Suvarnabhumi', '素万那普机场', '曼谷机场']
      },
      {
        iata: 'DMK',
        icao: 'VTBD',
        name: 'Don Mueang International Airport',
        city: 'Bangkok',
        country: 'Thailand',
        aliases: ['Don Mueang', 'Bangkok Don Mueang', '廊曼机场']
      },
      {
        iata: 'HKT',
        icao: 'VTSP',
        name: 'Phuket International Airport',
        city: 'Phuket',
        country: 'Thailand',
        aliases: ['Phuket Airport', '普吉机场']
      },
      
      // 印尼机场
      {
        iata: 'CGK',
        icao: 'WIII',
        name: 'Soekarno-Hatta International Airport',
        city: 'Jakarta',
        country: 'Indonesia',
        aliases: ['Jakarta Airport', 'Soekarno-Hatta', '雅加达机场']
      },
      {
        iata: 'DPS',
        icao: 'WADD',
        name: 'Ngurah Rai International Airport',
        city: 'Denpasar',
        country: 'Indonesia',
        aliases: ['Bali Airport', 'Denpasar Airport', '巴厘岛机场']
      },
      
      // 菲律宾机场
      {
        iata: 'MNL',
        icao: 'RPLL',
        name: 'Ninoy Aquino International Airport',
        city: 'Manila',
        country: 'Philippines',
        aliases: ['Manila Airport', 'NAIA', '马尼拉机场']
      },
      
      // 越南机场
      {
        iata: 'SGN',
        icao: 'VVTS',
        name: 'Tan Son Nhat International Airport',
        city: 'Ho Chi Minh City',
        country: 'Vietnam',
        aliases: ['Ho Chi Minh Airport', 'Saigon Airport', '胡志明市机场']
      },
      {
        iata: 'HAN',
        icao: 'VVNB',
        name: 'Noi Bai International Airport',
        city: 'Hanoi',
        country: 'Vietnam',
        aliases: ['Hanoi Airport', '河内机场']
      },
      
      // 中国主要机场
      {
        iata: 'PEK',
        icao: 'ZBAA',
        name: 'Beijing Capital International Airport',
        city: 'Beijing',
        country: 'China',
        aliases: ['Beijing Airport', '北京首都机场', '首都机场']
      },
      {
        iata: 'PVG',
        icao: 'ZSPD',
        name: 'Shanghai Pudong International Airport',
        city: 'Shanghai',
        country: 'China',
        aliases: ['Shanghai Airport', 'Pudong Airport', '上海浦东机场', '浦东机场']
      },
      {
        iata: 'CAN',
        icao: 'ZGGG',
        name: 'Guangzhou Baiyun International Airport',
        city: 'Guangzhou',
        country: 'China',
        aliases: ['Guangzhou Airport', '广州白云机场', '白云机场']
      },
      {
        iata: 'SZX',
        icao: 'ZGSZ',
        name: 'Shenzhen Bao\'an International Airport',
        city: 'Shenzhen',
        country: 'China',
        aliases: ['Shenzhen Airport', '深圳宝安机场', '宝安机场']
      },
      
      // 香港机场
      {
        iata: 'HKG',
        icao: 'VHHH',
        name: 'Hong Kong International Airport',
        city: 'Hong Kong',
        country: 'Hong Kong',
        aliases: ['Hong Kong Airport', 'Chek Lap Kok', '香港国际机场', '香港机场']
      },
      
      // 台湾机场
      {
        iata: 'TPE',
        icao: 'RCTP',
        name: 'Taiwan Taoyuan International Airport',
        city: 'Taipei',
        country: 'Taiwan',
        aliases: ['Taipei Airport', 'Taoyuan Airport', '台北桃园机场', '桃园机场']
      },
      
      // 日本机场
      {
        iata: 'NRT',
        icao: 'RJAA',
        name: 'Narita International Airport',
        city: 'Tokyo',
        country: 'Japan',
        aliases: ['Tokyo Narita', 'Narita Airport', '成田机场', '东京成田机场']
      },
      {
        iata: 'HND',
        icao: 'RJTT',
        name: 'Tokyo Haneda Airport',
        city: 'Tokyo',
        country: 'Japan',
        aliases: ['Tokyo Haneda', 'Haneda Airport', '羽田机场', '东京羽田机场']
      },
      {
        iata: 'KIX',
        icao: 'RJBB',
        name: 'Kansai International Airport',
        city: 'Osaka',
        country: 'Japan',
        aliases: ['Osaka Airport', 'Kansai Airport', '关西机场', '大阪关西机场']
      },
      
      // 韩国机场
      {
        iata: 'ICN',
        icao: 'RKSI',
        name: 'Incheon International Airport',
        city: 'Seoul',
        country: 'South Korea',
        aliases: ['Seoul Airport', 'Incheon Airport', '仁川机场', '首尔仁川机场']
      },
      
      // 印度机场
      {
        iata: 'DEL',
        icao: 'VIDP',
        name: 'Indira Gandhi International Airport',
        city: 'New Delhi',
        country: 'India',
        aliases: ['Delhi Airport', 'New Delhi Airport', '德里机场', '新德里机场']
      },
      {
        iata: 'BOM',
        icao: 'VABB',
        name: 'Chhatrapati Shivaji Maharaj International Airport',
        city: 'Mumbai',
        country: 'India',
        aliases: ['Mumbai Airport', 'Bombay Airport', '孟买机场']
      },
      
      // 澳大利亚机场
      {
        iata: 'SYD',
        icao: 'YSSY',
        name: 'Sydney Kingsford Smith Airport',
        city: 'Sydney',
        country: 'Australia',
        aliases: ['Sydney Airport', 'Kingsford Smith', '悉尼机场']
      },
      {
        iata: 'MEL',
        icao: 'YMML',
        name: 'Melbourne Airport',
        city: 'Melbourne',
        country: 'Australia',
        aliases: ['Melbourne Airport', 'Tullamarine', '墨尔本机场']
      },
      
      // 欧洲主要机场
      {
        iata: 'LHR',
        icao: 'EGLL',
        name: 'London Heathrow Airport',
        city: 'London',
        country: 'United Kingdom',
        aliases: ['London Airport', 'Heathrow Airport', '伦敦希思罗机场', '希思罗机场']
      },
      {
        iata: 'CDG',
        icao: 'LFPG',
        name: 'Charles de Gaulle Airport',
        city: 'Paris',
        country: 'France',
        aliases: ['Paris Airport', 'Charles de Gaulle', '巴黎戴高乐机场', '戴高乐机场']
      },
      {
        iata: 'FRA',
        icao: 'EDDF',
        name: 'Frankfurt Airport',
        city: 'Frankfurt',
        country: 'Germany',
        aliases: ['Frankfurt Airport', '法兰克福机场']
      },
      {
        iata: 'AMS',
        icao: 'EHAM',
        name: 'Amsterdam Airport Schiphol',
        city: 'Amsterdam',
        country: 'Netherlands',
        aliases: ['Amsterdam Airport', 'Schiphol Airport', '阿姆斯特丹机场', '史基浦机场']
      },
      
      // 美国主要机场
      {
        iata: 'LAX',
        icao: 'KLAX',
        name: 'Los Angeles International Airport',
        city: 'Los Angeles',
        country: 'United States',
        aliases: ['Los Angeles Airport', 'LAX Airport', '洛杉矶机场']
      },
      {
        iata: 'JFK',
        icao: 'KJFK',
        name: 'John F. Kennedy International Airport',
        city: 'New York',
        country: 'United States',
        aliases: ['New York Airport', 'JFK Airport', '纽约肯尼迪机场', '肯尼迪机场']
      },
      {
        iata: 'SFO',
        icao: 'KSFO',
        name: 'San Francisco International Airport',
        city: 'San Francisco',
        country: 'United States',
        aliases: ['San Francisco Airport', '旧金山机场']
      },
      
      // 中东机场
      {
        iata: 'DXB',
        icao: 'OMDB',
        name: 'Dubai International Airport',
        city: 'Dubai',
        country: 'United Arab Emirates',
        aliases: ['Dubai Airport', '迪拜机场']
      },
      {
        iata: 'DOH',
        icao: 'OTHH',
        name: 'Hamad International Airport',
        city: 'Doha',
        country: 'Qatar',
        aliases: ['Doha Airport', '多哈机场']
      }
    ];
  }

  /**
   * 建立搜索索引
   */
  buildIndexes() {
    console.log('🔍 建立机场搜索索引...');
    
    this.airports.forEach((airport, index) => {
      // IATA代码索引
      if (airport.iata) {
        this.codeIndex.set(airport.iata.toLowerCase(), index);
      }
      
      // ICAO代码索引
      if (airport.icao) {
        this.codeIndex.set(airport.icao.toLowerCase(), index);
      }
      
      // 机场名称索引
      this.airportIndex.set(airport.name.toLowerCase(), index);
      
      // 城市索引
      this.cityIndex.set(airport.city.toLowerCase(), index);
      
      // 国家索引
      if (!this.countryIndex.has(airport.country.toLowerCase())) {
        this.countryIndex.set(airport.country.toLowerCase(), []);
      }
      this.countryIndex.get(airport.country.toLowerCase()).push(index);
      
      // 别名索引
      if (airport.aliases) {
        airport.aliases.forEach(alias => {
          this.airportIndex.set(alias.toLowerCase(), index);
        });
      }
    });
    
    console.log('✅ 搜索索引建立完成');
  }

  /**
   * 智能搜索机场
   */
  searchAirports(query, limit = 10) {
    if (!this.initialized) {
      console.warn('⚠️ 机场数据库未初始化');
      return [];
    }
    
    if (!query || query.trim().length === 0) {
      return [];
    }
    
    const searchTerm = query.toLowerCase().trim();
    const results = [];
    const seen = new Set();
    
    // 1. 精确匹配IATA/ICAO代码
    if (this.codeIndex.has(searchTerm)) {
      const index = this.codeIndex.get(searchTerm);
      results.push({
        airport: this.airports[index],
        score: 100,
        matchType: 'code'
      });
      seen.add(index);
    }
    
    // 2. 精确匹配机场名称或别名
    if (this.airportIndex.has(searchTerm)) {
      const index = this.airportIndex.get(searchTerm);
      if (!seen.has(index)) {
        results.push({
          airport: this.airports[index],
          score: 95,
          matchType: 'name'
        });
        seen.add(index);
      }
    }
    
    // 3. 精确匹配城市名称
    if (this.cityIndex.has(searchTerm)) {
      const index = this.cityIndex.get(searchTerm);
      if (!seen.has(index)) {
        results.push({
          airport: this.airports[index],
          score: 90,
          matchType: 'city'
        });
        seen.add(index);
      }
    }
    
    // 4. 模糊匹配
    this.airports.forEach((airport, index) => {
      if (seen.has(index)) return;
      
      let score = 0;
      let matchType = 'fuzzy';
      
      // 检查IATA/ICAO代码包含
      if (airport.iata && airport.iata.toLowerCase().includes(searchTerm)) {
        score = Math.max(score, 85);
        matchType = 'code_partial';
      }
      if (airport.icao && airport.icao.toLowerCase().includes(searchTerm)) {
        score = Math.max(score, 85);
        matchType = 'code_partial';
      }
      
      // 检查机场名称包含
      if (airport.name.toLowerCase().includes(searchTerm)) {
        score = Math.max(score, 80);
        matchType = 'name_partial';
      }
      
      // 检查城市名称包含
      if (airport.city.toLowerCase().includes(searchTerm)) {
        score = Math.max(score, 75);
        matchType = 'city_partial';
      }
      
      // 检查国家名称包含
      if (airport.country.toLowerCase().includes(searchTerm)) {
        score = Math.max(score, 70);
        matchType = 'country_partial';
      }
      
      // 检查别名包含
      if (airport.aliases) {
        airport.aliases.forEach(alias => {
          if (alias.toLowerCase().includes(searchTerm)) {
            score = Math.max(score, 85);
            matchType = 'alias_partial';
          }
        });
      }
      
      // 计算字符串相似度
      const nameSimilarity = this.calculateSimilarity(searchTerm, airport.name.toLowerCase());
      const citySimilarity = this.calculateSimilarity(searchTerm, airport.city.toLowerCase());
      
      if (nameSimilarity > 0.6) {
        score = Math.max(score, Math.floor(nameSimilarity * 70));
        matchType = 'name_similar';
      }
      
      if (citySimilarity > 0.6) {
        score = Math.max(score, Math.floor(citySimilarity * 65));
        matchType = 'city_similar';
      }
      
      if (score > 0) {
        results.push({
          airport,
          score,
          matchType
        });
      }
    });
    
    // 按分数排序并返回前N个结果
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * 计算字符串相似度（简单的Levenshtein距离）
   */
  calculateSimilarity(str1, str2) {
    const len1 = str1.length;
    const len2 = str2.length;
    
    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;
    
    const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
    
    for (let i = 0; i <= len1; i++) matrix[i][0] = i;
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;
    
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }
    
    const maxLen = Math.max(len1, len2);
    return (maxLen - matrix[len1][len2]) / maxLen;
  }

  /**
   * 获取马来西亚机场列表
   */
  getMalaysianAirports() {
    return this.airports.filter(airport => 
      airport.country.toLowerCase() === 'malaysia'
    );
  }

  /**
   * 根据国家获取机场列表
   */
  getAirportsByCountry(country) {
    const countryKey = country.toLowerCase();
    if (this.countryIndex.has(countryKey)) {
      const indexes = this.countryIndex.get(countryKey);
      return indexes.map(index => this.airports[index]);
    }
    return [];
  }
}

// 创建全局机场数据库实例
window.AirportDatabase = AirportDatabase;

// 自动初始化
const airportDB = new AirportDatabase();
airportDB.init().then(() => {
  console.log('🚀 机场数据库已准备就绪');
  console.log('使用 window.airportDB 访问机场数据库');
});

// 导出到全局
window.airportDB = airportDB;
