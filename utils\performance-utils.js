/**
 * 性能优化工具函数
 * 包含防抖、节流、内存管理等功能
 * 
 * <AUTHOR> AI Team
 * @version 3.1.0
 */

/**
 * 防抖函数 - 延迟执行，在指定时间内多次调用只执行最后一次
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行第一次调用
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(this, args);
    };
}

/**
 * 节流函数 - 限制函数执行频率
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 内存管理器 - 统一管理定时器和事件监听器
 */
class MemoryManager {
    constructor() {
        this.timers = new Set();
        this.intervals = new Set();
        this.listeners = new Map();
        this.observers = new Set();
        this.isDestroyed = false;
    }

    /**
     * 添加定时器到管理器
     * @param {number} timer - setTimeout返回的ID
     */
    addTimer(timer) {
        if (!this.isDestroyed) {
            this.timers.add(timer);
        }
    }

    /**
     * 添加间隔定时器到管理器
     * @param {number} interval - setInterval返回的ID
     */
    addInterval(interval) {
        if (!this.isDestroyed) {
            this.intervals.add(interval);
        }
    }

    /**
     * 添加事件监听器到管理器
     * @param {HTMLElement} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 事件选项
     */
    addListener(element, event, handler, options = {}) {
        if (!this.isDestroyed && element && event && handler) {
            const key = `${element.constructor.name}_${event}_${Date.now()}`;
            this.listeners.set(key, { element, event, handler, options });
            element.addEventListener(event, handler, options);
        }
    }

    /**
     * 添加观察者到管理器
     * @param {Observer} observer - 观察者对象（如MutationObserver）
     */
    addObserver(observer) {
        if (!this.isDestroyed) {
            this.observers.add(observer);
        }
    }

    /**
     * 移除特定的定时器
     * @param {number} timer - 定时器ID
     */
    removeTimer(timer) {
        if (this.timers.has(timer)) {
            clearTimeout(timer);
            this.timers.delete(timer);
        }
    }

    /**
     * 移除特定的间隔定时器
     * @param {number} interval - 间隔定时器ID
     */
    removeInterval(interval) {
        if (this.intervals.has(interval)) {
            clearInterval(interval);
            this.intervals.delete(interval);
        }
    }

    /**
     * 移除特定的事件监听器
     * @param {HTMLElement} element - DOM元素
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    removeListener(element, event, handler) {
        for (const [key, listener] of this.listeners.entries()) {
            if (listener.element === element && 
                listener.event === event && 
                listener.handler === handler) {
                element.removeEventListener(event, handler, listener.options);
                this.listeners.delete(key);
                break;
            }
        }
    }

    /**
     * 清理所有资源
     */
    cleanup() {
        if (this.isDestroyed) return;

        console.log('🧹 [MemoryManager] 开始清理资源...');

        // 清理所有定时器
        this.timers.forEach(timer => {
            clearTimeout(timer);
        });
        this.timers.clear();

        // 清理所有间隔定时器
        this.intervals.forEach(interval => {
            clearInterval(interval);
        });
        this.intervals.clear();

        // 移除所有事件监听器
        this.listeners.forEach(({ element, event, handler, options }) => {
            try {
                element.removeEventListener(event, handler, options);
            } catch (error) {
                console.warn('⚠️ [MemoryManager] 移除事件监听器失败:', error);
            }
        });
        this.listeners.clear();

        // 断开所有观察者
        this.observers.forEach(observer => {
            try {
                if (observer.disconnect) {
                    observer.disconnect();
                }
            } catch (error) {
                console.warn('⚠️ [MemoryManager] 断开观察者失败:', error);
            }
        });
        this.observers.clear();

        this.isDestroyed = true;
        console.log('✅ [MemoryManager] 资源清理完成');
    }

    /**
     * 获取当前资源使用情况
     * @returns {Object} 资源统计信息
     */
    getStats() {
        return {
            timers: this.timers.size,
            intervals: this.intervals.size,
            listeners: this.listeners.size,
            observers: this.observers.size,
            isDestroyed: this.isDestroyed
        };
    }
}

/**
 * 错误处理器 - 统一错误处理和用户提示
 */
class ErrorHandler {
    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     * @param {string} userMessage - 用户友好的错误消息
     * @param {Function} showMessageCallback - 显示消息的回调函数
     */
    static handle(error, context, userMessage, showMessageCallback) {
        console.error(`❌ [${context}] ${error.message}`, error);

        const friendlyMessage = this.getFriendlyMessage(error, userMessage);
        
        if (showMessageCallback && typeof showMessageCallback === 'function') {
            showMessageCallback('error', friendlyMessage);
        }

        // 尝试错误恢复
        this.attemptRecovery(error, context);

        // 错误上报（如果需要）
        this.reportError(error, context);
    }

    /**
     * 获取用户友好的错误消息
     * @param {Error} error - 错误对象
     * @param {string} fallback - 备用消息
     * @returns {string} 用户友好的错误消息
     */
    static getFriendlyMessage(error, fallback) {
        const errorMap = {
            'network': '网络连接问题，请检查网络连接后重试',
            'timeout': '操作超时，请稍后重试',
            'parse': '数据解析失败，请检查输入格式',
            'permission': '权限不足，请刷新页面后重试',
            'quota': 'AI服务配额已用完，请稍后再试',
            'api': 'AI服务暂时不可用，请稍后重试',
            'format': '输入内容格式不支持，请尝试更清晰的描述',
            'connection': '连接失败，请检查网络连接',
            'cors': '跨域请求被阻止，请检查网站设置'
        };

        const errorMessage = error.message.toLowerCase();
        
        for (const [key, message] of Object.entries(errorMap)) {
            if (errorMessage.includes(key)) {
                return message;
            }
        }

        return fallback || '操作失败，请重试';
    }

    /**
     * 尝试错误恢复
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    static attemptRecovery(error, context) {
        // 根据错误类型尝试不同的恢复策略
        const errorMessage = error.message.toLowerCase();

        if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
            // 网络错误：可以尝试重连
            console.log('🔄 [ErrorHandler] 检测到网络错误，建议重试');
        } else if (errorMessage.includes('quota') || errorMessage.includes('limit')) {
            // 配额错误：建议稍后重试
            console.log('⏳ [ErrorHandler] 检测到配额限制，建议稍后重试');
        } else if (errorMessage.includes('permission')) {
            // 权限错误：建议刷新页面
            console.log('🔄 [ErrorHandler] 检测到权限错误，建议刷新页面');
        }
    }

    /**
     * 错误上报（可选）
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    static reportError(error, context) {
        // 这里可以添加错误上报逻辑
        // 例如发送到错误监控服务
        console.log('📊 [ErrorHandler] 错误已记录:', { error: error.message, context });
    }
}

/**
 * DOM操作优化器
 */
class DOMOptimizer {
    /**
     * 批量DOM操作
     * @param {Function} operations - DOM操作函数
     */
    static batchOperations(operations) {
        requestAnimationFrame(() => {
            operations();
        });
    }

    /**
     * 防抖的DOM更新
     * @param {Function} updateFunction - 更新函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的更新函数
     */
    static debouncedUpdate(updateFunction, delay = 100) {
        return debounce(updateFunction, delay);
    }

    /**
     * 节流的DOM更新
     * @param {Function} updateFunction - 更新函数
     * @param {number} limit - 时间限制
     * @returns {Function} 节流后的更新函数
     */
    static throttledUpdate(updateFunction, limit = 100) {
        return throttle(updateFunction, limit);
    }
}

// 导出工具函数和类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        debounce,
        throttle,
        MemoryManager,
        ErrorHandler,
        DOMOptimizer
    };
} else {
    // 浏览器环境
    window.PerformanceUtils = {
        debounce,
        throttle,
        MemoryManager,
        ErrorHandler,
        DOMOptimizer
    };
}
