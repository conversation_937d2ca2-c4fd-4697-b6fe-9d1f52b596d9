/**
 * MDAC监控接口
 * 连接测试页面与MDAC AI Chrome扩展
 */
class MDACMonitorInterface {
  constructor() {
    this.extensionDetected = false;
    this.components = {
      fieldStatusIndicator: null,
      progressIndicator: null,
      controlPanel: null,
      testFramework: null
    };
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      startTime: null
    };
    this.fieldMonitors = new Map();
    this.init();
  }

  /**
   * 初始化监控接口
   */
  async init() {
    console.log('🔍 MDAC监控接口初始化中...');
    
    // 等待页面加载完成
    if (document.readyState !== 'complete') {
      await new Promise(resolve => {
        window.addEventListener('load', resolve);
      });
    }
    
    this.detectExtension();
    this.setupEventListeners();
    this.setupFieldMonitors();
    
    // 提供全局访问点
    window.mdacModularSidePanel = this;
    window.mdacMonitorInterface = this;
    
    console.log('✅ MDAC监控接口初始化完成');
  }

  /**
   * 检测MDAC AI Chrome扩展
   */
  detectExtension() {
    // 检查扩展UI组件是否存在
    const components = {
      fieldStatusIndicator: window.fieldStatusIndicator,
      progressIndicator: window.progressIndicator,
      controlPanel: window.controlPanel,
      testFramework: window.testFramework
    };
    
    const detectedComponents = Object.entries(components)
      .filter(([key, value]) => value !== null && value !== undefined);
    
    if (detectedComponents.length > 0) {
      this.extensionDetected = true;
      
      detectedComponents.forEach(([key, value]) => {
        this.components[key] = value;
      });
      
      console.log(`✅ MDAC AI Chrome扩展已检测到 (${detectedComponents.length}/4 组件)`);
      this.logToTestResults(`✅ 检测到扩展组件: ${detectedComponents.map(([key]) => key).join(', ')}`, 'success');
      
      return true;
    } else {
      console.log('⚠️ 未检测到MDAC AI Chrome扩展');
      this.logToTestResults('⚠️ 未检测到MDAC AI Chrome扩展，请确保扩展已加载', 'warning');
      
      // 尝试等待扩展加载
      setTimeout(() => this.detectExtension(), 2000);
      return false;
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听字段变化
    document.addEventListener('input', (e) => {
      if (e.target.matches('input, select, textarea')) {
        this.onFieldChange(e.target);
      }
    });

    // 监听字段焦点
    document.addEventListener('focus', (e) => {
      if (e.target.matches('input, select, textarea')) {
        this.onFieldFocus(e.target);
      }
    }, true);

    // 监听字段失焦
    document.addEventListener('blur', (e) => {
      if (e.target.matches('input, select, textarea')) {
        this.onFieldBlur(e.target);
      }
    }, true);
  }

  /**
   * 设置字段监控器
   */
  setupFieldMonitors() {
    const fields = document.querySelectorAll('input, select, textarea');
    
    fields.forEach(field => {
      if (field.id) {
        this.fieldMonitors.set(field.id, {
          element: field,
          lastValue: field.value,
          changeCount: 0,
          focusTime: null,
          totalFocusTime: 0
        });
      }
    });
    
    console.log(`📊 设置了 ${this.fieldMonitors.size} 个字段监控器`);
  }

  /**
   * 字段变化处理
   */
  onFieldChange(field) {
    const monitor = this.fieldMonitors.get(field.id);
    if (!monitor) return;
    
    monitor.changeCount++;
    monitor.lastValue = field.value;
    
    // 更新字段状态
    const status = field.value ? 'success' : 'error';
    const message = field.value ? '字段已填充' : '字段为空';
    
    this.updateFieldStatus(field.id, status, message);
    
    // 记录变化
    this.logToTestResults(`字段 ${field.id} 已更新: "${field.value}"`, status);
  }

  /**
   * 字段获得焦点处理
   */
  onFieldFocus(field) {
    const monitor = this.fieldMonitors.get(field.id);
    if (!monitor) return;
    
    monitor.focusTime = Date.now();
    this.updateFieldStatus(field.id, 'loading', '字段获得焦点');
  }

  /**
   * 字段失去焦点处理
   */
  onFieldBlur(field) {
    const monitor = this.fieldMonitors.get(field.id);
    if (!monitor) return;
    
    if (monitor.focusTime) {
      const focusTime = Date.now() - monitor.focusTime;
      monitor.totalFocusTime += focusTime;
      monitor.focusTime = null;
      
      this.logToTestResults(`字段 ${field.id} 焦点时间: ${focusTime}ms`, 'info');
    }
  }

  /**
   * 更新字段状态
   */
  updateFieldStatus(fieldId, status, message = '') {
    if (!this.extensionDetected) return;
    
    // 更新字段状态指示器
    if (this.components.fieldStatusIndicator) {
      this.components.fieldStatusIndicator.updateStatus(fieldId, status, message);
    }
    
    // 更新进度指示器中的字段状态
    if (this.components.progressIndicator) {
      this.components.progressIndicator.addFieldStatus(fieldId, status, message);
    }
  }

  /**
   * 更新测试进度
   */
  updateTestProgress(testName, passed, message = '') {
    this.testResults.totalTests++;
    
    if (passed) {
      this.testResults.passedTests++;
    } else {
      this.testResults.failedTests++;
    }
    
    // 更新进度指示器
    if (this.components.progressIndicator) {
      this.components.progressIndicator.show();
      this.components.progressIndicator.updateProgress(
        this.testResults.passedTests + this.testResults.failedTests,
        this.testResults.totalTests,
        `测试进度: ${this.testResults.passedTests}/${this.testResults.totalTests} 通过`
      );
    }
    
    // 记录测试结果
    const status = passed ? 'success' : 'error';
    this.logToTestResults(`${testName}: ${passed ? '通过' : '失败'} - ${message}`, status);
    
    // 显示通知
    this.showNotification(
      `测试 "${testName}" ${passed ? '通过' : '失败'}`,
      status
    );
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    if (!this.extensionDetected) {
      // 如果扩展未检测到，使用原生通知
      this.logToTestResults(message, type);
      return;
    }
    
    // 使用扩展的通知系统
    if (this.components.controlPanel && this.components.controlPanel.showNotification) {
      this.components.controlPanel.showNotification(message, type);
    } else if (window.mdacUIManager && window.mdacUIManager.showNotification) {
      window.mdacUIManager.showNotification(message, type);
    }
  }

  /**
   * 记录到测试结果
   */
  logToTestResults(message, status = 'info') {
    const logElement = document.getElementById('testLog');
    if (!logElement) {
      console.log(`[${status.toUpperCase()}] ${message}`);
      return;
    }
    
    const timestamp = new Date().toLocaleTimeString();
    const statusIcon = this.getStatusIcon(status);
    const statusClass = `status-${status}`;
    
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${statusClass}`;
    logEntry.innerHTML = `
      <span class="log-time">[${timestamp}]</span>
      <span class="log-icon">${statusIcon}</span>
      <span class="log-message">${message}</span>
    `;
    
    logElement.appendChild(logEntry);
    logElement.scrollTop = logElement.scrollHeight;
  }

  /**
   * 获取状态图标
   */
  getStatusIcon(status) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      loading: '🔄',
      pending: '⏳'
    };
    return icons[status] || 'ℹ️';
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTest() {
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      startTime: Date.now()
    };
    
    this.logToTestResults('🚀 开始MDAC AI扩展集成测试...', 'info');
    
    if (!this.extensionDetected) {
      this.updateTestProgress('扩展检测', false, '未检测到MDAC AI扩展');
      return;
    }
    
    // 测试1: 扩展组件检测
    const componentTests = [
      ['字段状态指示器', this.components.fieldStatusIndicator],
      ['进度指示器', this.components.progressIndicator],
      ['控制面板', this.components.controlPanel],
      ['测试框架', this.components.testFramework]
    ];
    
    for (const [name, component] of componentTests) {
      this.updateTestProgress(`${name}检测`, !!component, component ? '组件已加载' : '组件未找到');
      await this.delay(200);
    }
    
    // 测试2: 字段状态指示器功能
    if (this.components.fieldStatusIndicator) {
      this.logToTestResults('测试字段状态指示器功能...', 'info');
      
      const testFields = ['passNo', 'email', 'mobile', 'passExpiry'];
      
      for (const fieldId of testFields) {
        const field = document.getElementById(fieldId);
        if (field) {
          // 测试加载状态
          this.updateFieldStatus(fieldId, 'loading', '测试中...');
          await this.delay(300);
          
          // 测试成功状态
          this.updateFieldStatus(fieldId, 'success', '测试通过');
          this.updateTestProgress(`字段${fieldId}状态指示`, true, '状态更新正常');
        } else {
          this.updateTestProgress(`字段${fieldId}状态指示`, false, '字段不存在');
        }
        await this.delay(200);
      }
    }
    
    // 测试3: 进度指示器功能
    if (this.components.progressIndicator) {
      this.logToTestResults('测试进度指示器功能...', 'info');
      
      this.components.progressIndicator.show();
      this.components.progressIndicator.clearStatus();
      
      for (let i = 1; i <= 5; i++) {
        this.components.progressIndicator.updateProgress(i, 5, `测试步骤 ${i}/5`);
        this.updateTestProgress(`进度更新${i}`, true, `进度: ${i}/5`);
        await this.delay(300);
      }
    }
    
    // 测试4: 通知系统
    this.logToTestResults('测试通知系统...', 'info');
    this.showNotification('这是一个测试通知', 'info');
    this.updateTestProgress('通知系统', true, '通知发送成功');
    
    // 完成测试
    const duration = Date.now() - this.testResults.startTime;
    const successRate = (this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(1);
    
    this.logToTestResults(`🎉 集成测试完成！`, 'success');
    this.logToTestResults(`📊 测试结果: ${this.testResults.passedTests}/${this.testResults.totalTests} 通过 (${successRate}%)`, 'info');
    this.logToTestResults(`⏱️ 测试时间: ${duration}ms`, 'info');
    
    this.showNotification(`集成测试完成！成功率: ${successRate}%`, 'success');
  }

  /**
   * 运行扩展测试框架
   */
  runExtensionTests() {
    if (!this.extensionDetected) {
      this.logToTestResults('❌ 扩展未检测到，无法运行扩展测试', 'error');
      return;
    }
    
    if (this.components.testFramework && window.runAllTests) {
      this.logToTestResults('🧪 运行扩展测试框架...', 'info');
      window.runAllTests();
    } else {
      this.logToTestResults('⚠️ 扩展测试框架未找到', 'warning');
    }
  }

  /**
   * 获取监控统计
   */
  getMonitoringStats() {
    const stats = {
      extensionDetected: this.extensionDetected,
      components: Object.keys(this.components).filter(key => this.components[key]),
      fieldsMonitored: this.fieldMonitors.size,
      testResults: this.testResults,
      fieldStats: {}
    };
    
    // 收集字段统计
    for (const [fieldId, monitor] of this.fieldMonitors) {
      stats.fieldStats[fieldId] = {
        changeCount: monitor.changeCount,
        totalFocusTime: monitor.totalFocusTime,
        hasValue: !!monitor.lastValue
      };
    }
    
    return stats;
  }

  /**
   * 延迟工具
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清除测试日志
   */
  clearTestLog() {
    const logElement = document.getElementById('testLog');
    if (logElement) {
      logElement.innerHTML = '';
    }
    
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      startTime: null
    };
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  window.mdacMonitorInterface = new MDACMonitorInterface();
});

// 导出类
window.MDACMonitorInterface = MDACMonitorInterface;
