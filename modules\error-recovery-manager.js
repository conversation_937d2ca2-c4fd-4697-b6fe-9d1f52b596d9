/**
 * MDAC错误恢复管理器模块 - 兼容性存根
 * 提供错误恢复和系统稳定性功能的后备实现
 * 
 * 功能特性：
 * - 自动错误检测和恢复
 * - 网络连接监控
 * - 表单状态保存和恢复
 * - 全局实例自动创建
 * - 向后兼容现有代码
 * 
 * 创建时间: 2025-01-12
 * 作者: MDAC AI智能分析工具
 */

(function() {
    'use strict';
    
    /**
     * MDAC错误恢复管理器类
     * 提供智能错误检测、恢复和系统稳定性管理
     */
    class ErrorRecoveryManager {
        constructor() {
            this.isInitialized = false;
            this.errorHistory = [];
            this.recoveryStrategies = new Map();
            this.retryAttempts = new Map();
            this.maxRetries = 3;
            this.logger = window.mdacLogger || console;
            this.savedFormData = {};
            this.isMonitoring = false;
            
            this.initialize();
        }
        
        /**
         * 初始化错误恢复管理器
         */
        async initialize() {
            try {
                this.setupErrorHandlers();
                this.setupRecoveryStrategies();
                this.startErrorMonitoring();
                this.loadSavedFormData();
                
                this.isInitialized = true;
                this.log('info', 'ErrorRecoveryManager', '错误恢复管理器初始化成功');
            } catch (error) {
                this.log('error', 'ErrorRecoveryManager', '初始化失败', error);
            }
        }
        
        /**
         * 设置全局错误处理器
         */
        setupErrorHandlers() {
            // 监听未捕获的错误
            window.addEventListener('error', (event) => {
                this.handleError({
                    type: 'javascript',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error,
                    timestamp: Date.now()
                });
            });
            
            // 监听未捕获的Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError({
                    type: 'promise',
                    message: event.reason?.message || '未处理的Promise拒绝',
                    reason: event.reason,
                    timestamp: Date.now()
                });
            });
            
            // 监听网络错误
            window.addEventListener('offline', () => {
                this.handleNetworkError('offline');
            });
            
            window.addEventListener('online', () => {
                this.handleNetworkError('online');
            });
        }
        
        /**
         * 设置恢复策略
         */
        setupRecoveryStrategies() {
            // 网络错误恢复策略
            this.recoveryStrategies.set('network', {
                detect: (error) => error.type === 'network' || error.message?.includes('fetch'),
                recover: async (error) => {
                    this.log('info', 'ErrorRecoveryManager', '尝试网络错误恢复', error);
                    
                    // 等待网络恢复
                    await this.waitForNetworkReconnection();
                    
                    // 重试失败的请求
                    if (error.retryFunction) {
                        return await error.retryFunction();
                    }
                    
                    return true;
                }
            });
            
            // 模块加载错误恢复策略
            this.recoveryStrategies.set('module-loading', {
                detect: (error) => error.message?.includes('模块加载失败') || error.message?.includes('script'),
                recover: async (error) => {
                    this.log('info', 'ErrorRecoveryManager', '尝试模块加载错误恢复', error);
                    
                    // 重试加载模块
                    if (error.moduleUrl) {
                        const retryCount = this.retryAttempts.get(error.moduleUrl) || 0;
                        if (retryCount < this.maxRetries) {
                            this.retryAttempts.set(error.moduleUrl, retryCount + 1);
                            await this.delay(1000 * (retryCount + 1)); // 指数退避
                            return await this.retryModuleLoad(error.moduleUrl);
                        }
                    }
                    
                    return false;
                }
            });
            
            // 表单数据丢失恢复策略
            this.recoveryStrategies.set('form-data', {
                detect: (error) => error.type === 'form-data-lost',
                recover: async (error) => {
                    this.log('info', 'ErrorRecoveryManager', '尝试表单数据恢复', error);
                    
                    // 从本地存储恢复表单数据
                    const savedData = this.getSavedFormData();
                    if (savedData && Object.keys(savedData).length > 0) {
                        await this.restoreFormData(savedData);
                        return true;
                    }
                    
                    return false;
                }
            });
            
            // Chrome扩展通信错误恢复策略
            this.recoveryStrategies.set('chrome-communication', {
                detect: (error) => error.message?.includes('Could not establish connection') || 
                                  error.message?.includes('Receiving end does not exist'),
                recover: async (error) => {
                    this.log('info', 'ErrorRecoveryManager', '尝试Chrome通信错误恢复', error);
                    
                    // 等待一段时间后重试
                    await this.delay(2000);
                    
                    // 检查扩展是否可用
                    if (typeof chrome !== 'undefined' && chrome.runtime) {
                        try {
                            // 尝试发送测试消息
                            await new Promise((resolve, reject) => {
                                chrome.runtime.sendMessage({ action: 'ping' }, (response) => {
                                    if (chrome.runtime.lastError) {
                                        reject(chrome.runtime.lastError);
                                    } else {
                                        resolve(response);
                                    }
                                });
                            });
                            return true;
                        } catch (testError) {
                            this.log('warn', 'ErrorRecoveryManager', '扩展通信仍然失败', testError);
                        }
                    }
                    
                    return false;
                }
            });
        }
        
        /**
         * 开始错误监控
         */
        startErrorMonitoring() {
            if (this.isMonitoring) return;
            
            this.isMonitoring = true;
            
            // 定期保存表单数据
            setInterval(() => {
                this.saveCurrentFormData();
            }, 30000); // 每30秒保存一次
            
            // 定期清理错误历史
            setInterval(() => {
                this.cleanupErrorHistory();
            }, 300000); // 每5分钟清理一次
            
            this.log('info', 'ErrorRecoveryManager', '错误监控已启动');
        }
        
        /**
         * 处理错误
         * @param {Object} errorInfo - 错误信息
         */
        async handleError(errorInfo) {
            // 记录错误
            this.errorHistory.push(errorInfo);
            this.log('error', 'ErrorRecoveryManager', '捕获到错误', errorInfo);
            
            // 尝试恢复
            const recovered = await this.attemptRecovery(errorInfo);
            
            if (recovered) {
                this.log('info', 'ErrorRecoveryManager', '错误恢复成功', errorInfo);
            } else {
                this.log('warn', 'ErrorRecoveryManager', '错误恢复失败', errorInfo);
                
                // 如果恢复失败，通知用户
                this.notifyUserOfError(errorInfo);
            }
        }
        
        /**
         * 尝试错误恢复
         * @param {Object} errorInfo - 错误信息
         * @returns {Promise<boolean>} 恢复是否成功
         */
        async attemptRecovery(errorInfo) {
            for (const [strategyName, strategy] of this.recoveryStrategies) {
                if (strategy.detect(errorInfo)) {
                    this.log('info', 'ErrorRecoveryManager', `使用恢复策略: ${strategyName}`);
                    
                    try {
                        const result = await strategy.recover(errorInfo);
                        if (result) {
                            return true;
                        }
                    } catch (recoveryError) {
                        this.log('error', 'ErrorRecoveryManager', `恢复策略 ${strategyName} 执行失败`, recoveryError);
                    }
                }
            }
            
            return false;
        }
        
        /**
         * 处理网络错误
         * @param {string} status - 网络状态 ('online' | 'offline')
         */
        handleNetworkError(status) {
            this.log('info', 'ErrorRecoveryManager', `网络状态变化: ${status}`);
            
            if (status === 'offline') {
                // 保存当前表单数据
                this.saveCurrentFormData();
                
                // 显示离线提示
                this.showOfflineNotification();
            } else if (status === 'online') {
                // 隐藏离线提示
                this.hideOfflineNotification();
                
                // 重试失败的操作
                this.retryFailedOperations();
            }
        }
        
        /**
         * 等待网络重新连接
         * @param {number} timeout - 超时时间（毫秒）
         * @returns {Promise<boolean>} 是否成功重连
         */
        async waitForNetworkReconnection(timeout = 30000) {
            const startTime = Date.now();
            
            while (Date.now() - startTime < timeout) {
                if (navigator.onLine) {
                    // 尝试网络请求验证连接
                    try {
                        await fetch('/', { method: 'HEAD', cache: 'no-cache' });
                        return true;
                    } catch (error) {
                        // 继续等待
                    }
                }
                
                await this.delay(1000);
            }
            
            return false;
        }
        
        /**
         * 重试模块加载
         * @param {string} moduleUrl - 模块URL
         * @returns {Promise<boolean>} 加载是否成功
         */
        async retryModuleLoad(moduleUrl) {
            return new Promise((resolve) => {
                const script = document.createElement('script');
                script.src = moduleUrl;
                script.onload = () => {
                    this.log('info', 'ErrorRecoveryManager', `模块重试加载成功: ${moduleUrl}`);
                    resolve(true);
                };
                script.onerror = () => {
                    this.log('error', 'ErrorRecoveryManager', `模块重试加载失败: ${moduleUrl}`);
                    resolve(false);
                };
                document.head.appendChild(script);
            });
        }
        
        /**
         * 保存当前表单数据
         */
        saveCurrentFormData() {
            try {
                const formData = {};
                const inputs = document.querySelectorAll('input, select, textarea');
                
                inputs.forEach((input) => {
                    if (input.name || input.id) {
                        const key = input.name || input.id;
                        formData[key] = {
                            value: input.value,
                            type: input.type,
                            timestamp: Date.now()
                        };
                    }
                });
                
                if (Object.keys(formData).length > 0) {
                    localStorage.setItem('mdac_form_backup', JSON.stringify(formData));
                    this.savedFormData = formData;
                    this.log('debug', 'ErrorRecoveryManager', '表单数据已保存');
                }
            } catch (error) {
                this.log('error', 'ErrorRecoveryManager', '保存表单数据失败', error);
            }
        }
        
        /**
         * 加载已保存的表单数据
         */
        loadSavedFormData() {
            try {
                const saved = localStorage.getItem('mdac_form_backup');
                if (saved) {
                    this.savedFormData = JSON.parse(saved);
                    this.log('info', 'ErrorRecoveryManager', '已加载保存的表单数据');
                }
            } catch (error) {
                this.log('error', 'ErrorRecoveryManager', '加载表单数据失败', error);
            }
        }
        
        /**
         * 获取已保存的表单数据
         * @returns {Object} 表单数据
         */
        getSavedFormData() {
            return this.savedFormData;
        }
        
        /**
         * 恢复表单数据
         * @param {Object} formData - 表单数据
         */
        async restoreFormData(formData) {
            try {
                let restoredCount = 0;
                
                for (const [key, data] of Object.entries(formData)) {
                    const input = document.querySelector(`[name="${key}"], #${key}`);
                    if (input && !input.value) {
                        input.value = data.value;
                        restoredCount++;
                        
                        // 触发change事件
                        const event = new Event('change', { bubbles: true });
                        input.dispatchEvent(event);
                    }
                }
                
                if (restoredCount > 0) {
                    this.log('info', 'ErrorRecoveryManager', `成功恢复 ${restoredCount} 个表单字段`);
                    this.showRestoreNotification(restoredCount);
                }
            } catch (error) {
                this.log('error', 'ErrorRecoveryManager', '恢复表单数据失败', error);
            }
        }
        
        /**
         * 显示离线通知
         */
        showOfflineNotification() {
            this.showNotification('网络连接已断开，数据已自动保存', 'warning', 0);
        }
        
        /**
         * 隐藏离线通知
         */
        hideOfflineNotification() {
            this.hideNotification();
            this.showNotification('网络连接已恢复', 'success', 3000);
        }
        
        /**
         * 显示恢复通知
         * @param {number} count - 恢复的字段数量
         */
        showRestoreNotification(count) {
            this.showNotification(`已恢复 ${count} 个表单字段的数据`, 'info', 5000);
        }
        
        /**
         * 显示通知
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         * @param {number} duration - 显示时长（0为永久显示）
         */
        showNotification(message, type = 'info', duration = 3000) {
            // 创建或更新通知元素
            let notification = document.getElementById('mdac-error-recovery-notification');
            
            if (!notification) {
                notification = document.createElement('div');
                notification.id = 'mdac-error-recovery-notification';
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    padding: 12px 16px;
                    border-radius: 4px;
                    color: white;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                    max-width: 300px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                    transition: opacity 0.3s ease;
                `;
                document.body.appendChild(notification);
            }
            
            // 设置样式和内容
            const colors = {
                info: '#2196F3',
                success: '#4CAF50',
                warning: '#FF9800',
                error: '#F44336'
            };
            
            notification.style.backgroundColor = colors[type] || colors.info;
            notification.textContent = message;
            notification.style.opacity = '1';
            
            // 设置自动隐藏
            if (duration > 0) {
                setTimeout(() => {
                    if (notification) {
                        notification.style.opacity = '0';
                        setTimeout(() => {
                            if (notification && notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }
                }, duration);
            }
        }
        
        /**
         * 隐藏通知
         */
        hideNotification() {
            const notification = document.getElementById('mdac-error-recovery-notification');
            if (notification) {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification && notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }
        
        /**
         * 通知用户错误
         * @param {Object} errorInfo - 错误信息
         */
        notifyUserOfError(errorInfo) {
            const userMessage = this.getUserFriendlyErrorMessage(errorInfo);
            this.showNotification(userMessage, 'error', 8000);
        }
        
        /**
         * 获取用户友好的错误消息
         * @param {Object} errorInfo - 错误信息
         * @returns {string} 用户友好的错误消息
         */
        getUserFriendlyErrorMessage(errorInfo) {
            if (errorInfo.type === 'network') {
                return '网络连接有问题，请检查网络设置';
            } else if (errorInfo.message?.includes('模块加载失败')) {
                return '部分功能加载失败，请刷新页面重试';
            } else if (errorInfo.message?.includes('Could not establish connection')) {
                return '与扩展的连接中断，请重新打开侧边栏';
            } else {
                return '发生了意外错误，数据已自动保存';
            }
        }
        
        /**
         * 重试失败的操作
         */
        async retryFailedOperations() {
            this.log('info', 'ErrorRecoveryManager', '开始重试失败的操作');
            
            // 这里可以添加重试逻辑
            // 例如：重新发送失败的请求、重新初始化模块等
        }
        
        /**
         * 清理错误历史
         */
        cleanupErrorHistory() {
            const maxAge = 24 * 60 * 60 * 1000; // 24小时
            const now = Date.now();
            
            this.errorHistory = this.errorHistory.filter(error => {
                return (now - error.timestamp) < maxAge;
            });
            
            this.log('debug', 'ErrorRecoveryManager', `错误历史清理完成，保留 ${this.errorHistory.length} 条记录`);
        }
        
        /**
         * 获取错误统计
         * @returns {Object} 错误统计信息
         */
        getErrorStats() {
            const stats = {
                total: this.errorHistory.length,
                byType: {},
                recent: 0
            };
            
            const oneHourAgo = Date.now() - (60 * 60 * 1000);
            
            this.errorHistory.forEach(error => {
                // 按类型统计
                stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
                
                // 最近一小时的错误
                if (error.timestamp > oneHourAgo) {
                    stats.recent++;
                }
            });
            
            return stats;
        }
        
        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} 延迟Promise
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        /**
         * 日志记录方法
         * @param {string} level - 日志级别
         * @param {string} module - 模块名
         * @param {string} message - 消息
         * @param {*} data - 数据
         */
        log(level, module, message, data = null) {
            if (this.logger && typeof this.logger.log === 'function') {
                this.logger.log(level, module, message, data);
            } else {
                console[level](`[${module}] ${message}`, data);
            }
        }
    }
    
    // 检查是否已存在ErrorRecoveryManager，避免重复定义
    if (typeof window.ErrorRecoveryManager === 'undefined') {
        // 将ErrorRecoveryManager类挂载到全局window对象
        window.ErrorRecoveryManager = ErrorRecoveryManager;
        
        // 创建全局实例供其他模块使用
        if (typeof window.mdacErrorRecovery === 'undefined') {
            window.mdacErrorRecovery = new ErrorRecoveryManager();
            console.log('✅ [ErrorRecoveryManager] 全局实例创建成功');
        }
    } else {
        console.log('✅ [ErrorRecoveryManager] 类已存在，跳过重复定义');
    }
    
})();
