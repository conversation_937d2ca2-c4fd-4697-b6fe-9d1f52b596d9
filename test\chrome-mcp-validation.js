/**
 * MDAC AI Chrome扩展验证系统
 * 使用Chrome MCP工具进行自动化验证
 * 
 * <AUTHOR> AI Team
 * @version 3.0.0
 */

class MDACValidationSystem {
    constructor() {
        this.testResults = [];
        this.currentTest = null;
        this.mdacUrl = 'https://imigresen-online.imi.gov.my/mdac/main?registerMain';
        
        // 测试数据
        this.testData = {
            personal: {
                fullName: '<PERSON>',
                passportNo: 'A12345678',
                nationality: 'MY',
                dob: '01/01/1990',
                sex: 'M',
                passExpiry: '01/01/2030',
                email: '<EMAIL>',
                confirmEmail: '<EMAIL>'
            },
            travel: {
                arrivalDate: '15/01/2025',
                departureDate: '25/01/2025',
                flightNo: 'MH123',
                modeOfTravel: '1', // 飞机
                embark: 'KLIA',
                accommodationStay: '1', // 酒店
                accommodationAddress1: '123 Test Street',
                accommodationAddress2: 'Test Area',
                accommodationState: 'KUL', // 吉隆坡
                accommodationCity: 'KL001', // 吉隆坡市
                accommodationPostcode: '50000'
            }
        };
    }

    /**
     * 开始验证流程
     */
    async startValidation() {
        console.log('🚀 [MDACValidation] 开始MDAC AI Chrome扩展验证...');
        
        try {
            // 1. 导航到MDAC网站
            await this.navigateToMDAC();
            
            // 2. 验证扩展加载
            await this.verifyExtensionLoaded();
            
            // 3. 测试自动解析功能
            await this.testAutoParseFunction();
            
            // 4. 测试日期字段填充
            await this.testDateFieldFilling();
            
            // 5. 测试州属城市同步
            await this.testStateCity Sync();
            
            // 6. 测试完整表单填充
            await this.testCompleteFormFilling();
            
            // 7. 生成验证报告
            this.generateValidationReport();
            
        } catch (error) {
            console.error('❌ [MDACValidation] 验证过程失败:', error);
            this.addTestResult('VALIDATION_ERROR', false, error.message);
        }
    }

    /**
     * 导航到MDAC网站
     */
    async navigateToMDAC() {
        this.currentTest = 'NAVIGATION';
        console.log('🌐 [MDACValidation] 导航到MDAC网站...');
        
        try {
            // 使用Chrome MCP导航
            const result = await chrome.runtime.sendMessage({
                action: 'navigate',
                url: this.mdacUrl
            });
            
            if (result.success) {
                await this.delay(3000); // 等待页面加载
                this.addTestResult('NAVIGATION', true, 'MDAC网站导航成功');
            } else {
                throw new Error('导航失败');
            }
        } catch (error) {
            this.addTestResult('NAVIGATION', false, `导航失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 验证扩展是否正确加载
     */
    async verifyExtensionLoaded() {
        this.currentTest = 'EXTENSION_LOAD';
        console.log('🔍 [MDACValidation] 验证扩展加载状态...');
        
        try {
            // 检查侧边栏是否存在
            const sidePanel = document.querySelector('#mdac-sidepanel');
            const contentScript = window.MDACContentScript;
            
            if (sidePanel && contentScript) {
                this.addTestResult('EXTENSION_LOAD', true, '扩展加载成功');
            } else {
                throw new Error('扩展未正确加载');
            }
        } catch (error) {
            this.addTestResult('EXTENSION_LOAD', false, `扩展加载失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 测试自动解析功能
     */
    async testAutoParseFunction() {
        this.currentTest = 'AUTO_PARSE';
        console.log('🧠 [MDACValidation] 测试自动解析功能...');
        
        try {
            // 测试个人信息自动解析
            const personalInput = document.getElementById('personalInfoInput');
            if (personalInput) {
                // 输入测试数据
                const testText = `姓名: ${this.testData.personal.fullName}\n护照号: ${this.testData.personal.passportNo}\n国籍: 马来西亚\n出生日期: ${this.testData.personal.dob}`;
                
                personalInput.value = testText;
                personalInput.dispatchEvent(new Event('input', { bubbles: true }));
                
                // 等待自动解析
                await this.delay(2000);
                
                // 检查解析结果
                const nameField = document.getElementById('fullName');
                if (nameField && nameField.value.includes('John')) {
                    this.addTestResult('AUTO_PARSE_PERSONAL', true, '个人信息自动解析成功');
                } else {
                    this.addTestResult('AUTO_PARSE_PERSONAL', false, '个人信息自动解析失败');
                }
            }
            
            // 测试旅行信息自动解析
            const travelInput = document.getElementById('travelInfoInput');
            if (travelInput) {
                const testText = `航班: ${this.testData.travel.flightNo}\n到达日期: ${this.testData.travel.arrivalDate}\n离开日期: ${this.testData.travel.departureDate}`;
                
                travelInput.value = testText;
                travelInput.dispatchEvent(new Event('input', { bubbles: true }));
                
                await this.delay(2000);
                
                const flightField = document.getElementById('flightNo');
                if (flightField && flightField.value === this.testData.travel.flightNo) {
                    this.addTestResult('AUTO_PARSE_TRAVEL', true, '旅行信息自动解析成功');
                } else {
                    this.addTestResult('AUTO_PARSE_TRAVEL', false, '旅行信息自动解析失败');
                }
            }
            
        } catch (error) {
            this.addTestResult('AUTO_PARSE', false, `自动解析测试失败: ${error.message}`);
        }
    }

    /**
     * 测试日期字段填充
     */
    async testDateFieldFilling() {
        this.currentTest = 'DATE_FILLING';
        console.log('📅 [MDACValidation] 测试日期字段填充...');
        
        try {
            // 测试到达日期填充
            const arrivalField = document.getElementById('arrivalDate');
            if (arrivalField) {
                // 模拟填充
                const event = new CustomEvent('fillField', {
                    detail: {
                        fieldId: 'arrivalDate',
                        value: this.testData.travel.arrivalDate
                    }
                });
                document.dispatchEvent(event);
                
                await this.delay(1000);
                
                if (arrivalField.value.includes('15/01/2025') || arrivalField.value.includes('2025-01-15')) {
                    this.addTestResult('DATE_ARRIVAL', true, '到达日期填充成功');
                } else {
                    this.addTestResult('DATE_ARRIVAL', false, '到达日期填充失败');
                }
            }
            
            // 测试离开日期填充
            const departureField = document.getElementById('departureDate');
            if (departureField) {
                const event = new CustomEvent('fillField', {
                    detail: {
                        fieldId: 'departureDate',
                        value: this.testData.travel.departureDate
                    }
                });
                document.dispatchEvent(event);
                
                await this.delay(1000);
                
                if (departureField.value.includes('25/01/2025') || departureField.value.includes('2025-01-25')) {
                    this.addTestResult('DATE_DEPARTURE', true, '离开日期填充成功');
                } else {
                    this.addTestResult('DATE_DEPARTURE', false, '离开日期填充失败');
                }
            }
            
        } catch (error) {
            this.addTestResult('DATE_FILLING', false, `日期填充测试失败: ${error.message}`);
        }
    }

    /**
     * 测试州属城市同步
     */
    async testStateCitySync() {
        this.currentTest = 'STATE_CITY_SYNC';
        console.log('🌏 [MDACValidation] 测试州属城市同步...');
        
        try {
            const stateField = document.getElementById('accommodationState');
            const cityField = document.getElementById('accommodationCity');
            
            if (stateField && cityField) {
                // 选择州属
                stateField.value = this.testData.travel.accommodationState;
                stateField.dispatchEvent(new Event('change', { bubbles: true }));
                
                await this.delay(500);
                
                // 检查城市选项是否更新
                const cityOptions = cityField.querySelectorAll('option');
                if (cityOptions.length > 1) { // 除了默认选项外还有其他选项
                    this.addTestResult('STATE_CITY_SYNC', true, '州属城市同步成功');
                } else {
                    this.addTestResult('STATE_CITY_SYNC', false, '州属城市同步失败');
                }
            }
            
        } catch (error) {
            this.addTestResult('STATE_CITY_SYNC', false, `州属城市同步测试失败: ${error.message}`);
        }
    }

    /**
     * 测试完整表单填充
     */
    async testCompleteFormFilling() {
        this.currentTest = 'COMPLETE_FORM';
        console.log('📋 [MDACValidation] 测试完整表单填充...');
        
        try {
            // 触发"更新到MDAC"功能
            const updateBtn = document.getElementById('updateToMDACBtn');
            if (updateBtn) {
                updateBtn.click();
                await this.delay(2000);
                
                // 检查关键字段是否填充
                const fieldsToCheck = [
                    'fullName', 'passportNo', 'nationality', 'dob',
                    'arrivalDate', 'departureDate', 'accommodationState'
                ];
                
                let filledCount = 0;
                fieldsToCheck.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field && field.value) {
                        filledCount++;
                    }
                });
                
                const successRate = (filledCount / fieldsToCheck.length) * 100;
                if (successRate >= 70) {
                    this.addTestResult('COMPLETE_FORM', true, `表单填充成功率: ${successRate.toFixed(1)}%`);
                } else {
                    this.addTestResult('COMPLETE_FORM', false, `表单填充成功率过低: ${successRate.toFixed(1)}%`);
                }
            }
            
        } catch (error) {
            this.addTestResult('COMPLETE_FORM', false, `完整表单填充测试失败: ${error.message}`);
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, success, message) {
        this.testResults.push({
            test: testName,
            success: success,
            message: message,
            timestamp: new Date().toISOString()
        });
        
        const status = success ? '✅' : '❌';
        console.log(`${status} [${testName}] ${message}`);
    }

    /**
     * 生成验证报告
     */
    generateValidationReport() {
        console.log('📊 [MDACValidation] 生成验证报告...');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const successRate = (passedTests / totalTests) * 100;
        
        const report = {
            summary: {
                totalTests: totalTests,
                passedTests: passedTests,
                failedTests: totalTests - passedTests,
                successRate: successRate.toFixed(1) + '%'
            },
            details: this.testResults,
            timestamp: new Date().toISOString()
        };
        
        console.log('📋 验证报告:', report);
        
        // 保存报告到本地存储
        localStorage.setItem('mdac_validation_report', JSON.stringify(report));
        
        return report;
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 导出验证系统
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MDACValidationSystem;
} else {
    window.MDACValidationSystem = MDACValidationSystem;
}
