/**
 * MDAC字段修复解决方案
 * 针对passExpDte、region、mobile、arrDt、depDt等问题字段的专门修复
 */

class MDACFieldFixer {
  constructor() {
    this.problemFields = [
      'passExpDte',  // 护照到期日期
      'region',      // 国家代码
      'mobile',      // 手机号码
      'arrDt',       // 到达日期
      'depDt'        // 离开日期
    ];
    
    this.fieldElements = {};
    this.fixResults = {
      success: [],
      failure: []
    };
  }
  
  /**
   * 初始化修复工具
   */
  async init() {
    console.log('🔧 MDAC字段修复工具初始化...');
    
    // 查找字段元素
    this.findFieldElements();
    
    console.log('✅ 修复工具初始化完成');
  }
  
  /**
   * 查找字段元素
   */
  findFieldElements() {
    console.log('🔍 查找问题字段元素...');
    
    this.problemFields.forEach(fieldId => {
      const element = document.getElementById(fieldId);
      
      if (element) {
        this.fieldElements[fieldId] = element;
        console.log(`✅ 找到字段 ${fieldId}: ${element.tagName}[${element.type || 'N/A'}]`);
      } else {
        console.warn(`⚠️ 未找到字段 ${fieldId}`);
      }
    });
    
    console.log('📊 字段查找结果:', Object.keys(this.fieldElements).length, '/', this.problemFields.length);
  }
  
  /**
   * 修复所有问题字段
   */
  async fixAllFields(testData) {
    console.log('🔧 开始修复所有问题字段...');
    
    // 默认测试数据
    const defaultTestData = {
      passExpDte: '31/12/2030',
      region: '+60',
      mobile: '167372551',
      arrDt: '01/08/2025',
      depDt: '15/08/2025'
    };
    
    // 合并测试数据
    const data = { ...defaultTestData, ...testData };
    
    // 重置结果
    this.fixResults = {
      success: [],
      failure: []
    };
    
    // 修复每个字段
    for (const fieldId of this.problemFields) {
      const element = this.fieldElements[fieldId];
      if (!element) {
        this.fixResults.failure.push({
          fieldId,
          reason: '字段元素不存在'
        });
        continue;
      }
      
      const value = data[fieldId];
      if (!value) {
        this.fixResults.failure.push({
          fieldId,
          reason: '没有提供测试数据'
        });
        continue;
      }
      
      console.log(`🔧 修复字段 ${fieldId}: "${value}"`);
      
      try {
        let success = false;
        
        // 根据字段类型选择修复方法
        switch (fieldId) {
          case 'passExpDte':
            success = await this.fixPassportExpiryField(element, value);
            break;
            
          case 'region':
            success = await this.fixRegionField(element, value);
            break;
            
          case 'mobile':
            success = await this.fixMobileField(element, value);
            break;
            
          case 'arrDt':
            success = await this.fixArrivalDateField(element, value);
            break;
            
          case 'depDt':
            success = await this.fixDepartureDateField(element, value);
            break;
        }
        
        if (success) {
          this.fixResults.success.push(fieldId);
          console.log(`✅ 字段 ${fieldId} 修复成功`);
        } else {
          this.fixResults.failure.push({
            fieldId,
            reason: '修复方法未能成功填充字段'
          });
          console.warn(`❌ 字段 ${fieldId} 修复失败`);
        }
      } catch (error) {
        this.fixResults.failure.push({
          fieldId,
          reason: error.message
        });
        console.error(`❌ 字段 ${fieldId} 修复异常:`, error);
      }
    }
    
    // 生成修复报告
    this.generateFixReport();
    
    return this.fixResults;
  }
  
  /**
   * 修复护照到期日期字段
   */
  async fixPassportExpiryField(field, value) {
    console.log('🔧 修复护照到期日期字段...');
    
    try {
      // 确保日期格式为DD/MM/YYYY
      const formattedDate = this.formatDateForMDAC(value);
      
      // 清空字段
      field.value = '';
      
      // 触发焦点事件
      field.focus();
      field.dispatchEvent(new Event('focus', { bubbles: true }));
      await this.delay(100);
      
      // 设置值
      field.value = formattedDate;
      
      // 触发事件序列
      field.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('change', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('blur', { bubbles: true }));
      
      // 验证填充结果
      return field.value === formattedDate;
    } catch (error) {
      console.error('修复护照到期日期字段失败:', error);
      return false;
    }
  }
  
  /**
   * 修复国家代码字段
   */
  async fixRegionField(field, value) {
    console.log('🔧 修复国家代码字段...');
    
    try {
      // 直接设置值
      field.value = value;
      
      // 触发change事件
      field.dispatchEvent(new Event('change', { bubbles: true }));
      
      // 检查是否使用Select2
      if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        try {
          $(field).val(value).trigger('change');
        } catch (error) {
          console.warn('Select2设置失败:', error);
        }
      }
      
      // 等待可能的依赖字段更新
      await this.delay(200);
      
      return field.value === value;
    } catch (error) {
      console.error('修复国家代码字段失败:', error);
      return false;
    }
  }
  
  /**
   * 修复手机号码字段
   */
  async fixMobileField(field, value) {
    console.log('🔧 修复手机号码字段...');
    
    try {
      // 清除现有值
      field.value = '';
      
      // 确保只包含数字
      const numericValue = value.replace(/\D/g, '');
      
      // 触发焦点事件
      field.focus();
      field.dispatchEvent(new Event('focus', { bubbles: true }));
      await this.delay(100);
      
      // 设置值
      field.value = numericValue;
      
      // 触发事件序列
      field.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('change', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('blur', { bubbles: true }));
      
      return field.value === numericValue;
    } catch (error) {
      console.error('修复手机号码字段失败:', error);
      return false;
    }
  }
  
  /**
   * 修复到达日期字段
   */
  async fixArrivalDateField(field, value) {
    console.log('🔧 修复到达日期字段...');
    
    try {
      // 确保日期格式为DD/MM/YYYY
      const formattedDate = this.formatDateForMDAC(value);
      
      // 清空字段
      field.value = '';
      
      // 触发焦点事件
      field.focus();
      field.dispatchEvent(new Event('focus', { bubbles: true }));
      await this.delay(100);
      
      // 设置值
      field.value = formattedDate;
      
      // 触发事件序列
      field.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('change', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('blur', { bubbles: true }));
      
      // 验证填充结果
      return field.value === formattedDate;
    } catch (error) {
      console.error('修复到达日期字段失败:', error);
      return false;
    }
  }
  
  /**
   * 修复离开日期字段
   */
  async fixDepartureDateField(field, value) {
    console.log('🔧 修复离开日期字段...');
    
    try {
      // 确保日期格式为DD/MM/YYYY
      const formattedDate = this.formatDateForMDAC(value);
      
      // 清空字段
      field.value = '';
      
      // 触发焦点事件
      field.focus();
      field.dispatchEvent(new Event('focus', { bubbles: true }));
      await this.delay(100);
      
      // 设置值
      field.value = formattedDate;
      
      // 触发事件序列
      field.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('change', { bubbles: true }));
      await this.delay(50);
      field.dispatchEvent(new Event('blur', { bubbles: true }));
      
      // 验证填充结果
      return field.value === formattedDate;
    } catch (error) {
      console.error('修复离开日期字段失败:', error);
      return false;
    }
  }
  
  /**
   * 格式化日期为MDAC网站要求的DD/MM/YYYY格式
   */
  formatDateForMDAC(dateStr) {
    if (!dateStr) return '';
    
    // 如果已经是DD/MM/YYYY格式，直接返回
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
      return dateStr;
    }
    
    // 如果是YYYY-MM-DD格式，转换为DD/MM/YYYY
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      const [year, month, day] = dateStr.split('-');
      return `${day}/${month}/${year}`;
    }
    
    // 尝试解析其他格式的日期
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        console.warn(`⚠️ 无法解析日期: ${dateStr}`);
        return dateStr; // 如果无法解析，返回原始值
      }
      
      // 格式化为DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      
      return `${day}/${month}/${year}`;
    } catch (error) {
      console.error(`❌ 日期格式化失败: ${dateStr}`, error);
      return dateStr; // 出错时返回原始值
    }
  }
  
  /**
   * 生成修复报告
   */
  generateFixReport() {
    console.log('📊 生成修复报告...');
    
    const totalFields = this.problemFields.length;
    const successCount = this.fixResults.success.length;
    const failureCount = this.fixResults.failure.length;
    const successRate = (successCount / totalFields * 100).toFixed(1);
    
    console.log('='.repeat(50));
    console.log('📋 MDAC字段修复报告');
    console.log('='.repeat(50));
    console.log(`📊 总字段数: ${totalFields}`);
    console.log(`✅ 修复成功: ${successCount}`);
    console.log(`❌ 修复失败: ${failureCount}`);
    console.log(`📈 成功率: ${successRate}%`);
    
    if (this.fixResults.failure.length > 0) {
      console.log('\n❌ 失败详情:');
      this.fixResults.failure.forEach(failure => {
        console.log(`   - ${failure.fieldId}: ${failure.reason}`);
      });
    }
    
    console.log('='.repeat(50));
  }
  
  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建修复工具实例
const fixer = new MDACFieldFixer();

// 导出到全局
window.MDACFieldFixer = fixer;

// 自动初始化
fixer.init().then(() => {
  console.log('🚀 MDAC字段修复工具已准备就绪');
  console.log('使用 window.MDACFieldFixer 访问修复工具');
  console.log('例如: MDACFieldFixer.fixAllFields()');
});
