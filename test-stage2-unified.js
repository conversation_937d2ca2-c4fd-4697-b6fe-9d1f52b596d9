/**
 * MDAC Extension Stage 2 统一架构测试脚本
 * 测试统一模块注册表和架构协调功能
 */

console.log('🧪 开始测试 MDAC Extension Stage 2 统一架构...');

class Stage2TestSuite {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0
        };
    }

    addTest(name, testFunction) {
        this.tests.push({ name, testFunction });
    }

    async runAllTests() {
        console.log('🚀 开始运行 Stage 2 测试套件...');
        
        for (const test of this.tests) {
            try {
                console.log(`\n📋 测试: ${test.name}`);
                await test.testFunction();
                console.log(`✅ ${test.name} - 通过`);
                this.results.passed++;
            } catch (error) {
                console.error(`❌ ${test.name} - 失败:`, error);
                this.results.failed++;
            }
            this.results.total++;
        }

        console.log('\n📊 测试结果汇总:');
        console.log(`总计: ${this.results.total}`);
        console.log(`通过: ${this.results.passed}`);
        console.log(`失败: ${this.results.failed}`);
        console.log(`成功率: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
    }
}

// 创建测试套件
const testSuite = new Stage2TestSuite();

// 测试1: 统一架构启动器检查
testSuite.addTest('统一架构启动器加载检查', async () => {
    if (typeof window.UnifiedArchitectureBootstrap === 'undefined') {
        throw new Error('UnifiedArchitectureBootstrap 类未定义');
    }
    
    if (typeof window.mdacUnifiedBootstrap === 'undefined') {
        throw new Error('mdacUnifiedBootstrap 实例未创建');
    }
    
    const status = window.mdacUnifiedBootstrap.getStatus();
    console.log('启动器状态:', status);
    
    if (!status.isInitialized) {
        // 等待初始化完成
        let attempts = 0;
        while (!window.mdacUnifiedBootstrap.getStatus().isInitialized && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        if (!window.mdacUnifiedBootstrap.getStatus().isInitialized) {
            throw new Error('统一架构启动器初始化超时');
        }
    }
    
    console.log('✅ 统一架构启动器已正常工作');
});

// 测试2: 统一模块注册表检查
testSuite.addTest('统一模块注册表功能检查', async () => {
    if (typeof window.UnifiedModuleRegistry === 'undefined') {
        throw new Error('UnifiedModuleRegistry 类未定义');
    }
    
    const bootstrap = window.mdacUnifiedBootstrap;
    if (!bootstrap.registry) {
        throw new Error('统一模块注册表实例未创建');
    }
    
    const registry = bootstrap.registry;
    const stats = registry.getStats();
    
    console.log('注册表统计:', stats);
    
    if (!stats.isInitialized) {
        throw new Error('统一模块注册表未初始化');
    }
    
    if (stats.moduleCount === 0) {
        throw new Error('没有注册任何模块');
    }
    
    console.log(`✅ 注册表已加载 ${stats.moduleCount} 个模块`);
});

// 测试3: 系统检测功能
testSuite.addTest('双系统检测功能', async () => {
    const bootstrap = window.mdacUnifiedBootstrap;
    const stats = bootstrap.getStatus();
    
    console.log('检测到的系统:', stats.registry?.loadedSystems);
    
    // 至少应该检测到一个系统
    if (!stats.registry?.loadedSystems || stats.registry.loadedSystems.length === 0) {
        throw new Error('未检测到任何现有系统');
    }
    
    // 检查传统系统
    if (typeof window.mdacSidePanelApp !== 'undefined') {
        console.log('✅ 传统系统已检测并可访问');
        
        if (!window.mdacSidePanelApp.isInitialized) {
            console.warn('⚠️ 传统系统尚未完全初始化');
        }
    }
    
    // 检查模块化系统
    if (typeof window.mdacModularSidePanel !== 'undefined') {
        console.log('✅ 模块化系统已检测并可访问');
    }
});

// 测试4: 事件总线功能
testSuite.addTest('统一事件总线功能', async () => {
    if (typeof window.mdacEventBus === 'undefined') {
        throw new Error('统一事件总线未创建');
    }
    
    const eventBus = window.mdacEventBus;
    
    // 测试事件发送和接收
    let eventReceived = false;
    const testData = { test: 'stage2-test', timestamp: Date.now() };
    
    // 添加监听器
    eventBus.on('stage2-test-event', (data) => {
        if (data.test === 'stage2-test') {
            eventReceived = true;
            console.log('✅ 测试事件已接收:', data);
        }
    });
    
    // 发送测试事件
    eventBus.emit('stage2-test-event', testData);
    
    // 等待事件处理
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (!eventReceived) {
        throw new Error('事件总线测试失败 - 事件未接收');
    }
    
    console.log('✅ 事件总线功能正常');
});

// 测试5: 状态管理器功能
testSuite.addTest('统一状态管理器功能', async () => {
    if (typeof window.mdacUnifiedStateManager === 'undefined') {
        throw new Error('统一状态管理器未创建');
    }
    
    const stateManager = window.mdacUnifiedStateManager;
    
    // 测试状态设置和获取
    const testKey = 'stage2-test-state';
    const testValue = { value: 'test', timestamp: Date.now() };
    
    stateManager.setState(testKey, testValue);
    
    const retrievedValue = stateManager.getState(testKey);
    
    if (!retrievedValue || retrievedValue.value !== testValue.value) {
        throw new Error('状态管理器设置/获取测试失败');
    }
    
    // 测试状态删除
    stateManager.removeState(testKey);
    
    if (stateManager.hasState(testKey)) {
        throw new Error('状态管理器删除测试失败');
    }
    
    console.log('✅ 状态管理器功能正常');
});

// 测试6: 热重启监控功能
testSuite.addTest('热重启监控功能', async () => {
    const bootstrap = window.mdacUnifiedBootstrap;
    const registry = bootstrap.registry;
    
    if (!registry) {
        throw new Error('注册表不可用，无法测试热重启');
    }
    
    // 检查热重启计数器初始化
    const stats = registry.getStats();
    
    if (typeof stats.hotReloadCount !== 'number') {
        throw new Error('热重启计数器未初始化');
    }
    
    console.log(`✅ 热重启计数器: ${stats.hotReloadCount}/${registry.maxHotReloads}`);
    
    // 测试是否能触发模拟错误恢复（不实际执行）
    if (typeof registry.handleCriticalError !== 'function') {
        throw new Error('关键错误处理函数不存在');
    }
    
    console.log('✅ 热重启监控功能已配置');
});

// 测试7: 消息队列功能
testSuite.addTest('统一消息队列功能', async () => {
    const bootstrap = window.mdacUnifiedBootstrap;
    const registry = bootstrap.registry;
    
    if (!registry) {
        throw new Error('注册表不可用，无法测试消息队列');
    }
    
    // 检查消息队列属性
    if (!Array.isArray(registry.messageQueue)) {
        throw new Error('消息队列未正确初始化');
    }
    
    // 测试队列大小统计
    const stats = registry.getStats();
    if (typeof stats.queuedMessages !== 'number') {
        throw new Error('队列消息计数不可用');
    }
    
    console.log(`✅ 消息队列: ${stats.queuedMessages} 条排队消息`);
});

// 测试8: 系统桥接功能
testSuite.addTest('系统桥接适配器功能', async () => {
    const bootstrap = window.mdacUnifiedBootstrap;
    const registry = bootstrap.registry;
    
    if (!registry) {
        throw new Error('注册表不可用，无法测试系统桥接');
    }
    
    const stats = registry.getStats();
    let bridgedSystems = 0;
    
    // 检查传统系统适配器
    const traditionalAdapter = registry.getModule('TraditionalSystemAdapter');
    if (traditionalAdapter) {
        console.log('✅ 传统系统适配器已创建');
        bridgedSystems++;
        
        // 检查适配器接口
        if (typeof traditionalAdapter.updateToMDAC === 'function') {
            console.log('✅ 传统系统接口已桥接');
        }
    }
    
    // 检查模块化系统适配器
    const modularAdapter = registry.getModule('ModularSystemAdapter');
    if (modularAdapter) {
        console.log('✅ 模块化系统适配器已创建');
        bridgedSystems++;
        
        // 检查适配器接口
        if (typeof modularAdapter.getModuleStatus === 'function') {
            console.log('✅ 模块化系统接口已桥接');
        }
    }
    
    if (bridgedSystems === 0) {
        throw new Error('没有创建任何系统桥接适配器');
    }
    
    console.log(`✅ 已桥接 ${bridgedSystems} 个系统`);
});

// 测试9: 错误处理和降级模式
testSuite.addTest('错误处理和降级模式', async () => {
    const bootstrap = window.mdacUnifiedBootstrap;
    const registry = bootstrap.registry;
    
    if (!registry || !registry.stateManager) {
        throw new Error('状态管理器不可用，无法测试降级模式');
    }
    
    // 检查当前是否在降级模式
    const isDegraded = registry.stateManager.getState('degradedMode', false);
    console.log(`降级模式状态: ${isDegraded}`);
    
    // 检查错误处理函数存在
    if (typeof registry.handleCriticalError !== 'function') {
        throw new Error('关键错误处理函数不存在');
    }
    
    if (typeof registry.enterDegradedMode !== 'function') {
        throw new Error('降级模式函数不存在');
    }
    
    console.log('✅ 错误处理和降级模式功能已配置');
});

// 测试10: 统一API接口
testSuite.addTest('统一API接口可用性', async () => {
    const bootstrap = window.mdacUnifiedBootstrap;
    
    // 检查启动器API
    const requiredBootstrapMethods = ['getStatus', 'reload'];
    for (const method of requiredBootstrapMethods) {
        if (typeof bootstrap[method] !== 'function') {
            throw new Error(`启动器缺少方法: ${method}`);
        }
    }
    
    // 检查注册表API
    if (bootstrap.registry) {
        const requiredRegistryMethods = ['getModule', 'getStats', 'emitEvent'];
        for (const method of requiredRegistryMethods) {
            if (typeof bootstrap.registry[method] !== 'function') {
                throw new Error(`注册表缺少方法: ${method}`);
            }
        }
    }
    
    // 检查全局API
    const globalAPIs = [
        'mdacEventBus',
        'mdacUnifiedStateManager',
        'mdacUnifiedBootstrap'
    ];
    
    for (const api of globalAPIs) {
        if (typeof window[api] === 'undefined') {
            throw new Error(`全局API不可用: ${api}`);
        }
    }
    
    console.log('✅ 所有统一API接口都可用');
});

// 延迟运行测试，确保系统完全加载
setTimeout(async () => {
    try {
        await testSuite.runAllTests();
        console.log('\n🎉 Stage 2 统一架构测试完成！');
        
        // 生成测试报告
        const report = {
            timestamp: new Date().toISOString(),
            stage: 'Stage 2 - 统一架构',
            results: testSuite.results,
            systemInfo: {
                bootstrap: window.mdacUnifiedBootstrap?.getStatus(),
                registry: window.mdacUnifiedBootstrap?.registry?.getStats(),
                eventBus: !!window.mdacEventBus,
                stateManager: !!window.mdacUnifiedStateManager
            }
        };
        
        console.log('📋 详细测试报告:', report);
        
        // 保存到全局以便检查
        window.mdacStage2TestReport = report;
        
    } catch (error) {
        console.error('🚨 测试套件执行失败:', error);
    }
}, 3000); // 3秒延迟，确保所有系统加载完成

console.log('🎯 Stage 2 测试脚本已加载，将在3秒后开始测试...');
