# MDAC AI智能填充工具用户使用手册

## 📖 用户手册概述

**版本**: 1.0  
**更新日期**: 2025-07-15  
**适用版本**: MDAC AI智能填充工具 v2.0.0  
**目标读者**: 最终用户、MDAC申请者

---

## 🎯 工具简介

MDAC AI智能填充工具是一个专门为马来西亚数字入境卡（Malaysia Digital Arrival Card）设计的Chrome浏览器扩展。它利用先进的AI技术，能够智能解析您的个人信息并自动填充到MDAC表单中，大幅提升填写效率和准确性。

### 🌟 主要优势
- **🤖 AI智能解析**: 自动识别和分类个人信息
- **⚡ 一键填充**: 智能检测表单字段，快速填充
- **🔒 隐私安全**: 所有数据仅在本地存储，不上传云端
- **🎯 专业准确**: 专为MDAC表单优化，确保格式正确
- **💾 数据保存**: 自动保存输入信息，避免重复录入

---

## 🚀 快速开始

### 第一步：安装扩展

#### 从Chrome Web Store安装（推荐）
1. 打开Chrome浏览器
2. 访问Chrome Web Store搜索"MDAC AI智能填充工具"
3. 点击"添加至Chrome"按钮
4. 在弹出确认框中点击"添加扩展程序"

#### 手动安装（开发版本）
1. 下载扩展文件包
2. 打开Chrome，访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择扩展文件夹

### 第二步：验证安装
安装成功后，您会看到：
- Chrome工具栏出现MDAC扩展图标 🔧
- 弹出"扩展已添加"的通知
- 扩展图标显示为彩色（激活状态）

### 第三步：首次配置
1. 点击扩展图标
2. 选择"打开MDAC AI智能助手侧边栏"
3. 阅读使用条款和隐私政策
4. 完成基本设置（语言、偏好等）

---

## 🎨 界面介绍

### 主界面布局

```
┌─────────────────────────────────┐
│  🟢 MDAC AI助手已就绪            │  ← 状态指示器
├─────────────────────────────────┤
│  📝 个人信息输入区               │
│  ┌─────────────────────────────┐ │
│  │ 请输入或粘贴您的个人信息...    │ │  ← 信息输入框
│  │                             │ │
│  └─────────────────────────────┘ │
│  [🤖 AI解析个人信息]             │  ← AI解析按钮
├─────────────────────────────────┤
│  ✈️ 旅行信息输入区               │
│  ┌─────────────────────────────┐ │
│  │ 请输入您的旅行相关信息...      │ │  ← 旅行信息框
│  │                             │ │
│  └─────────────────────────────┘ │
│  [🧳 AI解析旅行信息]             │  ← 旅行解析按钮
├─────────────────────────────────┤
│  [📤 更新到MDAC] [🗑️ 清除数据]   │  ← 操作按钮
│  [👁️ 数据预览] [🔧 设置]         │
└─────────────────────────────────┘
```

### 状态指示器说明
- 🟢 **绿色**: 系统正常，可以使用
- 🟡 **黄色**: 系统警告，功能可能受限
- 🔴 **红色**: 系统错误，需要处理
- 🔄 **蓝色**: 系统正在处理中

---

## 📝 详细使用教程

### 步骤一：准备个人信息

在使用工具前，请准备以下信息：

#### 必填个人信息
```
✅ 必需信息:
- 姓名（英文，与护照一致）
- 护照号码
- 出生日期
- 国籍
- 性别

📋 格式示例:
姓名：ZHANG SAN
护照号：A12345678
出生日期：1990/01/01
国籍：中国 或 CHINA
性别：男 或 MALE
```

#### 旅行信息
```
✈️ 旅行详情:
- 入境目的（旅游/商务/探亲等）
- 在马来西亚地址
- 停留天数
- 航班信息（可选）

📋 格式示例:
目的：旅游
地址：Kuala Lumpur City Centre
停留：7天
航班：MH123
```

### 步骤二：访问MDAC网站

1. **打开MDAC官方网站**
   ```
   网址：https://imigresen-online.imi.gov.my/mdac/main?registerMain
   ```

2. **确认扩展激活**
   - 扩展图标变为彩色
   - 页面顶部可能显示"MDAC助手已就绪"

3. **打开工具侧边栏**
   - 点击Chrome工具栏中的MDAC扩展图标
   - 选择"打开MDAC AI智能助手侧边栏"
   - 或使用快捷键：`Ctrl+Shift+S` (Windows) / `Cmd+Shift+S` (Mac)

### 步骤三：输入和解析信息

#### 个人信息解析
1. **输入个人信息**
   ```
   在"个人信息输入区"输入或粘贴：
   
   姓名：ZHANG SAN
   护照号：A12345678
   出生日期：1990年1月1日
   国籍：中国
   性别：男
   ```

2. **AI智能解析**
   - 点击"🤖 AI解析个人信息"按钮
   - 等待3-5秒，AI处理您的信息
   - 解析完成后会显示"✅ 个人信息AI解析完成"

3. **验证解析结果**
   - 点击"👁️ 数据预览"查看解析结果
   - 确认信息准确性
   - 如有错误可手动修正

#### 旅行信息解析
1. **输入旅行信息**
   ```
   在"旅行信息输入区"输入：
   
   入境目的：旅游
   停留地址：吉隆坡双子塔附近酒店
   停留天数：7天
   计划活动：观光购物
   ```

2. **AI解析处理**
   - 点击"🧳 AI解析旅行信息"按钮
   - AI会智能识别地址、停留时间等信息
   - 解析完成后显示确认消息

### 步骤四：填充MDAC表单

1. **确认表单页面**
   - 确保当前在MDAC官方表单页面
   - 表单应该包含姓名、护照等输入字段

2. **一键填充**
   - 点击"📤 更新到MDAC"按钮
   - 工具会自动检测表单字段
   - 智能匹配并填充相应信息

3. **填充结果确认**
   ```
   成功填充会显示：
   "✅ 数据已发送到X个MDAC页面"
   
   如果失败会显示：
   "❌ 请先打开MDAC网站"
   ```

4. **手动验证**
   - 检查所有填充的字段
   - 确认信息准确无误
   - 必要时进行手动调整

---

## 🔧 高级功能

### 1. 数据管理

#### 数据预览功能
- **功能**: 查看当前保存的所有信息
- **使用**: 点击"👁️ 数据预览"按钮
- **显示内容**: 
  - 个人信息汇总
  - 旅行信息详情
  - 数据完整度百分比
  - 最后更新时间

#### 数据保存和恢复
```
💾 自动保存:
- 系统自动保存您输入的信息
- 关闭浏览器后数据不会丢失
- 下次使用时自动恢复数据

🔄 手动管理:
- 清除数据：删除所有保存的信息
- 导出数据：将信息保存为文件
- 导入数据：从文件恢复信息
```

### 2. 智能填充设置

#### 填充偏好设置
1. 点击"🔧 设置"按钮
2. 在设置面板中配置：
   ```
   ⚙️ 可配置选项:
   - 自动填充速度（快/中/慢）
   - 填充确认提示（开启/关闭）
   - 数据保存时长（1周/1月/永久）
   - 错误处理方式（提示/忽略）
   ```

#### 字段映射自定义
- **功能**: 自定义字段匹配规则
- **适用**: 当自动检测不准确时
- **设置**: 设置 → 高级 → 字段映射

### 3. 多语言支持

#### 语言切换
1. 点击设置按钮
2. 选择"语言/Language"
3. 可选语言：
   - 中文（简体）
   - 中文（繁体）
   - English
   - Bahasa Malaysia

#### AI解析语言
- **自动检测**: AI会自动识别输入语言
- **手动指定**: 可在设置中指定输入语言
- **混合语言**: 支持中英文混合输入

---

## 🛡️ 隐私和安全

### 数据安全保障

#### 本地存储原则
```
🔒 隐私保护措施:
✅ 所有数据仅在您的设备本地存储
✅ 不会上传到任何第三方服务器
✅ 使用Chrome官方安全存储API
✅ 符合最新隐私保护标准
❌ 不收集个人隐私信息
❌ 不与其他应用共享数据
```

#### AI处理安全
```
🤖 AI调用说明:
- AI解析功能需要网络连接
- 仅发送必要的文本信息进行解析
- 不包含完整的个人敏感信息
- 解析后立即删除服务器端数据
- 使用加密传输协议
```

### 权限说明

扩展申请的权限及用途：
```
📋 权限清单:
🔹 sidePanel - 显示侧边栏界面
🔹 activeTab - 访问当前标签页（仅MDAC网站）
🔹 storage - 本地存储用户数据
🔹 scripting - 注入表单填充脚本
🔹 tabs - 管理标签页状态

⚠️ 注意：扩展只能在MDAC官方网站工作
```

---

## 🔍 故障排除

### 常见问题解决

#### 问题1：扩展图标灰色/无响应
**症状**: 扩展图标显示为灰色，点击无反应
**解决方案**:
1. 刷新当前页面（F5）
2. 检查是否在MDAC官方网站
3. 重新启用扩展：
   - 访问 `chrome://extensions/`
   - 找到MDAC扩展
   - 先关闭再开启

#### 问题2：侧边栏无法打开
**症状**: 点击扩展图标后侧边栏不显示
**解决方案**:
1. 确认Chrome版本 ≥ 114
2. 重新加载扩展
3. 关闭并重新打开Chrome浏览器
4. 检查是否有其他扩展冲突

#### 问题3：AI解析失败
**症状**: 点击AI解析按钮后显示错误
**可能原因**:
```
🔍 常见原因:
- 网络连接问题
- 输入信息格式不规范
- AI服务暂时不可用
- 输入内容为空
```

**解决方案**:
1. 检查网络连接
2. 规范输入格式：
   ```
   ✅ 正确格式:
   姓名：ZHANG SAN
   护照号：A12345678
   出生日期：1990/01/01
   
   ❌ 错误格式:
   我叫张三，护照号码是...
   ```
3. 稍后重试
4. 查看详细错误信息（F12开发者工具）

#### 问题4：表单填充不成功
**症状**: 点击"更新到MDAC"后表单字段未填充
**解决方案**:
1. 确认在MDAC官方表单页面
2. 确保已完成AI解析
3. 检查表单字段是否可编辑
4. 尝试手动点击表单字段后再填充

#### 问题5：数据丢失
**症状**: 之前输入的信息不见了
**解决方案**:
1. 检查"数据预览"是否显示历史数据
2. 确认未点击"清除数据"按钮
3. 检查Chrome是否清理了扩展数据
4. 重新输入信息（系统会自动保存）

### 性能优化建议

#### 最佳使用实践
```
⚡ 性能优化提示:
✅ 定期清理旧数据（设置→数据管理）
✅ 关闭不使用的Chrome标签页
✅ 确保Chrome版本是最新的
✅ 避免同时运行过多扩展
✅ 定期重启Chrome浏览器
```

#### 内存使用优化
- 使用完毕后关闭侧边栏
- 避免长时间保持多个MDAC标签页打开
- 定期查看扩展的内存使用情况

---

## 📊 使用技巧和最佳实践

### 效率提升技巧

#### 快捷操作
```
⌨️ 快捷键:
Ctrl+Shift+S (Windows) / Cmd+Shift+S (Mac) - 打开/关闭侧边栏
Ctrl+Shift+P (Windows) / Cmd+Shift+P (Mac) - 快速数据预览
Ctrl+Shift+C (Windows) / Cmd+Shift+C (Mac) - 清除当前输入
```

#### 批量信息处理
1. **模板信息准备**
   ```
   💡 建议创建标准模板:
   姓名：[您的英文姓名]
   护照号：[您的护照号]
   出生日期：[YYYY/MM/DD格式]
   国籍：[中国/CHINA]
   性别：[男/女 或 MALE/FEMALE]
   ```

2. **信息复用**
   - 个人基本信息一次输入，多次使用
   - 不同旅行的信息分别保存
   - 使用数据导出/导入功能备份

#### 准确性提升
```
📝 信息准确性建议:
✅ 姓名必须与护照完全一致
✅ 日期使用标准格式 YYYY/MM/DD
✅ 国籍使用标准英文名称
✅ 地址信息尽量详细准确
✅ 联系方式保持最新状态
```

### 高级用法

#### 自定义AI提示
1. 访问设置 → 高级选项
2. 找到"AI解析设置"
3. 可以自定义AI解析的提示词
4. 适用于特殊格式的信息输入

#### 多账户管理
```
👥 多用户使用:
- 家庭成员可以分别保存各自信息
- 使用"数据预览"切换不同用户数据
- 每次使用前确认当前用户信息
- 填充前请务必核对个人信息
```

---

## 📱 移动设备支持

### Android Chrome支持
```
📱 移动端使用说明:
✅ 支持Android Chrome浏览器
✅ 界面自动适配手机屏幕
✅ 支持触摸操作
⚠️ 部分高级功能可能受限
❌ 不支持iOS Safari浏览器
```

### 移动端操作差异
- 侧边栏会以全屏形式显示
- 长按代替右键菜单
- 虚拟键盘可能影响界面显示
- 建议横屏操作以获得更好体验

---

## 🆘 获取帮助

### 自助服务

#### 内置帮助系统
1. 点击设置按钮
2. 选择"帮助与支持"
3. 查看：
   - 常见问题解答
   - 视频教程
   - 使用技巧
   - 更新日志

#### 诊断工具
```
🔧 自诊断功能:
- 设置 → 诊断工具
- 自动检测常见问题
- 提供修复建议
- 生成诊断报告
```

### 联系技术支持

#### 报告问题
如遇问题，请提供以下信息：
```
📋 问题报告模板:
1. Chrome版本：[帮助→关于Google Chrome]
2. 扩展版本：[扩展管理页面查看]
3. 操作系统：[Windows/Mac/Linux]
4. 问题描述：[详细说明]
5. 复现步骤：[具体操作流程]
6. 错误截图：[如有]
```

#### 支持渠道
- 📧 邮箱支持：[<EMAIL>]
- 💬 在线客服：[工作时间 9:00-18:00]
- 📖 帮助中心：[help.mdac-extension.com]
- 🐛 问题反馈：[GitHub Issues]

### 社区支持
- 用户交流群：[QQ群/微信群]
- 使用心得分享
- 功能建议收集
- 经验技巧交流

---

## 🔄 更新和版本管理

### 自动更新
```
🔄 更新机制:
✅ Chrome会自动检查扩展更新
✅ 更新后自动重启扩展
✅ 用户数据会自动迁移
✅ 新功能会有使用指导
```

### 版本历史
#### v2.0.0 (当前版本)
- ✨ 全新AI解析引擎
- 🎨 优化用户界面设计
- 🔒 增强隐私安全保护
- 🌐 支持多语言界面
- 🚀 提升填充准确率

#### v1.x.x (历史版本)
- 基础表单填充功能
- 简单数据保存
- 基本错误处理

### 功能预告
即将推出的新功能：
```
🚀 开发中功能:
📸 OCR图片识别 - 支持护照照片识别
📋 模板管理 - 多套信息模板保存
🔄 数据同步 - 跨设备数据同步
🎯 智能纠错 - 自动发现并修正错误
📊 使用统计 - 个人使用数据分析
```

---

## 📚 附录

### A. 支持的表单字段

MDAC官方表单字段对照：
```
字段映射表:
🏷️ 姓名字段: "Full Name", "Name", "fullname"
🆔 护照字段: "Passport No", "Passport Number", "passport"
📅 出生日期: "Date of Birth", "DOB", "birthdate"
🌍 国籍字段: "Nationality", "Country", "nationality"
⚥ 性别字段: "Gender", "Sex", "gender"
📍 地址字段: "Address", "Location", "address"
📧 邮箱字段: "Email", "Email Address", "email"
📱 电话字段: "Phone", "Mobile", "telephone"
```

### B. 国家和地区代码

常用国家英文名称：
```
🌏 亚洲国家:
中国 - China
日本 - Japan  
韩国 - South Korea
新加坡 - Singapore
泰国 - Thailand
印度 - India
印度尼西亚 - Indonesia

🌍 其他地区:
美国 - United States
英国 - United Kingdom
澳大利亚 - Australia
加拿大 - Canada
德国 - Germany
法国 - France
```

### C. 日期格式说明

支持的日期输入格式：
```
📅 标准格式:
YYYY/MM/DD - 1990/01/01 (推荐)
YYYY-MM-DD - 1990-01-01
DD/MM/YYYY - 01/01/1990
MM/DD/YYYY - 01/01/1990

📅 中文格式:
YYYY年MM月DD日 - 1990年1月1日
YYYY年M月D日 - 1990年1月1日

⚠️ 注意: 系统会自动转换为MDAC要求的格式
```

### D. 故障代码对照表

常见错误代码及含义：
```
🔢 错误代码:
E001 - 网络连接失败
E002 - AI解析服务不可用
E003 - 表单字段检测失败
E004 - 数据格式验证失败
E005 - 存储访问权限不足
E006 - 扩展版本不兼容
```

---

## 📞 联系我们

### 开发团队信息
- **项目名称**: MDAC AI智能填充工具
- **开发团队**: MDAC Extension Team
- **版本**: v2.0.0
- **发布日期**: 2025年7月

### 法律信息
- **隐私政策**: [链接地址]
- **服务条款**: [链接地址]  
- **开源许可**: MIT License
- **第三方组件**: Google Gemini AI

### 致谢
感谢所有用户的反馈和建议，您的支持是我们不断改进的动力！

---

**用户手册版本**: 1.0  
**最后更新**: 2025-07-15  
**下次更新**: 根据用户反馈和功能更新进行修订

---

*本手册持续更新中，如有疑问或建议，欢迎随时联系我们！*