/**
 * 自动解析管理器模块
 * 负责处理自动解析功能的所有逻辑
 * 
 * <AUTHOR> AI Team
 * @version 3.1.0
 */

class AutoParseManager {
    constructor(memoryManager, errorHandler, messageSystem) {
        this.memoryManager = memoryManager;
        this.errorHandler = errorHandler;
        this.messageSystem = messageSystem;
        
        // 自动解析配置
        this.settings = {
            personal: {
                enabled: true,
                delay: 1000, // 1秒延迟
                minLength: 10 // 最少10个字符
            },
            travel: {
                enabled: true,
                delay: 1000,
                minLength: 10
            }
        };
        
        // 定时器管理
        this.timeouts = {
            personal: null,
            travel: null
        };
        
        this.countdowns = {
            personal: null,
            travel: null
        };
        
        // 防抖处理器
        this.debouncedHandlers = {};
        
        this.initializeDebouncedHandlers();
    }

    /**
     * 初始化防抖处理器
     */
    initializeDebouncedHandlers() {
        const { debounce } = window.PerformanceUtils;

        this.debouncedHandlers.personal = debounce(
            (inputElement) => this.handleAutoParseCore('personal', inputElement),
            100
        );

        this.debouncedHandlers.travel = debounce(
            (inputElement) => this.handleAutoParseCore('travel', inputElement),
            100
        );

        console.log('✅ [AutoParseManager] 防抖处理器初始化完成');
    }

    /**
     * 设置自动解析监听器
     * @param {Object} elements - UI元素对象
     */
    setupListeners(elements) {
        console.log('🤖 [AutoParseManager] 设置自动解析监听器...');

        try {
            // 个人信息输入框自动解析
            if (elements.personalInfoInput) {
                const personalInputHandler = (e) => {
                    this.handleAutoParseInput('personal', e.target);
                };
                this.memoryManager.addListener(elements.personalInfoInput, 'input', personalInputHandler);
            }

            // 旅行信息输入框自动解析
            if (elements.travelInfoInput) {
                const travelInputHandler = (e) => {
                    this.handleAutoParseInput('travel', e.target);
                };
                this.memoryManager.addListener(elements.travelInfoInput, 'input', travelInputHandler);
            }

            // 自动解析开关监听
            if (elements.autoParsePersonalEnabled) {
                const personalToggleHandler = (e) => {
                    this.settings.personal.enabled = e.target.checked;
                    if (!e.target.checked) {
                        this.cancelAutoParse('personal');
                    }
                };
                this.memoryManager.addListener(elements.autoParsePersonalEnabled, 'change', personalToggleHandler);
            }

            if (elements.autoParseTravel_Enabled) {
                const travelToggleHandler = (e) => {
                    this.settings.travel.enabled = e.target.checked;
                    if (!e.target.checked) {
                        this.cancelAutoParse('travel');
                    }
                };
                this.memoryManager.addListener(elements.autoParseTravel_Enabled, 'change', travelToggleHandler);
            }

            // 取消自动解析按钮
            if (elements.cancelAutoParsePersonal) {
                const cancelPersonalHandler = () => {
                    this.cancelAutoParse('personal');
                };
                this.memoryManager.addListener(elements.cancelAutoParsePersonal, 'click', cancelPersonalHandler);
            }

            if (elements.cancelAutoParseTravel) {
                const cancelTravelHandler = () => {
                    this.cancelAutoParse('travel');
                };
                this.memoryManager.addListener(elements.cancelAutoParseTravel, 'click', cancelTravelHandler);
            }

            console.log('✅ [AutoParseManager] 自动解析监听器设置完成');
        } catch (error) {
            this.errorHandler.handle(error, 'AutoParseListeners', '自动解析监听器设置失败');
        }
    }

    /**
     * 处理自动解析输入事件
     * @param {string} type - 解析类型
     * @param {HTMLElement} inputElement - 输入框元素
     */
    handleAutoParseInput(type, inputElement) {
        try {
            if (this.debouncedHandlers[type]) {
                this.debouncedHandlers[type](inputElement);
            }
        } catch (error) {
            this.errorHandler.handle(error, 'AutoParseInput', '自动解析处理失败');
        }
    }

    /**
     * 自动解析核心处理逻辑
     * @param {string} type - 解析类型
     * @param {HTMLElement} inputElement - 输入框元素
     */
    handleAutoParseCore(type, inputElement) {
        try {
            const settings = this.settings[type];

            if (!settings || !settings.enabled) {
                return;
            }

            // 清除之前的定时器
            this.cancelAutoParse(type);

            const inputText = inputElement.value.trim();

            // 检查输入长度
            if (inputText.length < settings.minLength) {
                this.hideAutoParseStatus(type);
                return;
            }

            // 开始倒计时
            this.startAutoParseCountdown(type, settings.delay);
        } catch (error) {
            this.errorHandler.handle(error, 'AutoParseCore', '自动解析核心处理失败');
        }
    }

    /**
     * 开始自动解析倒计时
     * @param {string} type - 解析类型
     * @param {number} delay - 延迟时间
     */
    startAutoParseCountdown(type, delay = 1000) {
        try {
            console.log(`⏰ [AutoParseManager] 开始${type}自动解析倒计时: ${delay}ms`);

            let countdown = Math.ceil(delay / 1000);
            this.showAutoParseStatus(type, countdown);

            // 倒计时显示
            const countdownInterval = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    this.showAutoParseStatus(type, countdown);
                } else {
                    this.memoryManager.removeInterval(countdownInterval);
                    this.countdowns[type] = null;
                }
            }, 1000);

            this.countdowns[type] = countdownInterval;
            this.memoryManager.addInterval(countdownInterval);

            // 实际解析定时器
            const parseTimeout = setTimeout(() => {
                try {
                    this.hideAutoParseStatus(type);
                    
                    // 触发解析事件
                    this.triggerParseEvent(type);
                    
                    // 清理定时器
                    this.memoryManager.removeTimer(parseTimeout);
                    this.timeouts[type] = null;
                } catch (error) {
                    this.errorHandler.handle(error, 'AutoParseExecution', '自动解析执行失败');
                }
            }, delay);

            this.timeouts[type] = parseTimeout;
            this.memoryManager.addTimer(parseTimeout);

        } catch (error) {
            this.errorHandler.handle(error, 'AutoParseCountdown', '自动解析倒计时启动失败');
        }
    }

    /**
     * 取消自动解析
     * @param {string} type - 解析类型
     */
    cancelAutoParse(type) {
        try {
            // 取消解析定时器
            if (this.timeouts[type]) {
                this.memoryManager.removeTimer(this.timeouts[type]);
                this.timeouts[type] = null;
            }

            // 取消倒计时定时器
            if (this.countdowns[type]) {
                this.memoryManager.removeInterval(this.countdowns[type]);
                this.countdowns[type] = null;
            }

            // 隐藏状态显示
            this.hideAutoParseStatus(type);
            
            console.log(`❌ [AutoParseManager] 已取消${type}自动解析`);
        } catch (error) {
            console.warn(`⚠️ [AutoParseManager] 取消${type}自动解析时出错:`, error);
        }
    }

    /**
     * 取消所有自动解析
     */
    cancelAllAutoParse() {
        Object.keys(this.timeouts).forEach(type => {
            this.cancelAutoParse(type);
        });
    }

    /**
     * 显示自动解析状态
     * @param {string} type - 解析类型
     * @param {number} countdown - 倒计时秒数
     */
    showAutoParseStatus(type, countdown) {
        const statusEl = document.getElementById(`autoParse${type.charAt(0).toUpperCase() + type.slice(1)}Status`);
        const countdownEl = document.getElementById(`${type}CountdownText`);
        
        if (statusEl) {
            statusEl.classList.remove('hidden');
        }
        
        if (countdownEl) {
            countdownEl.textContent = `${countdown}秒后自动解析`;
        }
    }

    /**
     * 隐藏自动解析状态
     * @param {string} type - 解析类型
     */
    hideAutoParseStatus(type) {
        const statusEl = document.getElementById(`autoParse${type.charAt(0).toUpperCase() + type.slice(1)}Status`);
        
        if (statusEl) {
            statusEl.classList.add('hidden');
        }
    }

    /**
     * 触发解析事件
     * @param {string} type - 解析类型
     */
    triggerParseEvent(type) {
        const event = new CustomEvent('autoParseTriggered', {
            detail: { type: type }
        });
        document.dispatchEvent(event);
    }

    /**
     * 获取设置
     * @param {string} type - 解析类型
     * @returns {Object} 设置对象
     */
    getSettings(type) {
        return this.settings[type];
    }

    /**
     * 更新设置
     * @param {string} type - 解析类型
     * @param {Object} newSettings - 新设置
     */
    updateSettings(type, newSettings) {
        if (this.settings[type]) {
            this.settings[type] = { ...this.settings[type], ...newSettings };
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.cancelAllAutoParse();
        this.debouncedHandlers = {};
        console.log('✅ [AutoParseManager] 资源清理完成');
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AutoParseManager;
} else {
    window.AutoParseManager = AutoParseManager;
}
