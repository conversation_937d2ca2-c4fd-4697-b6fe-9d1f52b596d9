/**
 * MDAC AI Chrome扩展字段映射修复测试脚本
 * 测试修复后的字段ID映射和填充功能
 */

// 测试配置
const TEST_CONFIG = {
    // 修正后的字段ID映射
    CORRECTED_FIELD_MAPPINGS: {
        // 个人信息字段
        'fullName': 'fullName',
        'passportNumber': 'passportNumber',
        'dob': 'dob',
        'nationality': 'nationality',
        'sex': 'sex',
        'passExpDte': 'passExpDte', // 修正：护照到期日期
        
        // 联系信息字段
        'email': 'email',
        'countryCode': 'countryCode',
        'mobileNumber': 'mobileNumber',
        
        // 旅行信息字段
        'arrDt': 'arrDt',           // 修正：到达日期
        'depDt': 'depDt',           // 修正：离开日期
        'embark': 'embark',         // Last Port of Embarkation
        'vesselNm': 'vesselNm',
        'trvlMode': 'trvlMode',
        
        // 住宿信息字段
        'accommodationStay': 'accommodationStay',
        'accommodationAddress1': 'accommodationAddress1',
        'accommodationAddress2': 'accommodationAddress2',
        'accommodationState': 'accommodationState',
        'accommodationPostcode': 'accommodationPostcode',
        'accommodationCity': 'accommodationCity'
    },
    
    // 测试数据
    TEST_DATA: {
        'fullName': 'ZHANG SAN',
        'passportNumber': 'A12345678',
        'dob': '01/01/1990',
        'nationality': 'CHN',
        'sex': '1',
        'passExpDte': '31/12/2030',
        'email': '<EMAIL>',
        'countryCode': '+60',
        'mobileNumber': '*********',
        'arrDt': '01/08/2025',
        'depDt': '15/08/2025',
        'embark': 'KLIA',
        'vesselNm': 'MH123',
        'trvlMode': 'AIR',
        'accommodationStay': '01',
        'accommodationAddress1': 'KUALA LUMPUR CITY CENTER HOTEL',
        'accommodationAddress2': 'JALAN BUKIT BINTANG',
        'accommodationState': '14',
        'accommodationPostcode': '50000',
        'accommodationCity': '1400'
    }
};

/**
 * 主测试函数
 */
async function runFieldMappingTests() {
    console.log('🧪 开始MDAC字段映射修复测试...');
    console.log('='.repeat(60));
    
    const results = {
        totalFields: 0,
        detectedFields: 0,
        successfullyFilled: 0,
        failed: [],
        dateFields: [],
        selectFields: []
    };
    
    // 1. 测试字段检测
    console.log('📋 步骤1: 测试字段检测');
    await testFieldDetection(results);
    
    // 2. 测试日期字段填充
    console.log('\n📅 步骤2: 测试日期字段填充');
    await testDateFieldFilling(results);
    
    // 3. 测试下拉框字段填充
    console.log('\n🔽 步骤3: 测试下拉框字段填充');
    await testSelectFieldFilling(results);
    
    // 4. 测试文本字段填充
    console.log('\n📝 步骤4: 测试文本字段填充');
    await testTextFieldFilling(results);
    
    // 5. 生成测试报告
    console.log('\n📊 步骤5: 生成测试报告');
    generateTestReport(results);
    
    return results;
}

/**
 * 测试字段检测
 */
async function testFieldDetection(results) {
    console.log('🔍 检测MDAC表单字段...');
    
    results.totalFields = Object.keys(TEST_CONFIG.CORRECTED_FIELD_MAPPINGS).length;
    
    Object.entries(TEST_CONFIG.CORRECTED_FIELD_MAPPINGS).forEach(([logicalName, fieldId]) => {
        const element = document.getElementById(fieldId);
        if (element) {
            results.detectedFields++;
            console.log(`✅ ${logicalName} (${fieldId}): 检测成功 - ${element.tagName}${element.type ? `[${element.type}]` : ''}`);
            
            // 分类字段类型
            if (element.tagName === 'SELECT') {
                results.selectFields.push({ logicalName, fieldId, element });
            } else if (isDateField(logicalName)) {
                results.dateFields.push({ logicalName, fieldId, element });
            }
        } else {
            results.failed.push({ logicalName, fieldId, reason: '字段不存在' });
            console.log(`❌ ${logicalName} (${fieldId}): 字段不存在`);
        }
    });
    
    console.log(`📊 字段检测结果: ${results.detectedFields}/${results.totalFields} 个字段检测成功`);
}

/**
 * 测试日期字段填充
 */
async function testDateFieldFilling(results) {
    console.log('📅 测试日期字段填充...');
    
    for (const { logicalName, fieldId, element } of results.dateFields) {
        const testValue = TEST_CONFIG.TEST_DATA[fieldId];
        if (!testValue) {
            console.log(`⚠️ ${logicalName}: 没有测试数据`);
            continue;
        }
        
        try {
            console.log(`🔧 填充 ${logicalName} (${fieldId}): "${testValue}"`);
            
            // 确保日期格式为DD/MM/YYYY
            const formattedDate = formatDateForMDAC(testValue);
            
            // 清空并填充
            element.value = '';
            element.value = formattedDate;
            
            // 触发事件
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('blur', { bubbles: true }));
            
            // 添加成功样式
            element.style.backgroundColor = '#e8f5e8';
            element.style.border = '2px solid #4CAF50';
            
            // 验证填充结果
            if (element.value === formattedDate) {
                results.successfullyFilled++;
                console.log(`✅ ${logicalName}: 填充成功 - "${element.value}"`);
            } else {
                results.failed.push({ logicalName, fieldId, reason: '填充后值不匹配' });
                console.log(`❌ ${logicalName}: 填充失败 - 期望"${formattedDate}"，实际"${element.value}"`);
            }
            
        } catch (error) {
            results.failed.push({ logicalName, fieldId, reason: error.message });
            console.error(`❌ ${logicalName}: 填充异常 - ${error.message}`);
        }
    }
}

/**
 * 测试下拉框字段填充
 */
async function testSelectFieldFilling(results) {
    console.log('🔽 测试下拉框字段填充...');
    
    for (const { logicalName, fieldId, element } of results.selectFields) {
        const testValue = TEST_CONFIG.TEST_DATA[fieldId];
        if (!testValue) {
            console.log(`⚠️ ${logicalName}: 没有测试数据`);
            continue;
        }
        
        try {
            console.log(`🔧 填充 ${logicalName} (${fieldId}): "${testValue}"`);
            console.log(`   选项数量: ${element.options.length}`);
            
            let success = false;
            
            // 特殊处理embark字段
            if (fieldId === 'embark') {
                success = await fillEmbarkField(element, testValue);
            } else {
                // 尝试直接匹配值
                success = setSelectByValue(element, testValue) || setSelectByText(element, testValue);
            }
            
            if (success) {
                results.successfullyFilled++;
                console.log(`✅ ${logicalName}: 填充成功 - "${element.value}"`);
            } else {
                results.failed.push({ logicalName, fieldId, reason: '未找到匹配选项' });
                console.log(`❌ ${logicalName}: 未找到匹配选项`);
            }
            
        } catch (error) {
            results.failed.push({ logicalName, fieldId, reason: error.message });
            console.error(`❌ ${logicalName}: 填充异常 - ${error.message}`);
        }
    }
}

/**
 * 测试文本字段填充
 */
async function testTextFieldFilling(results) {
    console.log('📝 测试文本字段填充...');
    
    Object.entries(TEST_CONFIG.CORRECTED_FIELD_MAPPINGS).forEach(([logicalName, fieldId]) => {
        const element = document.getElementById(fieldId);
        const testValue = TEST_CONFIG.TEST_DATA[fieldId];
        
        // 跳过已测试的日期和下拉框字段
        if (!element || !testValue || 
            results.dateFields.some(f => f.fieldId === fieldId) ||
            results.selectFields.some(f => f.fieldId === fieldId)) {
            return;
        }
        
        try {
            console.log(`🔧 填充 ${logicalName} (${fieldId}): "${testValue}"`);
            
            // 清空并填充
            element.value = '';
            element.value = testValue;
            
            // 触发事件
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('blur', { bubbles: true }));
            
            // 添加成功样式
            element.style.backgroundColor = '#e8f5e8';
            element.style.border = '2px solid #4CAF50';
            
            // 验证填充结果
            if (element.value === testValue) {
                results.successfullyFilled++;
                console.log(`✅ ${logicalName}: 填充成功`);
            } else {
                results.failed.push({ logicalName, fieldId, reason: '填充后值不匹配' });
                console.log(`❌ ${logicalName}: 填充失败`);
            }
            
        } catch (error) {
            results.failed.push({ logicalName, fieldId, reason: error.message });
            console.error(`❌ ${logicalName}: 填充异常 - ${error.message}`);
        }
    });
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
    console.log('📊 MDAC字段映射修复测试报告');
    console.log('='.repeat(60));
    console.log(`📋 总字段数: ${results.totalFields}`);
    console.log(`✅ 检测成功: ${results.detectedFields}`);
    console.log(`🎯 填充成功: ${results.successfullyFilled}`);
    console.log(`❌ 失败数量: ${results.failed.length}`);
    console.log(`📅 日期字段: ${results.dateFields.length}`);
    console.log(`🔽 下拉框字段: ${results.selectFields.length}`);
    
    const successRate = ((results.successfullyFilled / results.detectedFields) * 100).toFixed(1);
    console.log(`📈 填充成功率: ${successRate}%`);
    
    if (results.failed.length > 0) {
        console.log('\n❌ 失败详情:');
        results.failed.forEach(({ logicalName, fieldId, reason }) => {
            console.log(`   - ${logicalName} (${fieldId}): ${reason}`);
        });
    }
    
    console.log('\n🎉 测试完成！');
}

// 辅助函数
function isDateField(logicalName) {
    return ['dob', 'passExpDte', 'arrDt', 'depDt'].includes(logicalName);
}

function formatDateForMDAC(dateStr) {
    if (!dateStr) return '';
    
    // 如果已经是DD/MM/YYYY格式，直接返回
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
        return dateStr;
    }
    
    // 如果是YYYY-MM-DD格式，转换为DD/MM/YYYY
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        const [year, month, day] = dateStr.split('-');
        return `${day}/${month}/${year}`;
    }
    
    return dateStr;
}

async function fillEmbarkField(element, value) {
    const airportKeywords = ['KLIA', 'Kuala Lumpur', 'KUL', 'Malaysia', 'Airport'];
    
    // 查找匹配选项
    for (let i = 0; i < element.options.length; i++) {
        const option = element.options[i];
        const optionText = option.text.toLowerCase();
        
        if (optionText.includes(value.toLowerCase()) || 
            airportKeywords.some(keyword => optionText.includes(keyword.toLowerCase()))) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.style.backgroundColor = '#e8f5e8';
            element.style.border = '2px solid #4CAF50';
            return true;
        }
    }
    
    return false;
}

function setSelectByValue(element, value) {
    element.value = value;
    if (element.value === value) {
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.style.backgroundColor = '#e8f5e8';
        element.style.border = '2px solid #4CAF50';
        return true;
    }
    return false;
}

function setSelectByText(element, text) {
    for (let i = 0; i < element.options.length; i++) {
        const option = element.options[i];
        if (option.text.toLowerCase().includes(text.toLowerCase())) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.style.backgroundColor = '#e8f5e8';
            element.style.border = '2px solid #4CAF50';
            return true;
        }
    }
    return false;
}

// 导出测试函数
if (typeof window !== 'undefined') {
    window.runFieldMappingTests = runFieldMappingTests;
    console.log('🧪 字段映射测试脚本已加载，运行 runFieldMappingTests() 开始测试');
}
