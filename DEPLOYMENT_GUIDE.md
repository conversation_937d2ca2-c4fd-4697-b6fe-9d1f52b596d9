# MDAC Chrome扩展部署和维护手册

## 📖 文档概述

**版本**: 1.0  
**更新日期**: 2025-07-15  
**适用版本**: MDAC AI智能填充工具 v2.0.0  
**目标读者**: 系统管理员、DevOps工程师、项目维护者

---

## 🎯 文档目的

本手册提供MDAC Chrome扩展的完整部署、发布、更新和维护指导，确保扩展能够安全、稳定地运行在生产环境中。

### 涵盖内容
- 🚀 **生产环境部署** - 完整的打包和发布流程
- 🔄 **版本管理** - 版本控制和更新策略
- 🛡️ **安全配置** - 生产环境安全最佳实践
- 📊 **监控和维护** - 运行状态监控和问题排查
- 🔧 **故障恢复** - 应急处理和回滚策略

---

## 🏗️ 部署架构概览

### 组件分布
```
Chrome Web Store (官方发布)
├── MDAC Extension Package (.zip/.crx)
│   ├── Manifest V3 配置
│   ├── Background Service Worker
│   ├── Content Scripts
│   ├── Side Panel UI
│   └── Static Resources
├── API 依赖服务
│   ├── Google Gemini API
│   └── MDAC官方网站接口
└── 本地存储
    ├── Chrome Storage (用户数据)
    └── 扩展缓存
```

### 部署环境
- **开发环境**: 本地开发和测试
- **测试环境**: 内部测试发布
- **生产环境**: Chrome Web Store公开发布
- **企业环境**: 内部分发和管理

---

## 📦 打包和构建

### 1. 预构建检查

#### 环境检查清单
```bash
# 检查Node.js版本
node --version  # 建议 >= 16.0.0

# 检查Chrome版本
google-chrome --version  # 建议 >= 114

# 检查项目依赖
npm audit  # 检查安全漏洞
npm outdated  # 检查过期依赖
```

#### 代码质量检查
```bash
# 运行代码检查（如果配置了）
npm run lint
npm run test
npm run type-check  # 如果使用TypeScript

# 手动检查清单
echo "✅ 所有TODO和FIXME已处理"
echo "✅ 调试代码已移除"
echo "✅ 敏感信息已移除"
echo "✅ 版本号已更新"
```

### 2. 配置生产环境

#### 更新manifest.json
```json
{
  "manifest_version": 3,
  "name": "MDAC AI智能分析工具",
  "version": "2.0.0",  // 确保版本号正确
  "description": "马来西亚数字入境卡智能填充工具",
  
  // 生产环境权限最小化
  "permissions": [
    "sidePanel",
    "activeTab", 
    "storage",
    "scripting",
    "tabs"
  ],
  
  // 明确指定目标网站
  "host_permissions": [
    "https://imigresen-online.imi.gov.my/*"
  ],
  
  // 生产环境CSP配置
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://generativelanguage.googleapis.com https://imigresen-online.imi.gov.my;"
  }
}
```

#### 清理开发配置
```javascript
// config/ai-config.js - 生产环境配置
const MDAC_AI_CONFIG = {
    GEMINI_CONFIG: {
        // ❌ 移除硬编码的API密钥
        // apiKey: "your-api-key-here",
        
        // ✅ 使用环境变量或用户配置
        apiKey: '', // 用户需要自行配置
        
        model: "gemini-1.5-flash",
        endpoint: "https://generativelanguage.googleapis.com/v1beta/models/",
        maxTokens: 1000,
        temperature: 0.3
    },
    
    // 生产环境日志级别
    LOG_LEVEL: 'info', // 改为 'error' 减少日志
    
    // 生产环境性能配置
    PERFORMANCE: {
        enableDetailedLogging: false,
        enablePerformanceMonitoring: true,
        cacheTimeout: 300000 // 5分钟
    }
};
```

### 3. 构建脚本

#### 自动化构建脚本 (build.sh)
```bash
#!/bin/bash

echo "🚀 开始构建MDAC Chrome扩展..."

# 1. 清理旧构建
rm -rf dist/
mkdir -p dist/

# 2. 复制源文件
echo "📂 复制源文件..."
cp -r background/ dist/
cp -r content/ dist/
cp -r ui/ dist/
cp -r modules/ dist/
cp -r config/ dist/
cp -r icons/ dist/
cp manifest.json dist/

# 3. 清理开发文件
echo "🧹 清理开发文件..."
find dist/ -name "*.test.js" -delete
find dist/ -name "*.dev.js" -delete
find dist/ -name ".DS_Store" -delete

# 4. 压缩代码（可选）
echo "🗜️ 优化代码..."
# 如果使用压缩工具
# terser dist/**/*.js --output dist/

# 5. 验证构建
echo "✅ 验证构建结果..."
if [ -f "dist/manifest.json" ]; then
    echo "✅ manifest.json 存在"
else
    echo "❌ manifest.json 缺失"
    exit 1
fi

# 6. 创建发布包
echo "📦 创建发布包..."
cd dist/
zip -r ../mdac-extension-v$(grep '"version"' manifest.json | cut -d'"' -f4).zip .
cd ..

echo "🎉 构建完成！发布包: mdac-extension-v*.zip"
```

#### 构建验证脚本
```bash
#!/bin/bash

echo "🔍 验证构建质量..."

# 检查文件完整性
required_files=(
    "manifest.json"
    "background/background.js"
    "content/content-script.js"
    "ui/ui-sidepanel-main.js"
    "modules/event-bus.js"
    "config/ai-config.js"
)

for file in "${required_files[@]}"; do
    if [ -f "dist/$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file 缺失"
        exit 1
    fi
done

# 检查权限配置
echo "🔒 检查权限配置..."
if grep -q '"permissions"' dist/manifest.json; then
    echo "✅ 权限配置存在"
else
    echo "❌ 权限配置缺失"
fi

# 检查CSP配置
if grep -q 'content_security_policy' dist/manifest.json; then
    echo "✅ CSP配置存在"
else
    echo "⚠️ CSP配置缺失"
fi

echo "✅ 构建验证完成"
```

---

## 🚀 发布流程

### 1. Chrome Web Store 发布

#### 准备发布材料
```
发布材料清单:
├── 扩展包文件 (mdac-extension-v2.0.0.zip)
├── 应用图标 (128x128, 48x48, 16x16)
├── 商店截图 (1280x800 或 640x400)
├── 宣传图片 (440x280, 可选)
├── 详细描述文本
├── 隐私政策文档
└── 支持联系信息
```

#### 发布步骤
1. **登录Chrome Web Store开发者控制台**
   - 访问: https://chrome.google.com/webstore/devconsole
   - 使用开发者账号登录

2. **创建新项目或更新现有项目**
   ```
   如果是新发布:
   - 点击"添加新项"
   - 上传扩展包ZIP文件
   - 填写基本信息
   
   如果是更新:
   - 选择现有项目
   - 上传新版本ZIP文件
   - 更新变更日志
   ```

3. **填写商店列表信息**
   ```
   必填信息:
   - 扩展名称: "MDAC AI智能分析工具"
   - 简短描述: "马来西亚数字入境卡智能填充工具，基于AI技术自动解析和填充表单信息"
   - 详细描述: [见下文详细描述模板]
   - 类别: "生产力工具"
   - 语言: "中文 (简体)"
   ```

4. **上传视觉资源**
   ```
   图标要求:
   - 16x16: 工具栏小图标
   - 48x48: 扩展管理页面图标
   - 128x128: Chrome Web Store图标
   
   截图要求:
   - 至少1张，最多5张
   - 尺寸: 1280x800 或 640x400
   - 展示主要功能界面
   ```

5. **配置发布设置**
   ```
   可见性设置:
   - 公开: 所有用户可见
   - 不公开: 仅指定用户可见
   - 私有: 仅开发者可见
   
   定价设置:
   - 免费
   - 付费 (如适用)
   ```

6. **提交审核**
   - 点击"提交以供审核"
   - 等待Google审核（通常1-3个工作日）
   - 审核通过后自动发布

#### 商店描述模板
```
🤖 MDAC AI智能分析工具

专为马来西亚数字入境卡(MDAC)设计的智能填充工具，基于先进的AI技术，能够自动解析个人信息并智能填充表单，大幅提升填写效率和准确性。

🌟 主要功能:
• AI智能解析: 输入个人信息文本，AI自动识别和分类
• 一键填充: 智能检测表单字段，自动填充相应信息  
• 数据安全: 本地存储，保护用户隐私安全
• 实时验证: 智能验证输入格式，减少错误
• 多语言支持: 支持中文、英文等多种语言

🔒 隐私安全:
• 所有数据仅在本地存储，不上传到第三方服务器
• 使用Chrome官方Storage API，确保数据安全
• 符合Manifest V3最新安全标准

📝 使用方法:
1. 安装扩展后，访问MDAC官方网站
2. 点击扩展图标打开侧边栏
3. 输入或粘贴个人信息
4. 点击AI解析，自动识别信息
5. 一键填充到表单中

⚠️ 注意事项:
• 仅支持MDAC官方网站使用
• 需要网络连接以使用AI解析功能
• 建议在填充后再次核对信息准确性

🆘 支持与反馈:
如遇问题或有改进建议，请通过支持邮箱联系我们。

🏷️ 标签: MDAC, 马来西亚, 入境卡, AI填充, 表单助手, 自动填写
```

### 2. 企业内部分发

#### 生成CRX文件
```bash
# 使用Chrome命令行工具打包
google-chrome --pack-extension=./dist --pack-extension-key=./private.pem

# 或使用chrome-webstore-upload工具
npm install -g chrome-webstore-upload-cli
chrome-webstore-upload upload --source ./dist --extension-id YOUR_EXTENSION_ID
```

#### 企业策略分发
```xml
<!-- 企业策略配置示例 (Windows Registry / Group Policy) -->
<policy>
    <name>ExtensionInstallForcelist</name>
    <value>
        <item>extensionid;https://your-server.com/mdac-extension.crx</item>
    </value>
</policy>
```

#### 内部分发服务器配置
```nginx
# Nginx配置示例
server {
    listen 443 ssl;
    server_name extensions.your-company.com;
    
    location /mdac/ {
        root /var/www/extensions;
        
        # 设置正确的MIME类型
        location ~ \.crx$ {
            add_header Content-Type application/x-chrome-extension;
        }
        
        # 设置缓存策略
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
```

---

## 🔄 版本管理

### 1. 版本号规范

采用语义化版本控制 (SemVer): `主版本.次版本.修订版本`

```
版本格式: X.Y.Z
- X (主版本): 不兼容的API修改
- Y (次版本): 向后兼容的功能性新增
- Z (修订版本): 向后兼容的问题修正

示例:
- 1.0.0: 初始发布版本
- 1.1.0: 新增功能 (如OCR识别)
- 1.1.1: 修复bug
- 2.0.0: 重大架构变更
```

### 2. 发布分支策略

```
Git分支管理:
├── main (生产分支)
│   └── 只包含稳定的发布版本
├── develop (开发分支)  
│   └── 最新开发进度
├── feature/* (功能分支)
│   └── 新功能开发
├── release/* (发布分支)
│   └── 发布前准备和测试
└── hotfix/* (热修复分支)
    └── 紧急问题修复
```

### 3. 发布检查清单

#### 发布前检查
```markdown
## 发布前检查清单 v2.0.0

### 代码质量
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 文档已更新
- [ ] 性能测试通过
- [ ] 安全扫描通过

### 配置检查
- [ ] manifest.json版本号已更新
- [ ] 权限配置最小化
- [ ] CSP策略正确配置
- [ ] API密钥已移除
- [ ] 调试代码已清理

### 功能验证
- [ ] 核心功能正常工作
- [ ] AI解析功能正常
- [ ] 表单填充功能正常
- [ ] 数据存储功能正常
- [ ] 错误处理正常

### 兼容性测试
- [ ] Chrome最新版本测试
- [ ] Chrome稳定版本测试
- [ ] 不同操作系统测试
- [ ] MDAC网站兼容性测试

### 发布材料
- [ ] 发布包已生成并验证
- [ ] 变更日志已准备
- [ ] 发布说明已准备
- [ ] 回滚方案已准备
```

### 4. 自动化发布脚本

```bash
#!/bin/bash
# release.sh - 自动化发布脚本

VERSION=$1
if [ -z "$VERSION" ]; then
    echo "用法: ./release.sh <version>"
    echo "示例: ./release.sh 2.0.1"
    exit 1
fi

echo "🚀 开始发布版本 $VERSION..."

# 1. 检查工作目录状态
if [ -n "$(git status --porcelain)" ]; then
    echo "❌ 工作目录有未提交的更改"
    exit 1
fi

# 2. 更新版本号
echo "📝 更新版本号..."
sed -i.bak "s/\"version\": \".*\"/\"version\": \"$VERSION\"/" manifest.json
rm manifest.json.bak

# 3. 运行测试
echo "🧪 运行测试..."
npm test
if [ $? -ne 0 ]; then
    echo "❌ 测试失败"
    exit 1
fi

# 4. 构建发布包
echo "📦 构建发布包..."
./build.sh

# 5. 创建Git标签
echo "🏷️ 创建Git标签..."
git add manifest.json
git commit -m "Release version $VERSION"
git tag -a "v$VERSION" -m "Release version $VERSION"

# 6. 推送到远程仓库
echo "📤 推送到远程仓库..."
git push origin main
git push origin "v$VERSION"

echo "✅ 版本 $VERSION 发布完成！"
echo "📦 发布包: mdac-extension-v$VERSION.zip"
echo "🔗 请到Chrome Web Store上传新版本"
```

---

## 🛡️ 安全配置

### 1. 生产环境安全配置

#### 内容安全策略 (CSP)
```json
{
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://generativelanguage.googleapis.com https://imigresen-online.imi.gov.my;"
  }
}
```

#### 权限最小化原则
```json
{
  "permissions": [
    "sidePanel",     // 侧边栏功能 - 必需
    "activeTab",     // 当前标签页访问 - 必需  
    "storage",       // 数据存储 - 必需
    "scripting",     // 脚本注入 - 必需
    "tabs"           // 标签页管理 - 必需
  ],
  
  // 移除不必要的权限
  // "background",     // 不需要持久后台
  // "webRequest",     // 不需要请求拦截
  // "cookies",        // 不需要Cookie访问
  // "history"         // 不需要历史记录访问
}
```

### 2. 敏感数据处理

#### API密钥管理
```javascript
// ❌ 错误做法 - 硬编码API密钥
const API_KEY = "AIzaSyC...";

// ✅ 正确做法 - 用户配置
class APIKeyManager {
    static async getAPIKey() {
        // 从用户设置中获取
        const settings = await mdacStateManager.getState('userSettings');
        return settings?.apiKey;
    }
    
    static async setAPIKey(apiKey) {
        // 验证API密钥格式
        if (!this.validateAPIKey(apiKey)) {
            throw new Error('无效的API密钥格式');
        }
        
        // 安全存储
        await mdacStateManager.setState('userSettings', {
            ...await mdacStateManager.getState('userSettings', {}),
            apiKey: apiKey
        });
    }
    
    static validateAPIKey(apiKey) {
        // 验证Gemini API密钥格式
        return /^AIza[0-9A-Za-z-_]{35}$/.test(apiKey);
    }
}
```

#### 数据脱敏
```javascript
// 日志记录时的数据脱敏
class SecureLogger {
    static sanitizeForLog(data) {
        if (typeof data !== 'object') return data;
        
        const sanitized = { ...data };
        const sensitiveKeys = ['apiKey', 'password', 'token', 'secret'];
        
        sensitiveKeys.forEach(key => {
            if (sanitized[key]) {
                sanitized[key] = '***masked***';
            }
        });
        
        // 脱敏个人信息
        if (sanitized.passport) {
            sanitized.passport = sanitized.passport.slice(0, 2) + '***';
        }
        
        return sanitized;
    }
}
```

### 3. 网络安全

#### HTTPS强制
```javascript
// 确保所有API调用使用HTTPS
class SecureHTTPClient {
    static async makeRequest(url, options = {}) {
        // 强制使用HTTPS
        if (!url.startsWith('https://')) {
            throw new Error('仅允许HTTPS请求');
        }
        
        // 验证目标域名
        const allowedDomains = [
            'generativelanguage.googleapis.com',
            'imigresen-online.imi.gov.my'
        ];
        
        const domain = new URL(url).hostname;
        if (!allowedDomains.includes(domain)) {
            throw new Error(`不允许的目标域名: ${domain}`);
        }
        
        return fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
    }
}
```

---

## 📊 监控和维护

### 1. 健康状态监控

#### 系统健康检查
```javascript
// 扩展健康状态监控
class HealthMonitor {
    static async performHealthCheck() {
        const healthStatus = {
            timestamp: new Date().toISOString(),
            version: chrome.runtime.getManifest().version,
            status: 'healthy',
            checks: {}
        };
        
        try {
            // 检查核心模块
            healthStatus.checks.eventBus = window.mdacEventBus ? 'OK' : 'FAIL';
            healthStatus.checks.logger = window.mdacLogger ? 'OK' : 'FAIL';
            healthStatus.checks.stateManager = window.mdacStateManager ? 'OK' : 'FAIL';
            
            // 检查存储访问
            try {
                await chrome.storage.local.get('test');
                healthStatus.checks.storage = 'OK';
            } catch (error) {
                healthStatus.checks.storage = 'FAIL';
            }
            
            // 检查API连接
            try {
                const response = await fetch('https://generativelanguage.googleapis.com/');
                healthStatus.checks.apiConnectivity = response.ok ? 'OK' : 'DEGRADED';
            } catch (error) {
                healthStatus.checks.apiConnectivity = 'FAIL';
            }
            
            // 计算整体状态
            const failedChecks = Object.values(healthStatus.checks).filter(status => status === 'FAIL');
            if (failedChecks.length > 0) {
                healthStatus.status = 'unhealthy';
            } else if (Object.values(healthStatus.checks).some(status => status === 'DEGRADED')) {
                healthStatus.status = 'degraded';
            }
            
        } catch (error) {
            healthStatus.status = 'error';
            healthStatus.error = error.message;
        }
        
        return healthStatus;
    }
    
    // 定期健康检查
    static startPeriodicHealthCheck() {
        setInterval(async () => {
            const health = await this.performHealthCheck();
            
            if (health.status !== 'healthy') {
                mdacLogger.log('warn', 'HealthMonitor', '系统健康状态异常', health);
                
                // 发送状态事件
                mdacEventBus.emit('healthStatusChanged', health);
            }
        }, 5 * 60 * 1000); // 每5分钟检查一次
    }
}
```

### 2. 性能监控

#### 性能指标收集
```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.startMemoryMonitoring();
    }
    
    // 内存使用监控
    startMemoryMonitoring() {
        setInterval(() => {
            if (performance.memory) {
                const memory = performance.memory;
                const memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
                    timestamp: Date.now()
                };
                
                this.recordMetric('memory', memoryUsage);
                
                // 内存使用过高警告
                if (memoryUsage.used > 100) {
                    mdacLogger.log('warn', 'PerformanceMonitor', '内存使用过高', memoryUsage);
                }
            }
        }, 30000); // 每30秒检查一次
    }
    
    // 记录性能指标
    recordMetric(name, value) {
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        
        const metricData = this.metrics.get(name);
        metricData.push(value);
        
        // 保持最近100条记录
        if (metricData.length > 100) {
            metricData.shift();
        }
    }
    
    // 获取性能报告
    getPerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            metrics: {}
        };
        
        this.metrics.forEach((values, name) => {
            if (values.length > 0) {
                const latest = values[values.length - 1];
                const avg = values.reduce((sum, val) => sum + (val.used || val), 0) / values.length;
                
                report.metrics[name] = {
                    latest: latest,
                    average: Math.round(avg),
                    samples: values.length
                };
            }
        });
        
        return report;
    }
}
```

### 3. 错误监控和报告

#### 错误收集器
```javascript
class ErrorCollector {
    constructor() {
        this.errors = [];
        this.setupGlobalErrorHandling();
    }
    
    setupGlobalErrorHandling() {
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                type: 'unhandledRejection',
                message: event.reason?.message || String(event.reason),
                stack: event.reason?.stack,
                timestamp: new Date().toISOString()
            });
        });
        
        // 捕获全局错误
        window.addEventListener('error', (event) => {
            this.recordError({
                type: 'globalError',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: new Date().toISOString()
            });
        });
    }
    
    recordError(error) {
        this.errors.push(error);
        
        // 记录到日志
        mdacLogger.log('error', 'ErrorCollector', error.message, error);
        
        // 保持最近50个错误
        if (this.errors.length > 50) {
            this.errors.shift();
        }
        
        // 发送错误事件
        mdacEventBus.emit('errorRecorded', error);
    }
    
    getErrorReport() {
        return {
            timestamp: new Date().toISOString(),
            totalErrors: this.errors.length,
            recentErrors: this.errors.slice(-10),
            errorTypes: this.getErrorTypeStats()
        };
    }
    
    getErrorTypeStats() {
        const stats = {};
        this.errors.forEach(error => {
            stats[error.type] = (stats[error.type] || 0) + 1;
        });
        return stats;
    }
}
```

### 4. 自动化维护任务

#### 定期清理任务
```javascript
class MaintenanceScheduler {
    static startScheduledTasks() {
        // 每小时清理过期缓存
        setInterval(() => {
            this.cleanupExpiredCache();
        }, 60 * 60 * 1000);
        
        // 每6小时清理旧日志
        setInterval(() => {
            this.cleanupOldLogs();
        }, 6 * 60 * 60 * 1000);
        
        // 每24小时生成状态报告
        setInterval(() => {
            this.generateStatusReport();
        }, 24 * 60 * 60 * 1000);
    }
    
    static async cleanupExpiredCache() {
        try {
            const allData = await chrome.storage.local.get();
            const expiredKeys = [];
            
            Object.entries(allData).forEach(([key, value]) => {
                if (value.expires && Date.now() > value.expires) {
                    expiredKeys.push(key);
                }
            });
            
            if (expiredKeys.length > 0) {
                await chrome.storage.local.remove(expiredKeys);
                mdacLogger.log('info', 'Maintenance', `清理了${expiredKeys.length}个过期缓存项`);
            }
        } catch (error) {
            mdacLogger.log('error', 'Maintenance', '缓存清理失败', { error: error.message });
        }
    }
    
    static cleanupOldLogs() {
        try {
            const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7天前
            const logs = mdacLogger.getLogs();
            const recentLogs = logs.filter(log => new Date(log.timestamp).getTime() > cutoffTime);
            
            if (recentLogs.length < logs.length) {
                mdacLogger.clearLogs();
                recentLogs.forEach(log => {
                    mdacLogger.log(log.level, log.category, log.message, log.details);
                });
                mdacLogger.log('info', 'Maintenance', `清理了${logs.length - recentLogs.length}条旧日志`);
            }
        } catch (error) {
            mdacLogger.log('error', 'Maintenance', '日志清理失败', { error: error.message });
        }
    }
    
    static async generateStatusReport() {
        try {
            const health = await HealthMonitor.performHealthCheck();
            const performance = window.performanceMonitor?.getPerformanceReport();
            const errors = window.errorCollector?.getErrorReport();
            
            const report = {
                timestamp: new Date().toISOString(),
                health: health,
                performance: performance,
                errors: errors,
                version: chrome.runtime.getManifest().version
            };
            
            // 保存状态报告
            await mdacStateManager.setState('statusReport', report);
            mdacLogger.log('info', 'Maintenance', '状态报告已生成');
            
        } catch (error) {
            mdacLogger.log('error', 'Maintenance', '状态报告生成失败', { error: error.message });
        }
    }
}
```

---

## 🚨 故障恢复和应急处理

### 1. 故障检测

#### 自动故障检测
```javascript
class FailureDetector {
    static async detectCriticalFailures() {
        const failures = [];
        
        // 检测模块加载失败
        const requiredModules = ['mdacEventBus', 'mdacLogger', 'mdacStateManager'];
        requiredModules.forEach(module => {
            if (!window[module]) {
                failures.push({
                    type: 'MODULE_LOAD_FAILURE',
                    module: module,
                    severity: 'critical'
                });
            }
        });
        
        // 检测存储访问失败
        try {
            await chrome.storage.local.set({ 'test': Date.now() });
            await chrome.storage.local.remove('test');
        } catch (error) {
            failures.push({
                type: 'STORAGE_ACCESS_FAILURE',
                error: error.message,
                severity: 'critical'
            });
        }
        
        // 检测网络连接问题
        try {
            const response = await fetch('https://www.google.com/favicon.ico', {
                mode: 'no-cors',
                cache: 'no-cache'
            });
        } catch (error) {
            failures.push({
                type: 'NETWORK_CONNECTIVITY_FAILURE',
                error: error.message,
                severity: 'warning'
            });
        }
        
        return failures;
    }
}
```

### 2. 自动恢复机制

#### 模块重载
```javascript
class AutoRecovery {
    static async attemptModuleRecovery(moduleName) {
        try {
            mdacLogger.log('warn', 'AutoRecovery', `尝试恢复模块: ${moduleName}`);
            
            // 重新加载模块
            const script = document.createElement('script');
            script.src = `modules/${moduleName.toLowerCase().replace('mdac', '')}.js`;
            
            return new Promise((resolve, reject) => {
                script.onload = () => {
                    if (window[moduleName]) {
                        mdacLogger.log('info', 'AutoRecovery', `模块恢复成功: ${moduleName}`);
                        resolve(true);
                    } else {
                        reject(new Error('模块加载后仍不可用'));
                    }
                };
                
                script.onerror = () => {
                    reject(new Error('模块加载失败'));
                };
                
                document.head.appendChild(script);
            });
            
        } catch (error) {
            mdacLogger.log('error', 'AutoRecovery', `模块恢复失败: ${moduleName}`, { error: error.message });
            return false;
        }
    }
    
    static async performSystemRecovery() {
        mdacLogger.log('warn', 'AutoRecovery', '开始系统恢复流程');
        
        const failures = await FailureDetector.detectCriticalFailures();
        const recoveryResults = [];
        
        for (const failure of failures) {
            switch (failure.type) {
                case 'MODULE_LOAD_FAILURE':
                    const recovered = await this.attemptModuleRecovery(failure.module);
                    recoveryResults.push({ failure, recovered });
                    break;
                    
                case 'STORAGE_ACCESS_FAILURE':
                    // 尝试重置存储
                    try {
                        await chrome.storage.local.clear();
                        recoveryResults.push({ failure, recovered: true });
                    } catch (error) {
                        recoveryResults.push({ failure, recovered: false, error: error.message });
                    }
                    break;
            }
        }
        
        return recoveryResults;
    }
}
```

### 3. 紧急回滚

#### 版本回滚脚本
```bash
#!/bin/bash
# rollback.sh - 紧急回滚脚本

ROLLBACK_VERSION=$1
if [ -z "$ROLLBACK_VERSION" ]; then
    echo "用法: ./rollback.sh <回滚版本>"
    echo "示例: ./rollback.sh 1.9.5"
    exit 1
fi

echo "🚨 开始紧急回滚到版本 $ROLLBACK_VERSION..."

# 1. 检查回滚版本是否存在
if ! git rev-parse "v$ROLLBACK_VERSION" >/dev/null 2>&1; then
    echo "❌ 版本 $ROLLBACK_VERSION 不存在"
    exit 1
fi

# 2. 创建回滚分支
git checkout -b "rollback-to-v$ROLLBACK_VERSION" "v$ROLLBACK_VERSION"

# 3. 更新版本号
PATCH_VERSION=$(echo $ROLLBACK_VERSION | awk -F. '{print $1"."$2"."$3+1}')
sed -i.bak "s/\"version\": \".*\"/\"version\": \"$PATCH_VERSION\"/" manifest.json
rm manifest.json.bak

# 4. 构建回滚版本
./build.sh

# 5. 创建回滚提交
git add manifest.json
git commit -m "Emergency rollback to v$ROLLBACK_VERSION (as v$PATCH_VERSION)"

# 6. 推送回滚版本
git push origin "rollback-to-v$ROLLBACK_VERSION"

echo "✅ 回滚完成！"
echo "📦 回滚包: mdac-extension-v$PATCH_VERSION.zip"
echo "🔄 请立即上传到Chrome Web Store"
echo "📧 记得通知用户相关变更"
```

### 4. 应急联系流程

#### 故障响应流程
```markdown
## 紧急故障响应流程

### 故障等级定义
- P0 (严重): 扩展完全无法使用，影响所有用户
- P1 (高级): 核心功能故障，影响大部分用户
- P2 (中级): 部分功能故障，影响少数用户
- P3 (低级): 非关键功能问题，轻微影响

### 响应时间要求
- P0: 立即响应（1小时内）
- P1: 快速响应（4小时内）
- P2: 正常响应（24小时内）
- P3: 计划响应（48小时内）

### 联系方式
- 技术负责人: [技术负责人联系方式]
- 项目经理: [项目经理联系方式]
- Chrome Web Store支持: [支持邮箱]

### 应急处理步骤
1. 确认故障等级和影响范围
2. 启动应急响应团队
3. 实施临时修复措施
4. 准备和发布修复版本
5. 监控修复效果
6. 总结和改进流程
```

---

## 📋 维护检查清单

### 每日检查 (自动化)
- [ ] 系统健康状态检查
- [ ] 错误日志审查
- [ ] 性能指标监控
- [ ] 用户反馈收集

### 每周检查
- [ ] 安全更新检查
- [ ] 依赖项更新检查
- [ ] 备份数据验证
- [ ] 监控报告分析

### 每月检查
- [ ] 全面功能测试
- [ ] 性能基准测试
- [ ] 安全漏洞扫描
- [ ] 用户反馈分析
- [ ] 竞品功能对比

### 季度检查
- [ ] 架构设计审查
- [ ] 技术债务评估
- [ ] 容量规划更新
- [ ] 灾难恢复演练
- [ ] 团队培训更新

---

## 📚 相关文档

- [开发者指南](./DEVELOPER_GUIDE.md) - 开发环境和工作流程
- [API接口文档](./API_REFERENCE.md) - 详细API说明
- [用户使用手册](./USER_MANUAL.md) - 最终用户指南
- [故障排除指南](./TROUBLESHOOTING_GUIDE.md) - 问题诊断和解决

---

## 📞 技术支持

### 获取支持
如需技术支持，请提供以下信息：
1. 扩展版本号
2. Chrome浏览器版本
3. 操作系统信息
4. 详细错误描述
5. 复现步骤
6. 相关日志信息

### 支持渠道
- 📧 邮箱支持: [<EMAIL>]
- 🐛 问题报告: [GitHub Issues链接]
- 📖 文档中心: [文档网站链接]

---

**文档版本**: 1.0  
**最后更新**: 2025-07-15  
**维护者**: MDAC开发团队  
**审核者**: 系统架构师