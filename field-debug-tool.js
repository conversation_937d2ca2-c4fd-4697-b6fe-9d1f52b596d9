/**
 * MDAC字段调试工具
 * 用于分析和修复问题字段的填充问题
 */

class MDACFieldDebugger {
  constructor() {
    this.problemFields = [
      'passExpDte',  // 护照到期日期
      'region',      // 国家代码
      'mobile',      // 手机号码
      'arrDt',       // 到达日期
      'depDt'        // 离开日期
    ];
    
    this.fieldElements = {};
    this.fieldProperties = {};
    this.eventListeners = {};
    this.validationRules = {};
  }
  
  /**
   * 初始化调试工具
   */
  async init() {
    console.log('🔍 MDAC字段调试工具初始化...');
    
    // 获取字段元素
    this.findFieldElements();
    
    // 分析字段属性
    this.analyzeFieldProperties();
    
    // 监听字段事件
    this.setupEventListeners();
    
    // 分析验证规则
    this.analyzeValidationRules();
    
    console.log('✅ 调试工具初始化完成');
  }
  
  /**
   * 查找字段元素
   */
  findFieldElements() {
    console.log('🔍 查找问题字段元素...');
    
    this.problemFields.forEach(fieldId => {
      const element = document.getElementById(fieldId);
      
      if (element) {
        this.fieldElements[fieldId] = element;
        console.log(`✅ 找到字段 ${fieldId}: ${element.tagName}[${element.type || 'N/A'}]`);
      } else {
        console.warn(`⚠️ 未找到字段 ${fieldId}`);
        
        // 尝试使用其他选择器查找
        const alternativeSelectors = [
          `[name="${fieldId}"]`,
          `[data-field="${fieldId}"]`,
          `[aria-label*="${fieldId}"]`,
          `label[for*="${fieldId}"] + *`
        ];
        
        for (const selector of alternativeSelectors) {
          const altElement = document.querySelector(selector);
          if (altElement) {
            this.fieldElements[fieldId] = altElement;
            console.log(`✅ 使用选择器 ${selector} 找到字段 ${fieldId}`);
            break;
          }
        }
      }
    });
    
    console.log('📊 字段查找结果:', Object.keys(this.fieldElements).length, '/', this.problemFields.length);
  }
  
  /**
   * 分析字段属性
   */
  analyzeFieldProperties() {
    console.log('🔍 分析字段属性...');
    
    for (const [fieldId, element] of Object.entries(this.fieldElements)) {
      // 收集基本属性
      const properties = {
        tagName: element.tagName.toLowerCase(),
        type: element.type || 'N/A',
        id: element.id,
        name: element.name,
        value: element.value,
        placeholder: element.placeholder,
        required: element.required,
        readOnly: element.readOnly,
        disabled: element.disabled,
        maxLength: element.maxLength,
        pattern: element.pattern,
        classList: Array.from(element.classList),
        attributes: this.getElementAttributes(element),
        dataAttributes: this.getDataAttributes(element),
        parentNode: {
          tagName: element.parentNode.tagName,
          id: element.parentNode.id,
          classList: Array.from(element.parentNode.classList)
        }
      };
      
      // 特殊属性处理
      if (properties.tagName === 'select') {
        properties.options = Array.from(element.options).map(option => ({
          value: option.value,
          text: option.text,
          selected: option.selected
        }));
        properties.optionCount = element.options.length;
      }
      
      this.fieldProperties[fieldId] = properties;
      console.log(`✅ 分析字段 ${fieldId} 属性完成`);
    }
  }
  
  /**
   * 获取元素的所有属性
   */
  getElementAttributes(element) {
    const attributes = {};
    for (const attr of element.attributes) {
      attributes[attr.name] = attr.value;
    }
    return attributes;
  }
  
  /**
   * 获取元素的data-*属性
   */
  getDataAttributes(element) {
    const dataAttributes = {};
    for (const key in element.dataset) {
      dataAttributes[key] = element.dataset[key];
    }
    return dataAttributes;
  }
  
  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    console.log('🔍 监听字段事件...');
    
    for (const [fieldId, element] of Object.entries(this.fieldElements)) {
      this.eventListeners[fieldId] = {
        events: [],
        handlers: {}
      };
      
      // 监听常见事件
      const events = ['focus', 'blur', 'input', 'change', 'keyup', 'keydown'];
      
      events.forEach(eventType => {
        const handler = event => {
          this.eventListeners[fieldId].events.push({
            type: event.type,
            timestamp: new Date().toISOString(),
            value: element.value
          });
          
          console.log(`📝 字段 ${fieldId} 触发 ${event.type} 事件:`, element.value);
        };
        
        element.addEventListener(eventType, handler);
        this.eventListeners[fieldId].handlers[eventType] = handler;
      });
      
      console.log(`✅ 为字段 ${fieldId} 设置事件监听器`);
    }
  }
  
  /**
   * 分析验证规则
   */
  analyzeValidationRules() {
    console.log('🔍 分析字段验证规则...');
    
    for (const [fieldId, element] of Object.entries(this.fieldElements)) {
      const rules = {
        required: element.required,
        pattern: element.pattern,
        minLength: element.minLength,
        maxLength: element.maxLength,
        min: element.min,
        max: element.max,
        customValidation: false
      };
      
      // 检查是否有自定义验证
      if (typeof element.checkValidity === 'function') {
        const originalValue = element.value;
        
        // 测试一些常见值
        const testValues = {
          empty: '',
          invalid: 'invalid',
          date: '01/01/2025',
          number: '12345',
          email: '<EMAIL>'
        };
        
        const validationResults = {};
        
        for (const [key, value] of Object.entries(testValues)) {
          element.value = value;
          validationResults[key] = {
            valid: element.checkValidity(),
            validationMessage: element.validationMessage
          };
        }
        
        // 恢复原始值
        element.value = originalValue;
        
        rules.testResults = validationResults;
        rules.customValidation = Object.values(validationResults).some(r => !r.valid);
      }
      
      this.validationRules[fieldId] = rules;
      console.log(`✅ 分析字段 ${fieldId} 验证规则完成`);
    }
  }
  
  /**
   * 测试字段填充
   */
  async testFieldFilling() {
    console.log('🧪 测试字段填充...');
    
    const testData = {
      passExpDte: '31/12/2030',
      region: '+60',
      mobile: '167372551',
      arrDt: '01/08/2025',
      depDt: '15/08/2025'
    };
    
    const results = {
      success: [],
      failure: []
    };
    
    for (const [fieldId, element] of Object.entries(this.fieldElements)) {
      const testValue = testData[fieldId];
      if (!testValue) continue;
      
      console.log(`🧪 测试填充字段 ${fieldId}: "${testValue}"`);
      
      try {
        // 保存原始值
        const originalValue = element.value;
        
        // 测试基本填充
        await this.testBasicFilling(fieldId, element, testValue);
        
        // 测试事件序列填充
        await this.testEventSequenceFilling(fieldId, element, testValue);
        
        // 测试特殊填充方法
        await this.testSpecialFilling(fieldId, element, testValue);
        
        // 检查填充结果
        if (element.value === testValue) {
          results.success.push(fieldId);
          console.log(`✅ 字段 ${fieldId} 填充成功`);
        } else {
          results.failure.push({
            fieldId,
            expected: testValue,
            actual: element.value
          });
          console.warn(`❌ 字段 ${fieldId} 填充失败: 期望"${testValue}", 实际"${element.value}"`);
          
          // 恢复原始值
          element.value = originalValue;
        }
      } catch (error) {
        results.failure.push({
          fieldId,
          error: error.message
        });
        console.error(`❌ 字段 ${fieldId} 填充异常:`, error);
      }
    }
    
    console.log('📊 字段填充测试结果:', results);
    return results;
  }
  
  /**
   * 测试基本填充方法
   */
  async testBasicFilling(fieldId, element, value) {
    console.log(`🧪 测试基本填充 ${fieldId}`);
    
    // 清空字段
    element.value = '';
    
    // 直接设置值
    element.value = value;
    
    // 触发事件
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    
    // 等待可能的异步验证
    await this.delay(300);
    
    return element.value === value;
  }
  
  /**
   * 测试事件序列填充
   */
  async testEventSequenceFilling(fieldId, element, value) {
    console.log(`🧪 测试事件序列填充 ${fieldId}`);
    
    // 清空字段
    element.value = '';
    
    // 触发焦点事件
    element.dispatchEvent(new Event('focus', { bubbles: true }));
    await this.delay(100);
    
    // 模拟逐字输入
    for (const char of value) {
      element.value += char;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50);
    }
    
    // 触发变更事件
    element.dispatchEvent(new Event('change', { bubbles: true }));
    await this.delay(100);
    
    // 触发失焦事件
    element.dispatchEvent(new Event('blur', { bubbles: true }));
    await this.delay(300);
    
    return element.value === value;
  }
  
  /**
   * 测试特殊填充方法
   */
  async testSpecialFilling(fieldId, element, value) {
    console.log(`🧪 测试特殊填充 ${fieldId}`);
    
    // 根据字段类型使用特殊方法
    switch (fieldId) {
      case 'passExpDte':
      case 'arrDt':
      case 'depDt':
        return await this.testDateFieldFilling(fieldId, element, value);
        
      case 'region':
        return await this.testSelectFieldFilling(fieldId, element, value);
        
      case 'mobile':
        return await this.testMobileFieldFilling(fieldId, element, value);
        
      default:
        return false;
    }
  }
  
  /**
   * 测试日期字段填充
   */
  async testDateFieldFilling(fieldId, element, value) {
    console.log(`🧪 测试日期字段填充 ${fieldId}`);
    
    // 检查是否使用jQuery datepicker
    if (element.classList.contains('hasDatepicker') && typeof $ !== 'undefined') {
      try {
        $(element).datepicker('setDate', value);
        return true;
      } catch (error) {
        console.warn('jQuery datepicker设置失败:', error);
      }
    }
    
    // 标准日期填充
    element.value = '';
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    
    return element.value === value;
  }
  
  /**
   * 测试下拉框字段填充
   */
  async testSelectFieldFilling(fieldId, element, value) {
    console.log(`🧪 测试下拉框填充 ${fieldId}`);
    
    if (element.tagName !== 'SELECT') {
      console.warn(`字段 ${fieldId} 不是SELECT元素`);
      return false;
    }
    
    // 检查是否使用Select2
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
      try {
        $(element).val(value).trigger('change');
        return true;
      } catch (error) {
        console.warn('Select2设置失败:', error);
      }
    }
    
    // 标准下拉框填充
    element.value = value;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    
    return element.value === value;
  }
  
  /**
   * 测试手机号码字段填充
   */
  async testMobileFieldFilling(fieldId, element, value) {
    console.log(`🧪 测试手机号码字段填充 ${fieldId}`);
    
    // 清除现有值
    element.value = '';
    
    // 确保只包含数字
    const numericValue = value.replace(/\D/g, '');
    
    // 模拟逐字输入
    for (const char of numericValue) {
      element.value += char;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50);
    }
    
    // 触发验证事件
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.dispatchEvent(new Event('blur', { bubbles: true }));
    
    return element.value === numericValue;
  }
  
  /**
   * 生成修复方案
   */
  generateFixSolutions() {
    console.log('🔧 生成字段修复方案...');
    
    const solutions = {};
    
    for (const fieldId of this.problemFields) {
      const element = this.fieldElements[fieldId];
      if (!element) continue;
      
      const properties = this.fieldProperties[fieldId];
      
      switch (fieldId) {
        case 'passExpDte':
        case 'arrDt':
        case 'depDt':
          solutions[fieldId] = this.generateDateFieldSolution(fieldId, element, properties);
          break;
          
        case 'region':
          solutions[fieldId] = this.generateSelectFieldSolution(fieldId, element, properties);
          break;
          
        case 'mobile':
          solutions[fieldId] = this.generateMobileFieldSolution(fieldId, element, properties);
          break;
      }
    }
    
    console.log('✅ 修复方案生成完成:', solutions);
    return solutions;
  }
  
  /**
   * 生成日期字段修复方案
   */
  generateDateFieldSolution(fieldId, element, properties) {
    return {
      fieldId,
      type: 'date',
      solution: `
/**
 * 填充日期字段 - ${fieldId}
 */
async function fill${this.capitalize(fieldId)}Field(field, value) {
  if (!field) return false;
  
  try {
    // 确保日期格式为DD/MM/YYYY
    const formattedDate = formatDateForMDAC(value);
    
    // 清空字段
    field.value = '';
    
    // 触发焦点事件
    field.focus();
    field.dispatchEvent(new Event('focus', { bubbles: true }));
    await delay(100);
    
    // 设置值
    field.value = formattedDate;
    
    // 触发事件序列
    field.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('blur', { bubbles: true }));
    
    // 验证填充结果
    return field.value === formattedDate;
  } catch (error) {
    console.error(\`填充${fieldId}字段失败:\`, error);
    return false;
  }
}`
    };
  }
  
  /**
   * 生成下拉框字段修复方案
   */
  generateSelectFieldSolution(fieldId, element, properties) {
    return {
      fieldId,
      type: 'select',
      solution: `
/**
 * 填充国家代码字段 - ${fieldId}
 */
async function fill${this.capitalize(fieldId)}Field(field, value) {
  if (!field) return false;
  
  try {
    // 直接设置值
    field.value = value;
    
    // 触发change事件
    field.dispatchEvent(new Event('change', { bubbles: true }));
    
    // 检查是否使用Select2
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
      try {
        $(field).val(value).trigger('change');
      } catch (error) {
        console.warn('Select2设置失败:', error);
      }
    }
    
    // 等待可能的依赖字段更新
    await delay(200);
    
    return field.value === value;
  } catch (error) {
    console.error(\`填充${fieldId}字段失败:\`, error);
    return false;
  }
}`
    };
  }
  
  /**
   * 生成手机号码字段修复方案
   */
  generateMobileFieldSolution(fieldId, element, properties) {
    return {
      fieldId,
      type: 'mobile',
      solution: `
/**
 * 填充手机号码字段 - ${fieldId}
 */
async function fill${this.capitalize(fieldId)}Field(field, value) {
  if (!field) return false;
  
  try {
    // 清除现有值
    field.value = '';
    
    // 确保只包含数字
    const numericValue = value.replace(/\\D/g, '');
    
    // 触发焦点事件
    field.focus();
    field.dispatchEvent(new Event('focus', { bubbles: true }));
    await delay(100);
    
    // 设置值
    field.value = numericValue;
    
    // 触发事件序列
    field.dispatchEvent(new Event('input', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('change', { bubbles: true }));
    await delay(50);
    field.dispatchEvent(new Event('blur', { bubbles: true }));
    
    return field.value === numericValue;
  } catch (error) {
    console.error(\`填充${fieldId}字段失败:\`, error);
    return false;
  }
}`
    };
  }
  
  /**
   * 首字母大写
   */
  capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  
  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建调试器实例
const debugger = new MDACFieldDebugger();

// 导出到全局
window.MDACFieldDebugger = debugger;

// 自动初始化
debugger.init().then(() => {
  console.log('🚀 MDAC字段调试工具已准备就绪');
  console.log('使用 window.MDACFieldDebugger 访问调试工具');
  console.log('例如: MDACFieldDebugger.testFieldFilling()');
});
