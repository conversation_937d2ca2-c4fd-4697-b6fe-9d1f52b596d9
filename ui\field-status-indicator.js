/**
 * 字段状态指示器
 * 为表单字段提供视觉反馈，显示填充状态
 */

class FieldStatusIndicator {
  constructor() {
    this.indicators = new Map();
    this.createStyles();
  }

  /**
   * 创建样式
   */
  createStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .mdac-field-status {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: white;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1000;
      }
      
      .mdac-field-status.visible {
        opacity: 1;
      }
      
      .mdac-field-status.success {
        background-color: #4CAF50;
      }
      
      .mdac-field-status.error {
        background-color: #F44336;
      }
      
      .mdac-field-status.warning {
        background-color: #FFC107;
      }
      
      .mdac-field-status.loading {
        background-color: #2196F3;
        animation: mdac-spin 1s linear infinite;
      }
      
      @keyframes mdac-spin {
        0% { transform: translateY(-50%) rotate(0deg); }
        100% { transform: translateY(-50%) rotate(360deg); }
      }
      
      .mdac-field-container {
        position: relative;
      }
      
      .mdac-field-tooltip {
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #333;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
        z-index: 1001;
      }
      
      .mdac-field-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #333 transparent transparent transparent;
      }
      
      .mdac-field-status:hover + .mdac-field-tooltip {
        opacity: 1;
        visibility: visible;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 添加状态指示器
   */
  addIndicator(field, fieldId) {
    if (!field || this.indicators.has(fieldId)) return;
    
    // 创建容器
    const container = document.createElement('div');
    container.className = 'mdac-field-container';
    
    // 保存原始样式
    const originalStyles = {
      position: field.style.position,
      width: field.style.width
    };
    
    // 设置字段样式
    if (field.style.position !== 'absolute' && field.style.position !== 'relative') {
      field.style.position = 'relative';
    }
    
    // 创建状态指示器
    const indicator = document.createElement('div');
    indicator.className = 'mdac-field-status';
    indicator.innerHTML = '✓';
    
    // 创建提示框
    const tooltip = document.createElement('div');
    tooltip.className = 'mdac-field-tooltip';
    tooltip.textContent = '字段已填充';
    
    // 将字段包装在容器中
    field.parentNode.insertBefore(container, field);
    container.appendChild(field);
    container.appendChild(indicator);
    container.appendChild(tooltip);
    
    // 存储引用
    this.indicators.set(fieldId, {
      field,
      indicator,
      tooltip,
      container,
      originalStyles
    });
  }

  /**
   * 更新状态
   */
  updateStatus(fieldId, status, message = '') {
    const item = this.indicators.get(fieldId);
    if (!item) return;
    
    const { indicator, tooltip } = item;
    
    // 重置类
    indicator.className = 'mdac-field-status';
    
    // 设置图标
    switch (status) {
      case 'success':
        indicator.innerHTML = '✓';
        indicator.classList.add('success', 'visible');
        break;
        
      case 'error':
        indicator.innerHTML = '✗';
        indicator.classList.add('error', 'visible');
        break;
        
      case 'warning':
        indicator.innerHTML = '!';
        indicator.classList.add('warning', 'visible');
        break;
        
      case 'loading':
        indicator.innerHTML = '↻';
        indicator.classList.add('loading', 'visible');
        break;
        
      case 'hidden':
        indicator.classList.remove('visible');
        break;
    }
    
    // 更新提示消息
    if (message) {
      tooltip.textContent = message;
    } else {
      switch (status) {
        case 'success':
          tooltip.textContent = '字段已成功填充';
          break;
          
        case 'error':
          tooltip.textContent = '字段填充失败';
          break;
          
        case 'warning':
          tooltip.textContent = '字段需要注意';
          break;
          
        case 'loading':
          tooltip.textContent = '正在填充...';
          break;
      }
    }
  }

  /**
   * 移除状态指示器
   */
  removeIndicator(fieldId) {
    const item = this.indicators.get(fieldId);
    if (!item) return;
    
    const { field, container, originalStyles } = item;
    
    // 恢复原始样式
    field.style.position = originalStyles.position;
    field.style.width = originalStyles.width;
    
    // 将字段移回原位置
    container.parentNode.insertBefore(field, container);
    container.remove();
    
    // 移除引用
    this.indicators.delete(fieldId);
  }

  /**
   * 清除所有状态指示器
   */
  clearAll() {
    for (const fieldId of this.indicators.keys()) {
      this.removeIndicator(fieldId);
    }
  }
}

// 导出类
window.FieldStatusIndicator = FieldStatusIndicator;
