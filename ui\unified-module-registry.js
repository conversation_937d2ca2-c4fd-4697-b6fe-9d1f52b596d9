/**
 * MDAC 统一模块注册表
 * Stage 2: 统一架构实现的核心组件
 * 
 * 负责管理和协调以下两个系统：
 * 1. 传统系统：ui-sidepanel-main.js (1672行单体架构)
 * 2. 模块化系统：ui/sidepanel/* (25+模块化架构)
 */

class UnifiedModuleRegistry {
    constructor() {
        this.modules = new Map();
        this.loadedSystems = new Set();
        this.eventBus = null;
        this.stateManager = null;
        this.messageQueue = [];
        this.isInitialized = false;
        this.hotReloadCount = 0;
        this.maxHotReloads = 3;
        
        console.log('🏗️ [UnifiedRegistry] 统一模块注册表初始化中...');
        this.init();
    }

    async init() {
        try {
            console.log('📋 [UnifiedRegistry] 开始系统统一初始化...');
            
            // 步骤1: 检测现有系统
            await this.detectExistingSystems();
            
            // 步骤2: 初始化事件总线
            await this.initializeEventBus();
            
            // 步骤3: 创建状态管理器
            await this.initializeStateManager();
            
            // 步骤4: 注册核心模块
            await this.registerCoreModules();
            
            // 步骤5: 建立系统桥接
            await this.establishSystemBridge();
            
            // 步骤6: 启动热重启监控
            this.startHotReloadMonitoring();
            
            this.isInitialized = true;
            console.log('✅ [UnifiedRegistry] 统一架构初始化完成');
            
            // 通知其他组件
            this.emitEvent('unified-registry-ready', {
                loadedSystems: Array.from(this.loadedSystems),
                moduleCount: this.modules.size
            });
            
        } catch (error) {
            console.error('❌ [UnifiedRegistry] 初始化失败:', error);
            await this.handleInitializationFailure(error);
        }
    }

    /**
     * 检测现有系统
     */
    async detectExistingSystems() {
        console.log('🔍 [UnifiedRegistry] 检测现有系统...');
        
        // 检测传统系统
        if (typeof window.mdacSidePanelApp !== 'undefined') {
            this.loadedSystems.add('traditional');
            console.log('✅ [UnifiedRegistry] 检测到传统系统: ui-sidepanel-main.js');
        }
        
        // 检测模块化系统
        if (typeof window.mdacModularSidePanel !== 'undefined') {
            this.loadedSystems.add('modular');
            console.log('✅ [UnifiedRegistry] 检测到模块化系统: ui/sidepanel/*');
        }
        
        // 检测事件总线
        if (typeof window.mdacEventBus !== 'undefined') {
            this.eventBus = window.mdacEventBus;
            console.log('✅ [UnifiedRegistry] 检测到现有事件总线');
        }
        
        console.log(`📊 [UnifiedRegistry] 系统检测完成，发现 ${this.loadedSystems.size} 个系统`);
    }

    /**
     * 初始化事件总线
     */
    async initializeEventBus() {
        if (!this.eventBus) {
            console.log('🚌 [UnifiedRegistry] 创建统一事件总线...');
            
            // 创建简单的事件总线
            this.eventBus = {
                listeners: new Map(),
                
                on(event, callback) {
                    if (!this.listeners.has(event)) {
                        this.listeners.set(event, []);
                    }
                    this.listeners.get(event).push(callback);
                },
                
                emit(event, data) {
                    if (this.listeners.has(event)) {
                        this.listeners.get(event).forEach(callback => {
                            try {
                                callback(data);
                            } catch (error) {
                                console.error(`❌ [EventBus] 事件 ${event} 处理失败:`, error);
                            }
                        });
                    }
                },
                
                off(event, callback) {
                    if (this.listeners.has(event)) {
                        const callbacks = this.listeners.get(event);
                        const index = callbacks.indexOf(callback);
                        if (index > -1) {
                            callbacks.splice(index, 1);
                        }
                    }
                }
            };
            
            // 设置为全局事件总线
            window.mdacEventBus = this.eventBus;
            console.log('✅ [UnifiedRegistry] 事件总线创建完成');
        } else {
            console.log('✅ [UnifiedRegistry] 使用现有事件总线');
        }
    }

    /**
     * 初始化状态管理器
     */
    async initializeStateManager() {
        console.log('📊 [UnifiedRegistry] 创建统一状态管理器...');
        
        this.stateManager = {
            state: new Map(),
            
            setState(key, value) {
                const oldValue = this.state.get(key);
                this.state.set(key, value);
                
                // 触发状态变化事件
                if (window.mdacEventBus) {
                    window.mdacEventBus.emit('state-changed', {
                        key,
                        oldValue,
                        newValue: value
                    });
                }
            },
            
            getState(key, defaultValue = null) {
                return this.state.has(key) ? this.state.get(key) : defaultValue;
            },
            
            hasState(key) {
                return this.state.has(key);
            },
            
            removeState(key) {
                const hadKey = this.state.has(key);
                this.state.delete(key);
                
                if (hadKey && window.mdacEventBus) {
                    window.mdacEventBus.emit('state-removed', { key });
                }
            },
            
            getAllState() {
                return Object.fromEntries(this.state);
            }
        };
        
        // 设置为全局状态管理器
        window.mdacUnifiedStateManager = this.stateManager;
        console.log('✅ [UnifiedRegistry] 状态管理器创建完成');
    }

    /**
     * 注册核心模块
     */
    async registerCoreModules() {
        console.log('📦 [UnifiedRegistry] 注册核心模块...');
        
        const coreModules = [
            { name: 'Logger', priority: 1, required: true },
            { name: 'ErrorRecoveryManager', priority: 2, required: true },
            { name: 'FormFieldDetector', priority: 3, required: true },
            { name: 'GoogleMapsIntegration', priority: 4, required: false },
            { name: 'FillMonitor', priority: 5, required: false },
            { name: 'FieldStatusDisplay', priority: 6, required: false }
        ];
        
        for (const moduleInfo of coreModules) {
            await this.registerModule(moduleInfo);
        }
        
        console.log(`✅ [UnifiedRegistry] 核心模块注册完成，共 ${this.modules.size} 个模块`);
    }

    /**
     * 注册单个模块
     */
    async registerModule(moduleInfo) {
        const { name, priority, required } = moduleInfo;
        
        try {
            // 检查模块是否已存在
            let moduleInstance = null;
            
            // 尝试从全局作用域获取
            if (typeof window[name] !== 'undefined') {
                moduleInstance = window[name];
                console.log(`✅ [UnifiedRegistry] 发现全局模块: ${name}`);
            }
            
            // 如果模块不存在且为必需模块，尝试加载
            if (!moduleInstance && required) {
                console.log(`⏳ [UnifiedRegistry] 加载必需模块: ${name}`);
                moduleInstance = await this.loadModule(name);
            }
            
            if (moduleInstance) {
                this.modules.set(name, {
                    name,
                    instance: moduleInstance,
                    priority,
                    required,
                    status: 'loaded',
                    loadTime: Date.now()
                });
                
                console.log(`✅ [UnifiedRegistry] 模块注册成功: ${name}`);
            } else if (required) {
                throw new Error(`必需模块 ${name} 加载失败`);
            }
            
        } catch (error) {
            console.error(`❌ [UnifiedRegistry] 模块注册失败: ${name}`, error);
            
            if (required) {
                throw error;
            }
        }
    }

    /**
     * 动态加载模块
     */
    async loadModule(moduleName) {
        const modulePath = `modules/${moduleName.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1)}.js`;
        
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL(modulePath);
            
            script.onload = () => {
                // 尝试获取模块实例
                const moduleInstance = window[moduleName];
                if (moduleInstance) {
                    resolve(moduleInstance);
                } else {
                    reject(new Error(`模块 ${moduleName} 加载后未找到全局实例`));
                }
            };
            
            script.onerror = () => {
                reject(new Error(`模块 ${moduleName} 脚本加载失败`));
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * 建立系统桥接
     */
    async establishSystemBridge() {
        console.log('🌉 [UnifiedRegistry] 建立系统桥接...');
        
        // 传统系统桥接
        if (this.loadedSystems.has('traditional')) {
            await this.bridgeTraditionalSystem();
        }
        
        // 模块化系统桥接
        if (this.loadedSystems.has('modular')) {
            await this.bridgeModularSystem();
        }
        
        console.log('✅ [UnifiedRegistry] 系统桥接建立完成');
    }

    /**
     * 桥接传统系统
     */
    async bridgeTraditionalSystem() {
        console.log('🔗 [UnifiedRegistry] 桥接传统系统...');
        
        const traditionalApp = window.mdacSidePanelApp;
        if (!traditionalApp) return;
        
        // 创建传统系统适配器
        const traditionalAdapter = {
            // 暴露传统系统的核心功能
            updateToMDAC: traditionalApp.updateToMDAC?.bind(traditionalApp),
            collectPersonalData: traditionalApp.collectPersonalData?.bind(traditionalApp),
            collectTravelData: traditionalApp.collectTravelData?.bind(traditionalApp),
            saveData: traditionalApp.saveData?.bind(traditionalApp),
            loadData: traditionalApp.loadData?.bind(traditionalApp),
            
            // 状态访问
            getFormData: () => traditionalApp.formData || {},
            getFieldMappings: () => traditionalApp.fieldMappings || {}
        };
        
        // 注册适配器
        this.modules.set('TraditionalSystemAdapter', {
            name: 'TraditionalSystemAdapter',
            instance: traditionalAdapter,
            priority: 0,
            required: false,
            status: 'bridged',
            loadTime: Date.now()
        });
        
        // 监听传统系统事件并转发到统一事件总线
        if (traditionalApp.addEventListener) {
            ['dataUpdated', 'formFilled', 'error'].forEach(eventType => {
                traditionalApp.addEventListener(eventType, (data) => {
                    this.emitEvent(`traditional:${eventType}`, data);
                });
            });
        }
        
        console.log('✅ [UnifiedRegistry] 传统系统桥接完成');
    }

    /**
     * 桥接模块化系统
     */
    async bridgeModularSystem() {
        console.log('🔗 [UnifiedRegistry] 桥接模块化系统...');
        
        const modularApp = window.mdacModularSidePanel;
        if (!modularApp) return;
        
        // 创建模块化系统适配器
        const modularAdapter = {
            // 暴露模块化系统的核心功能
            getModuleStatus: modularApp.getModuleStatus?.bind(modularApp),
            reloadModule: modularApp.reloadModule?.bind(modularApp),
            toggleDebugMode: modularApp.toggleDebugMode?.bind(modularApp),
            
            // 模块访问
            getModule: (moduleName) => modularApp.modules?.[moduleName],
            getAllModules: () => modularApp.modules || {}
        };
        
        // 注册适配器
        this.modules.set('ModularSystemAdapter', {
            name: 'ModularSystemAdapter',
            instance: modularAdapter,
            priority: 0,
            required: false,
            status: 'bridged',
            loadTime: Date.now()
        });
        
        // 监听模块化系统事件
        if (modularApp.eventBus) {
            ['moduleLoaded', 'moduleError', 'moduleReloaded'].forEach(eventType => {
                modularApp.eventBus.on(eventType, (data) => {
                    this.emitEvent(`modular:${eventType}`, data);
                });
            });
        }
        
        console.log('✅ [UnifiedRegistry] 模块化系统桥接完成');
    }

    /**
     * 热重启监控
     */
    startHotReloadMonitoring() {
        console.log('🔥 [UnifiedRegistry] 启动热重启监控...');
        
        // 监听关键错误事件
        this.eventBus.on('critical-error', (error) => {
            this.handleCriticalError(error);
        });
        
        // 监听模块故障事件
        this.eventBus.on('module-failure', (moduleInfo) => {
            this.handleModuleFailure(moduleInfo);
        });
        
        // 监听连接失败事件
        this.eventBus.on('connection-failure', (failureInfo) => {
            this.handleConnectionFailure(failureInfo);
        });
        
        console.log('✅ [UnifiedRegistry] 热重启监控启动完成');
    }

    /**
     * 处理关键错误
     */
    async handleCriticalError(error) {
        console.error('🚨 [UnifiedRegistry] 检测到关键错误:', error);
        
        if (this.hotReloadCount < this.maxHotReloads) {
            this.hotReloadCount++;
            console.log(`🔄 [UnifiedRegistry] 尝试热重启 (${this.hotReloadCount}/${this.maxHotReloads})`);
            
            try {
                await this.performHotReload();
                console.log('✅ [UnifiedRegistry] 热重启成功');
                this.emitEvent('hot-reload-success', { attempt: this.hotReloadCount });
            } catch (reloadError) {
                console.error('❌ [UnifiedRegistry] 热重启失败:', reloadError);
                this.emitEvent('hot-reload-failure', { 
                    attempt: this.hotReloadCount, 
                    error: reloadError 
                });
            }
        } else {
            console.error('❌ [UnifiedRegistry] 达到最大重启次数，进入降级模式');
            await this.enterDegradedMode();
        }
    }

    /**
     * 执行热重启
     */
    async performHotReload() {
        console.log('🔄 [UnifiedRegistry] 执行热重启...');
        
        // 保存当前状态
        const currentState = this.stateManager.getAllState();
        const currentQueue = [...this.messageQueue];
        
        // 重新初始化核心组件
        await this.detectExistingSystems();
        await this.registerCoreModules();
        await this.establishSystemBridge();
        
        // 恢复状态
        Object.entries(currentState).forEach(([key, value]) => {
            this.stateManager.setState(key, value);
        });
        
        // 恢复消息队列
        this.messageQueue = currentQueue;
        
        console.log('✅ [UnifiedRegistry] 热重启完成');
    }

    /**
     * 进入降级模式
     */
    async enterDegradedMode() {
        console.log('⚠️ [UnifiedRegistry] 进入降级模式');
        
        this.stateManager.setState('degradedMode', true);
        this.emitEvent('degraded-mode-entered', {
            reason: 'Maximum hot reload attempts exceeded',
            timestamp: Date.now()
        });
        
        // 只保留核心功能
        const essentialModules = ['Logger', 'ErrorRecoveryManager'];
        for (const [name, module] of this.modules) {
            if (!essentialModules.includes(name)) {
                module.status = 'disabled';
            }
        }
    }

    /**
     * 处理模块故障
     */
    async handleModuleFailure(moduleInfo) {
        console.warn('⚠️ [UnifiedRegistry] 模块故障:', moduleInfo);
        
        const moduleName = moduleInfo.name;
        const module = this.modules.get(moduleName);
        
        if (module && module.required) {
            // 尝试重新加载必需模块
            try {
                console.log(`🔄 [UnifiedRegistry] 重新加载模块: ${moduleName}`);
                const newInstance = await this.loadModule(moduleName);
                
                module.instance = newInstance;
                module.status = 'reloaded';
                module.loadTime = Date.now();
                
                console.log(`✅ [UnifiedRegistry] 模块重新加载成功: ${moduleName}`);
                this.emitEvent('module-reloaded', { name: moduleName });
                
            } catch (error) {
                console.error(`❌ [UnifiedRegistry] 模块重新加载失败: ${moduleName}`, error);
                this.emitEvent('critical-error', error);
            }
        }
    }

    /**
     * 处理连接失败
     */
    async handleConnectionFailure(failureInfo) {
        console.warn('🔌 [UnifiedRegistry] 连接失败:', failureInfo);
        
        // 将消息添加到队列
        this.messageQueue.push({
            ...failureInfo,
            timestamp: Date.now(),
            retryCount: 0
        });
        
        // 尝试重新建立连接
        setTimeout(() => {
            this.retryQueuedMessages();
        }, 2000);
    }

    /**
     * 重试队列中的消息
     */
    async retryQueuedMessages() {
        if (this.messageQueue.length === 0) return;
        
        console.log(`🔄 [UnifiedRegistry] 重试 ${this.messageQueue.length} 条队列消息`);
        
        const messagesToRetry = [...this.messageQueue];
        this.messageQueue = [];
        
        for (const message of messagesToRetry) {
            if (message.retryCount < 3) {
                message.retryCount++;
                
                try {
                    // 尝试重新发送消息
                    await this.sendMessage(message);
                    console.log('✅ [UnifiedRegistry] 队列消息重试成功');
                } catch (error) {
                    console.warn('⚠️ [UnifiedRegistry] 队列消息重试失败:', error);
                    
                    // 如果还没达到最大重试次数，重新加入队列
                    if (message.retryCount < 3) {
                        this.messageQueue.push(message);
                    }
                }
            }
        }
    }

    /**
     * 发送消息
     */
    async sendMessage(message) {
        // 实现具体的消息发送逻辑
        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(message.tabId, message.content, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
    }

    /**
     * 处理初始化失败
     */
    async handleInitializationFailure(error) {
        console.error('🚨 [UnifiedRegistry] 初始化失败，尝试恢复:', error);
        
        // 尝试降级初始化
        try {
            console.log('⚠️ [UnifiedRegistry] 尝试降级初始化...');
            
            // 只初始化基本功能
            await this.initializeEventBus();
            await this.initializeStateManager();
            
            // 设置降级模式
            this.stateManager.setState('degradedMode', true);
            this.stateManager.setState('initializationError', error.message);
            
            console.log('✅ [UnifiedRegistry] 降级初始化完成');
            
        } catch (degradedError) {
            console.error('❌ [UnifiedRegistry] 降级初始化也失败:', degradedError);
            
            // 最后的备用方案
            window.mdacUnifiedRegistryError = {
                originalError: error,
                degradedError: degradedError,
                timestamp: Date.now()
            };
        }
    }

    /**
     * 发送事件
     */
    emitEvent(eventName, data) {
        if (this.eventBus) {
            this.eventBus.emit(eventName, data);
        }
    }

    /**
     * 获取模块
     */
    getModule(name) {
        const module = this.modules.get(name);
        return module ? module.instance : null;
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            loadedSystems: Array.from(this.loadedSystems),
            moduleCount: this.modules.size,
            hotReloadCount: this.hotReloadCount,
            queuedMessages: this.messageQueue.length,
            degradedMode: this.stateManager?.getState('degradedMode', false),
            modules: Array.from(this.modules.entries()).map(([name, module]) => ({
                name,
                status: module.status,
                required: module.required,
                loadTime: module.loadTime
            }))
        };
    }
}

// 全局导出
window.UnifiedModuleRegistry = UnifiedModuleRegistry;
console.log('📦 [UnifiedRegistry] 统一模块注册表类已定义');
