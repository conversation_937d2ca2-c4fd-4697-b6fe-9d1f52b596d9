/**
 * MDAC填充监控器模块 - 兼容性存根
 * 提供表单填充过程监控和验证功能的后备实现
 * 
 * 功能特性：
 * - 实时填充状态监控
 * - 填充进度跟踪
 * - 字段验证和提示
 * - 全局实例自动创建
 * - 向后兼容现有代码
 * 
 * 创建时间: 2025-01-12
 * 作者: MDAC AI智能分析工具
 */

(function() {
    'use strict';
    
    /**
     * MDAC填充监控器类
     * 提供表单填充过程的全面监控和管理
     */
    class FillMonitor {
        constructor() {
            this.isInitialized = false;
            this.isMonitoring = false;
            this.fillSession = null;
            this.monitoredFields = new Map();
            this.fillHistory = [];
            this.validationRules = new Map();
            this.logger = window.mdacLogger || console;
            this.observers = [];
            
            this.initialize();
        }
        
        /**
         * 初始化填充监控器
         */
        async initialize() {
            try {
                this.setupValidationRules();
                this.setupFieldObservers();
                
                this.isInitialized = true;
                this.log('info', 'FillMonitor', '填充监控器初始化成功');
            } catch (error) {
                this.log('error', 'FillMonitor', '初始化失败', error);
            }
        }
        
        /**
         * 设置验证规则
         */
        setupValidationRules() {
            // 护照号码验证
            this.validationRules.set('passportNo', {
                pattern: /^[A-Z0-9]{6,12}$/,
                message: '护照号码应为6-12位字母数字组合',
                required: true
            });
            
            // 姓名验证
            this.validationRules.set('name', {
                pattern: /^[a-zA-Z\s]{2,50}$/,
                message: '姓名应为2-50位英文字母',
                required: true
            });
            
            // 邮箱验证
            this.validationRules.set('email', {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: '请输入有效的邮箱地址',
                required: false
            });
            
            // 电话号码验证
            this.validationRules.set('phone', {
                pattern: /^[\+]?[0-9\-\s\(\)]{7,20}$/,
                message: '请输入有效的电话号码',
                required: false
            });
            
            // 日期验证
            this.validationRules.set('date', {
                pattern: /^\d{4}-\d{2}-\d{2}$/,
                message: '日期格式应为YYYY-MM-DD',
                required: false,
                customValidator: (value) => {
                    const date = new Date(value);
                    return !isNaN(date.getTime()) && date.getFullYear() > 1900;
                }
            });
            
            // 航班号验证
            this.validationRules.set('flightNo', {
                pattern: /^[A-Z]{2,3}[0-9]{1,4}$/,
                message: '航班号格式不正确',
                required: false
            });
        }
        
        /**
         * 设置字段观察器
         */
        setupFieldObservers() {
            // 监听DOM变化
            const mutationObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                this.scanForNewFields(node);
                            }
                        });
                    }
                });
            });
            
            mutationObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            this.observers.push(mutationObserver);
        }
        
        /**
         * 扫描新字段
         * @param {Element} container - 容器元素
         */
        scanForNewFields(container) {
            const inputs = container.querySelectorAll ? 
                container.querySelectorAll('input, select, textarea') : 
                (container.matches && container.matches('input, select, textarea') ? [container] : []);
            
            inputs.forEach(input => {
                if (!this.monitoredFields.has(input)) {
                    this.monitorField(input);
                }
            });
        }
        
        /**
         * 开始监控填充过程
         * @param {Object} options - 监控选项
         */
        startMonitoring(options = {}) {
            if (this.isMonitoring) {
                this.log('warn', 'FillMonitor', '监控已在进行中');
                return;
            }
            
            this.isMonitoring = true;
            this.fillSession = {
                id: this.generateSessionId(),
                startTime: Date.now(),
                options: options,
                totalFields: 0,
                filledFields: 0,
                validFields: 0,
                errors: [],
                warnings: []
            };
            
            // 扫描现有字段
            this.scanExistingFields();
            
            this.log('info', 'FillMonitor', '开始监控填充过程', this.fillSession);
            
            // 发送监控开始事件
            this.emitEvent('monitor-started', this.fillSession);
        }
        
        /**
         * 停止监控
         */
        stopMonitoring() {
            if (!this.isMonitoring) {
                return;
            }
            
            this.isMonitoring = false;
            
            if (this.fillSession) {
                this.fillSession.endTime = Date.now();
                this.fillSession.duration = this.fillSession.endTime - this.fillSession.startTime;
                
                // 保存到历史记录
                this.fillHistory.push({ ...this.fillSession });
                
                this.log('info', 'FillMonitor', '监控已停止', this.fillSession);
                
                // 发送监控结束事件
                this.emitEvent('monitor-stopped', this.fillSession);
                
                this.fillSession = null;
            }
        }
        
        /**
         * 扫描现有字段
         */
        scanExistingFields() {
            const allInputs = document.querySelectorAll('input, select, textarea');
            allInputs.forEach(input => this.monitorField(input));
            
            if (this.fillSession) {
                this.fillSession.totalFields = this.monitoredFields.size;
            }
        }
        
        /**
         * 监控单个字段
         * @param {Element} field - 字段元素
         */
        monitorField(field) {
            if (this.monitoredFields.has(field)) {
                return; // 已经在监控中
            }
            
            const fieldInfo = {
                element: field,
                id: field.id || field.name || `field_${Date.now()}`,
                type: this.identifyFieldType(field),
                initialValue: field.value,
                currentValue: field.value,
                isFilled: false,
                isValid: null,
                lastModified: null,
                validationErrors: []
            };
            
            this.monitoredFields.set(field, fieldInfo);
            
            // 添加事件监听器
            field.addEventListener('input', (event) => this.handleFieldInput(event, fieldInfo));
            field.addEventListener('change', (event) => this.handleFieldChange(event, fieldInfo));
            field.addEventListener('blur', (event) => this.handleFieldBlur(event, fieldInfo));
            
            this.log('debug', 'FillMonitor', `开始监控字段: ${fieldInfo.id}`);
        }
        
        /**
         * 处理字段输入事件
         * @param {Event} event - 输入事件
         * @param {Object} fieldInfo - 字段信息
         */
        handleFieldInput(event, fieldInfo) {
            const newValue = event.target.value;
            const oldValue = fieldInfo.currentValue;
            
            fieldInfo.currentValue = newValue;
            fieldInfo.lastModified = Date.now();
            fieldInfo.isFilled = newValue.trim().length > 0;
            
            // 实时验证
            this.validateField(fieldInfo);
            
            // 更新会话统计
            this.updateSessionStats();
            
            // 发送字段更新事件
            this.emitEvent('field-updated', {
                fieldId: fieldInfo.id,
                oldValue: oldValue,
                newValue: newValue,
                isFilled: fieldInfo.isFilled,
                isValid: fieldInfo.isValid
            });
            
            this.log('debug', 'FillMonitor', `字段 ${fieldInfo.id} 已更新: ${newValue}`);
        }
        
        /**
         * 处理字段变化事件
         * @param {Event} event - 变化事件
         * @param {Object} fieldInfo - 字段信息
         */
        handleFieldChange(event, fieldInfo) {
            // 完整验证
            this.validateField(fieldInfo, true);
            
            // 发送字段完成事件
            this.emitEvent('field-completed', {
                fieldId: fieldInfo.id,
                value: fieldInfo.currentValue,
                isValid: fieldInfo.isValid,
                errors: fieldInfo.validationErrors
            });
        }
        
        /**
         * 处理字段失焦事件
         * @param {Event} event - 失焦事件
         * @param {Object} fieldInfo - 字段信息
         */
        handleFieldBlur(event, fieldInfo) {
            // 最终验证
            this.validateField(fieldInfo, true);
            
            // 显示验证结果
            this.showFieldValidationResult(fieldInfo);
        }
        
        /**
         * 识别字段类型
         * @param {Element} field - 字段元素
         * @returns {string} 字段类型
         */
        identifyFieldType(field) {
            const name = (field.name || '').toLowerCase();
            const id = (field.id || '').toLowerCase();
            const type = field.type || 'text';
            
            // 根据名称和ID判断
            if (name.includes('passport') || id.includes('passport')) return 'passportNo';
            if (name.includes('name') || id.includes('name')) return 'name';
            if (name.includes('email') || id.includes('email') || type === 'email') return 'email';
            if (name.includes('phone') || id.includes('phone') || type === 'tel') return 'phone';
            if (name.includes('date') || id.includes('date') || type === 'date') return 'date';
            if (name.includes('flight') || id.includes('flight')) return 'flightNo';
            
            return 'text';
        }
        
        /**
         * 验证字段
         * @param {Object} fieldInfo - 字段信息
         * @param {boolean} fullValidation - 是否进行完整验证
         */
        validateField(fieldInfo, fullValidation = false) {
            const value = fieldInfo.currentValue;
            const rule = this.validationRules.get(fieldInfo.type);
            
            fieldInfo.validationErrors = [];
            fieldInfo.isValid = true;
            
            if (!rule) {
                // 没有验证规则，认为有效
                return;
            }
            
            // 必填验证
            if (rule.required && (!value || value.trim().length === 0)) {
                fieldInfo.validationErrors.push('此字段为必填项');
                fieldInfo.isValid = false;
            }
            
            // 如果有值，进行格式验证
            if (value && value.trim().length > 0) {
                // 正则表达式验证
                if (rule.pattern && !rule.pattern.test(value)) {
                    fieldInfo.validationErrors.push(rule.message);
                    fieldInfo.isValid = false;
                }
                
                // 自定义验证器
                if (rule.customValidator && !rule.customValidator(value)) {
                    fieldInfo.validationErrors.push(rule.message);
                    fieldInfo.isValid = false;
                }
            }
            
            // 只在完整验证时显示错误
            if (fullValidation && fieldInfo.validationErrors.length > 0) {
                this.log('warn', 'FillMonitor', `字段 ${fieldInfo.id} 验证失败`, fieldInfo.validationErrors);
            }
        }
        
        /**
         * 显示字段验证结果
         * @param {Object} fieldInfo - 字段信息
         */
        showFieldValidationResult(fieldInfo) {
            const element = fieldInfo.element;
            
            // 移除之前的验证提示
            this.removeValidationMessages(element);
            
            if (fieldInfo.validationErrors.length > 0) {
                // 添加错误样式
                element.style.borderColor = '#f44336';
                element.style.backgroundColor = '#ffebee';
                
                // 创建错误提示
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mdac-validation-error';
                errorDiv.style.cssText = `
                    color: #f44336;
                    font-size: 12px;
                    margin-top: 4px;
                    padding: 4px;
                    background: #ffebee;
                    border-radius: 3px;
                    border: 1px solid #f44336;
                `;
                errorDiv.textContent = fieldInfo.validationErrors.join(', ');
                
                element.parentNode.insertBefore(errorDiv, element.nextSibling);
            } else if (fieldInfo.isFilled) {
                // 添加成功样式
                element.style.borderColor = '#4caf50';
                element.style.backgroundColor = '#e8f5e8';
            }
        }
        
        /**
         * 移除验证消息
         * @param {Element} element - 字段元素
         */
        removeValidationMessages(element) {
            // 重置样式
            element.style.borderColor = '';
            element.style.backgroundColor = '';
            
            // 移除错误提示
            const errorDiv = element.parentNode.querySelector('.mdac-validation-error');
            if (errorDiv) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }
        
        /**
         * 更新会话统计
         */
        updateSessionStats() {
            if (!this.fillSession) return;
            
            let filledCount = 0;
            let validCount = 0;
            
            this.monitoredFields.forEach(fieldInfo => {
                if (fieldInfo.isFilled) {
                    filledCount++;
                    if (fieldInfo.isValid) {
                        validCount++;
                    }
                }
            });
            
            this.fillSession.filledFields = filledCount;
            this.fillSession.validFields = validCount;
            
            // 发送进度更新事件
            this.emitEvent('progress-updated', {
                total: this.fillSession.totalFields,
                filled: filledCount,
                valid: validCount,
                progress: this.fillSession.totalFields > 0 ? 
                    Math.round((filledCount / this.fillSession.totalFields) * 100) : 0
            });
        }
        
        /**
         * 获取监控状态
         * @returns {Object} 监控状态
         */
        getMonitoringStatus() {
            return {
                isMonitoring: this.isMonitoring,
                currentSession: this.fillSession,
                monitoredFieldsCount: this.monitoredFields.size,
                fillHistory: this.fillHistory.slice(-10) // 最近10条记录
            };
        }
        
        /**
         * 获取字段状态
         * @returns {Array} 字段状态数组
         */
        getFieldsStatus() {
            const status = [];
            
            this.monitoredFields.forEach((fieldInfo, element) => {
                status.push({
                    id: fieldInfo.id,
                    type: fieldInfo.type,
                    isFilled: fieldInfo.isFilled,
                    isValid: fieldInfo.isValid,
                    value: fieldInfo.currentValue,
                    errors: fieldInfo.validationErrors.slice()
                });
            });
            
            return status;
        }
        
        /**
         * 清除所有监控
         */
        clearMonitoring() {
            // 停止监控
            this.stopMonitoring();
            
            // 移除所有验证消息
            this.monitoredFields.forEach((fieldInfo, element) => {
                this.removeValidationMessages(element);
            });
            
            // 清空监控字段
            this.monitoredFields.clear();
            
            this.log('info', 'FillMonitor', '已清除所有监控');
        }
        
        /**
         * 生成会话ID
         * @returns {string} 会话ID
         */
        generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        /**
         * 发送事件
         * @param {string} eventType - 事件类型
         * @param {*} data - 事件数据
         */
        emitEvent(eventType, data) {
            // 发送Chrome扩展消息
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    action: 'fill-monitor-event',
                    eventType: eventType,
                    data: data
                }).catch(error => {
                    this.log('warn', 'FillMonitor', '发送事件消息失败', error);
                });
            }
            
            // 发送DOM事件
            const event = new CustomEvent(`mdac:fill-${eventType}`, {
                detail: data
            });
            document.dispatchEvent(event);
        }
        
        /**
         * 销毁监控器
         */
        destroy() {
            this.clearMonitoring();
            
            // 移除观察器
            this.observers.forEach(observer => {
                observer.disconnect();
            });
            this.observers = [];
            
            this.log('info', 'FillMonitor', '监控器已销毁');
        }
        
        /**
         * 日志记录方法
         * @param {string} level - 日志级别
         * @param {string} module - 模块名
         * @param {string} message - 消息
         * @param {*} data - 数据
         */
        log(level, module, message, data = null) {
            if (this.logger && typeof this.logger.log === 'function') {
                this.logger.log(level, module, message, data);
            } else {
                console[level](`[${module}] ${message}`, data);
            }
        }
    }
    
    // 检查是否已存在FillMonitor，避免重复定义
    if (typeof window.FillMonitor === 'undefined') {
        // 将FillMonitor类挂载到全局window对象
        window.FillMonitor = FillMonitor;
        
        // 创建全局实例供其他模块使用
        if (typeof window.mdacFillMonitor === 'undefined') {
            window.mdacFillMonitor = new FillMonitor();
            console.log('✅ [FillMonitor] 全局实例创建成功');
        }
    } else {
        console.log('✅ [FillMonitor] 类已存在，跳过重复定义');
    }
    
})();
