<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI智能助手 - 侧边栏</title>
    <link rel="stylesheet" href="ui-sidepanel.css">
    <!-- 性能优化工具 -->
    <script src="../utils/performance-utils.js"></script>
    <!-- UI增强工具 -->
    <script src="../utils/ui-enhancements.js"></script>
</head>
<body class="sidepanel-body">
    <div class="sidepanel-container">
        <!-- 连接状态指示器 -->
        <div class="connection-status" id="connectionStatus">
            🟡 检测MDAC网站连接...
        </div>

        <!-- MDAC快速访问区域 -->
        <div class="mdac-quick-access">
            <button class="mdac-access-btn" id="mdacAccessBtn" title="在新标签页中打开MDAC网站">
                <div class="btn-content">
                    <span class="btn-icon">🌐</span>
                    <div class="btn-text-group">
                        <span class="btn-title">打开MDAC网站</span>
                        <span class="btn-subtitle">前往马来西亚入境卡表单</span>
                    </div>
                    <span class="btn-arrow">→</span>
                </div>
            </button>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="logo">
                <img src="../assets/icons/icon32.png" alt="MDAC AI">
                <h1>MDAC AI助手</h1>
            </div>
            <div class="ai-status" id="aiStatus">
                <span class="status-dot"></span>
                <span class="status-text">AI就绪</span>
            </div>
        </div>

        <!-- 网站检测区域 -->
        <div class="site-detection" id="siteDetection">
            <div class="detection-status" id="detectionStatus">
                <span class="icon">🔍</span>
                <span class="text">检测MDAC网站...</span>
            </div>
        </div>

        <!-- 简洁2x2网格布局 -->
        <div class="grid-layout">
            <!-- 顶部区域：图片识别工具栏 -->
            <div class="card-base top-section">
                <div class="card-padding toolbar-area">
                    <div class="flex items-center gap-sm toolbar-header">
                        <span class="toolbar-icon">🛠️</span>
                        <span class="toolbar-title">AI智能工具</span>
                    </div>
                    <div class="flex flex-wrap gap-sm toolbar-actions">
                        <!-- 图片识别按钮 -->
                        <button class="btn-base btn-gradient image-upload-btn" id="imageUploadBtn">
                            <span class="btn-icon">📷</span>
                            <span class="btn-text">上传图片</span>
                        </button>
                        <!-- 隐藏的文件输入 -->
                        <input type="file" id="imageInput" accept="image/*" class="hidden">

                        <!-- 其他工具按钮 -->
                        <button class="btn-base tool-btn" id="clearAllBtn">
                            <span class="btn-icon">🗑️</span>
                            <span class="btn-text">清除全部</span>
                        </button>
                        <button class="btn-base tool-btn" id="previewBtn">
                            <span class="btn-icon">👁️</span>
                            <span class="btn-text">预览数据</span>
                        </button>
                    </div>
                </div>

            </div>

            <!-- 中部区域：字段映射显示区 -->
            <div class="card-base flex-1 middle-section">
                <div class="grid flex-1 fields-grid">
                    <!-- 左列：个人信息 -->
                    <div class="card-padding personal-info-column">
                        <div class="flex items-center gap-sm column-header">
                            <span class="column-icon">👤</span>
                            <span class="column-title">Personal Information</span>
                        </div>

                        <!-- 个人信息智能解析区域 -->
                        <div class="card-base card-padding-small parse-input-section">
                            <div class="flex items-center justify-between parse-input-header">
                                <div class="flex items-center gap-xs">
                                    <span class="parse-icon">🧠</span>
                                    <span class="parse-title">AI解析个人信息</span>
                                </div>
                                <!-- 自动解析设置 -->
                                <div class="auto-parse-settings">
                                    <label class="auto-parse-toggle">
                                        <input type="checkbox" id="autoParsePersonalEnabled" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-label">自动解析</span>
                                    </label>
                                </div>
                            </div>
                            <textarea
                                id="personalInfoInput"
                                class="input-base input-textarea parse-textarea"
                                placeholder="粘贴护照信息、身份证信息或直接输入个人资料..."
                                rows="3">
                            </textarea>
                            <!-- 自动解析状态指示器 -->
                            <div class="auto-parse-status hidden" id="autoParsePersonalStatus">
                                <div class="countdown-indicator">
                                    <span class="countdown-icon">⏱️</span>
                                    <span class="countdown-text" id="personalCountdownText">3秒后自动解析...</span>
                                    <button class="cancel-auto-parse" id="cancelAutoParsePersonal">取消</button>
                                </div>
                            </div>
                            <div class="flex gap-sm parse-buttons">
                                <button class="btn-base btn-primary btn-small btn-full-width parse-btn" id="parsePersonalBtn">
                                    <span class="btn-icon">🚀</span>
                                    <span class="btn-text">手动解析</span>
                                </button>
                            </div>
                        </div>

                        <!-- 邮箱和电话预设区域 -->
                        <div class="card-base card-padding-small preset-section">
                            <div class="flex items-center justify-between preset-header">
                                <div class="flex items-center gap-xs">
                                    <span class="preset-icon">📧</span>
                                    <span class="preset-title">常用信息</span>
                                </div>
                                <button class="preset-edit-btn" id="editPresetBtn">✏️</button>
                            </div>
                            <div class="flex-col gap-sm preset-fields">
                                <div class="flex-col preset-field">
                                    <label>邮箱</label>
                                    <input type="email" id="presetEmail" class="input-base input-small preset-input" placeholder="<EMAIL>">
                                </div>
                                <div class="flex-col preset-field">
                                    <label>电话</label>
                                    <input type="tel" id="presetPhone" class="input-base input-small preset-input" placeholder="+60123456789">
                                </div>
                            </div>
                        </div>

                        <!-- 个人信息字段 -->
                        <div class="flex-col gap-sm field-group" id="personalFields">
                            <div class="flex-col field-item">
                                <label>姓名</label>
                                <input type="text" id="name" class="input-base field-input" placeholder="Full Name">
                                <div class="field-status" data-field="name"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>护照号码</label>
                                <input type="text" id="passportNo" class="input-base field-input" placeholder="A12345678">
                                <div class="field-status" data-field="passportNo"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>出生日期</label>
                                <input type="date" id="dateOfBirth" class="input-base field-input">
                                <div class="field-status" data-field="dateOfBirth"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>国籍</label>
                                <select id="nationality" class="input-base field-input">
                                    <option value="">选择国籍</option>
                                    <option value="CHN">中国</option>
                                    <option value="USA">美国</option>
                                    <option value="SGP">新加坡</option>
                                    <option value="MYS">马来西亚</option>
                                </select>
                                <div class="field-status" data-field="nationality"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>性别</label>
                                <select id="sex" class="input-base field-input">
                                    <option value="">选择性别</option>
                                    <option value="1">男</option>
                                    <option value="2">女</option>
                                </select>
                                <div class="field-status" data-field="sex"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>护照到期日</label>
                                <input type="date" id="passportExpiry" class="input-base field-input">
                                <div class="field-status" data-field="passportExpiry"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 右列：旅行信息 -->
                    <div class="card-padding travel-info-column">
                        <div class="flex items-center gap-sm column-header">
                            <span class="column-icon">✈️</span>
                            <span class="column-title">Traveling Information</span>
                        </div>

                        <!-- 旅行信息智能解析区域 -->
                        <div class="card-base card-padding-small parse-input-section">
                            <div class="flex items-center justify-between parse-input-header">
                                <div class="flex items-center gap-xs">
                                    <span class="parse-icon">🧠</span>
                                    <span class="parse-title">AI解析旅行信息</span>
                                </div>
                                <!-- 自动解析设置 -->
                                <div class="auto-parse-settings">
                                    <label class="auto-parse-toggle">
                                        <input type="checkbox" id="autoParseTravel_Enabled" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-label">自动解析</span>
                                    </label>
                                </div>
                            </div>
                            <textarea
                                id="travelInfoInput"
                                class="input-base input-textarea parse-textarea"
                                placeholder="粘贴机票信息、酒店预订或直接输入旅行计划..."
                                rows="3">
                            </textarea>
                            <!-- 自动解析状态指示器 -->
                            <div class="auto-parse-status hidden" id="autoParseTravel_Status">
                                <div class="countdown-indicator">
                                    <span class="countdown-icon">⏱️</span>
                                    <span class="countdown-text" id="travelCountdownText">3秒后自动解析...</span>
                                    <button class="cancel-auto-parse" id="cancelAutoParseTravel">取消</button>
                                </div>
                            </div>
                            <div class="flex gap-sm parse-buttons">
                                <button class="btn-base btn-primary btn-small parse-btn" id="parseTravelBtn">
                                    <span class="btn-icon">🚀</span>
                                    <span class="btn-text">手动解析</span>
                                </button>
                                <button class="btn-base btn-primary btn-small parse-btn" id="parseAddressBtn">
                                    <span class="btn-icon">📍</span>
                                    <span class="btn-text">智能地址解析</span>
                                </button>
                            </div>
                        </div>

                        <div class="flex-col gap-sm field-group" id="travelFields">
                            <div class="flex-col field-item">
                                <label>到达日期</label>
                                <input type="date" id="arrivalDate" class="input-base field-input">
                                <div class="field-status" data-field="arrivalDate"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>离开日期</label>
                                <input type="date" id="departureDate" class="input-base field-input">
                                <div class="field-status" data-field="departureDate"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>航班号</label>
                                <input type="text" id="flightNo" class="input-base field-input" placeholder="MH123">
                                <div class="field-status" data-field="flightNo"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>交通方式</label>
                                <select id="modeOfTravel" class="input-base field-input">
                                    <option value="">选择交通方式</option>
                                    <option value="1">飞机</option>
                                    <option value="2">汽车</option>
                                    <option value="3">船舶</option>
                                    <option value="4">火车</option>
                                    <option value="5">其他</option>
                                </select>
                                <div class="field-status" data-field="modeOfTravel"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>住宿类型</label>
                                <select id="accommodation" class="input-base field-input">
                                    <option value="">选择住宿类型</option>
                                    <option value="01">酒店</option>
                                    <option value="02">民宿</option>
                                    <option value="03">朋友家</option>
                                    <option value="04">其他</option>
                                </select>
                                <div class="field-status" data-field="accommodation"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>住宿地址</label>
                                <textarea id="address" class="input-base input-textarea field-input address-input" placeholder="详细地址" rows="2"></textarea>
                                <div class="field-status" data-field="address"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>州属</label>
                                <select id="state" class="input-base field-input">
                                    <option value="">选择州属</option>
                                </select>
                                <div class="field-status" data-field="state"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>城市</label>
                                <select id="city" class="input-base field-input">
                                    <option value="">选择城市</option>
                                </select>
                                <div class="field-status" data-field="city"></div>
                            </div>
                            <div class="flex-col field-item">
                                <label>邮政编码</label>
                                <input type="text" id="postcode" class="input-base field-input" placeholder="5位数字邮编" maxlength="5" pattern="[0-9]{5}">
                                <div class="field-status" data-field="postcode"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部区域：操作按钮区 -->
            <div class="card-base card-padding bottom-section">
                <div class="flex-col gap-md action-buttons">
                    <!-- 主要按钮 -->
                    <button class="btn-base btn-gradient btn-large btn-full-width primary-action-btn" id="updateToMDACBtn">
                        <span class="btn-icon">🚀</span>
                        <span class="btn-text">更新到MDAC页面</span>
                    </button>

                    <!-- 辅助按钮 -->
                    <div class="grid gap-sm secondary-actions">
                        <button class="btn-base flex-col items-center gap-xs secondary-btn" id="previewBtn">
                            <span class="btn-icon">👁️</span>
                            <span class="btn-text">预览</span>
                        </button>
                        <button class="btn-base flex-col items-center gap-xs secondary-btn" id="saveBtn">
                            <span class="btn-icon">💾</span>
                            <span class="btn-text">保存</span>
                        </button>
                        <button class="btn-base flex-col items-center gap-xs secondary-btn" id="clearBtn">
                            <span class="btn-icon">🗑️</span>
                            <span class="btn-text">清除</span>
                        </button>
                    </div>
                </div>

                <!-- 状态指示器 -->
                <div class="status-indicators">
                    <div class="status-item" id="confidenceStatus">
                        <span class="status-icon">🎯</span>
                        <span class="status-text">置信度: --</span>
                    </div>
                    <div class="status-item" id="completenessStatus">
                        <span class="status-icon">📊</span>
                        <span class="status-text">完整度: --</span>
                    </div>
                    <div class="status-item" id="fieldsStatus">
                        <span class="status-icon">📝</span>
                        <span class="status-text">字段: 0/12</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 隐藏的解析状态和结果区域 -->
        <div class="parsing-status hidden" id="parsingStatus">
            <div class="status-indicator">
                <div class="loading-spinner"></div>
                <span class="status-text">AI正在解析内容...</span>
            </div>
        </div>

        <div class="parse-results hidden" id="parseResults">
            <!-- 解析结果将动态插入这里 -->
        </div>

        <!-- 简化的footer -->
        <div class="footer">
            <div class="footer-info">
                <span class="version">MDAC AI v2.0</span>
                <span class="status">智能增强版</span>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn secondary" id="modalCancel">取消</button>
                <button class="btn primary" id="modalConfirm">确认</button>
            </div>
        </div>
    </div>

    <!-- AI配置文件 -->
    <script src="../config/ai-config.js"></script>
    <script src="../config/enhanced-ai-config.js"></script>
    
    <!-- 核心模块 -->
    <script src="../modules/logger.js"></script>
    <script src="../modules/address-resolver.js"></script>
    
    <!-- Stage 2: 统一架构启动器 (优先加载) -->
    <script src="unified-architecture-bootstrap.js"></script>
    
    <!-- MDAC AI助手主控制器 (由启动器协调) -->
    <script src="ui-sidepanel-main.js"></script>
</body>
</html>
