/**
 * UI增强工具
 * 提供加载状态、消息提示、进度指示等用户体验优化功能
 * 
 * <AUTHOR> AI Team
 * @version 3.1.0
 */

/**
 * 加载状态管理器
 */
class LoadingManager {
    constructor() {
        this.activeLoaders = new Map();
        this.defaultOptions = {
            text: '加载中...',
            spinner: true,
            overlay: true,
            timeout: 30000 // 30秒超时
        };
    }

    /**
     * 显示加载状态
     * @param {string} id - 加载器ID
     * @param {Object} options - 配置选项
     * @returns {Object} 加载器控制对象
     */
    show(id, options = {}) {
        try {
            const config = { ...this.defaultOptions, ...options };
            
            // 如果已存在，先移除
            if (this.activeLoaders.has(id)) {
                this.hide(id);
            }

            const loader = this.createLoader(id, config);
            this.activeLoaders.set(id, loader);

            // 设置超时
            if (config.timeout > 0) {
                loader.timeoutId = setTimeout(() => {
                    this.hide(id);
                    console.warn(`⚠️ 加载器 ${id} 超时`);
                }, config.timeout);
            }

            return {
                hide: () => this.hide(id),
                updateText: (text) => this.updateText(id, text),
                updateProgress: (progress) => this.updateProgress(id, progress)
            };
        } catch (error) {
            console.error('❌ 显示加载状态失败:', error);
            return { hide: () => {}, updateText: () => {}, updateProgress: () => {} };
        }
    }

    /**
     * 隐藏加载状态
     * @param {string} id - 加载器ID
     */
    hide(id) {
        try {
            const loader = this.activeLoaders.get(id);
            if (loader) {
                if (loader.timeoutId) {
                    clearTimeout(loader.timeoutId);
                }
                if (loader.element && loader.element.parentNode) {
                    loader.element.parentNode.removeChild(loader.element);
                }
                this.activeLoaders.delete(id);
            }
        } catch (error) {
            console.error('❌ 隐藏加载状态失败:', error);
        }
    }

    /**
     * 创建加载器元素
     * @param {string} id - 加载器ID
     * @param {Object} config - 配置
     * @returns {Object} 加载器对象
     */
    createLoader(id, config) {
        const container = document.createElement('div');
        container.id = `loader-${id}`;
        container.className = 'mdac-loader';
        container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        const content = document.createElement('div');
        content.className = 'mdac-loader-content';
        content.style.cssText = `
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            min-width: 200px;
        `;

        if (config.spinner) {
            const spinner = this.createSpinner();
            content.appendChild(spinner);
        }

        const text = document.createElement('div');
        text.className = 'mdac-loader-text';
        text.textContent = config.text;
        text.style.cssText = `
            color: #333;
            font-size: 14px;
            text-align: center;
        `;
        content.appendChild(text);

        // 进度条（可选）
        if (config.progress) {
            const progressBar = this.createProgressBar();
            content.appendChild(progressBar);
        }

        container.appendChild(content);
        document.body.appendChild(container);

        return {
            element: container,
            textElement: text,
            progressElement: content.querySelector('.mdac-progress-bar')
        };
    }

    /**
     * 创建旋转器
     * @returns {HTMLElement} 旋转器元素
     */
    createSpinner() {
        const spinner = document.createElement('div');
        spinner.className = 'mdac-spinner';
        spinner.style.cssText = `
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2563eb;
            border-radius: 50%;
            animation: mdac-spin 1s linear infinite;
        `;

        // 添加动画样式
        if (!document.getElementById('mdac-spinner-styles')) {
            const style = document.createElement('style');
            style.id = 'mdac-spinner-styles';
            style.textContent = `
                @keyframes mdac-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        return spinner;
    }

    /**
     * 创建进度条
     * @returns {HTMLElement} 进度条元素
     */
    createProgressBar() {
        const container = document.createElement('div');
        container.style.cssText = `
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        `;

        const bar = document.createElement('div');
        bar.className = 'mdac-progress-bar';
        bar.style.cssText = `
            height: 100%;
            background: #2563eb;
            width: 0%;
            transition: width 0.3s ease;
        `;

        container.appendChild(bar);
        return container;
    }

    /**
     * 更新文本
     * @param {string} id - 加载器ID
     * @param {string} text - 新文本
     */
    updateText(id, text) {
        const loader = this.activeLoaders.get(id);
        if (loader && loader.textElement) {
            loader.textElement.textContent = text;
        }
    }

    /**
     * 更新进度
     * @param {string} id - 加载器ID
     * @param {number} progress - 进度百分比 (0-100)
     */
    updateProgress(id, progress) {
        const loader = this.activeLoaders.get(id);
        if (loader && loader.progressElement) {
            loader.progressElement.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }
    }

    /**
     * 清理所有加载器
     */
    cleanup() {
        this.activeLoaders.forEach((loader, id) => {
            this.hide(id);
        });
    }
}

/**
 * 增强的消息提示系统
 */
class MessageSystem {
    constructor() {
        this.container = null;
        this.messages = new Map();
        this.defaultDuration = 3000;
        this.maxMessages = 5;
    }

    /**
     * 显示消息
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {string} message - 消息内容
     * @param {Object} options - 配置选项
     * @returns {string} 消息ID
     */
    show(type, message, options = {}) {
        try {
            const config = {
                duration: this.defaultDuration,
                closable: true,
                persistent: false,
                ...options
            };

            const messageId = this.generateMessageId();
            const messageElement = this.createMessage(messageId, type, message, config);
            
            this.ensureContainer();
            this.container.appendChild(messageElement);
            
            this.messages.set(messageId, {
                element: messageElement,
                config: config,
                timestamp: Date.now()
            });

            // 限制消息数量
            this.limitMessages();

            // 自动移除（如果不是持久消息）
            if (!config.persistent && config.duration > 0) {
                setTimeout(() => {
                    this.hide(messageId);
                }, config.duration);
            }

            // 添加入场动画
            requestAnimationFrame(() => {
                messageElement.style.transform = 'translateX(0)';
                messageElement.style.opacity = '1';
            });

            return messageId;
        } catch (error) {
            console.error('❌ 显示消息失败:', error);
            return null;
        }
    }

    /**
     * 隐藏消息
     * @param {string} messageId - 消息ID
     */
    hide(messageId) {
        try {
            const messageData = this.messages.get(messageId);
            if (messageData) {
                const element = messageData.element;
                
                // 添加退场动画
                element.style.transform = 'translateX(100%)';
                element.style.opacity = '0';
                
                setTimeout(() => {
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                    this.messages.delete(messageId);
                }, 300);
            }
        } catch (error) {
            console.error('❌ 隐藏消息失败:', error);
        }
    }

    /**
     * 创建消息元素
     * @param {string} id - 消息ID
     * @param {string} type - 消息类型
     * @param {string} message - 消息内容
     * @param {Object} config - 配置
     * @returns {HTMLElement} 消息元素
     */
    createMessage(id, type, message, config) {
        const element = document.createElement('div');
        element.id = `message-${id}`;
        element.className = `mdac-message mdac-message-${type}`;
        
        const colors = {
            success: { bg: '#10b981', border: '#059669' },
            error: { bg: '#ef4444', border: '#dc2626' },
            warning: { bg: '#f59e0b', border: '#d97706' },
            info: { bg: '#3b82f6', border: '#2563eb' }
        };

        const color = colors[type] || colors.info;
        
        element.style.cssText = `
            background: ${color.bg};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            border-left: 4px solid ${color.border};
            margin-bottom: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        `;

        const content = document.createElement('div');
        content.textContent = message;
        content.style.flex = '1';
        element.appendChild(content);

        if (config.closable) {
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                margin-left: 12px;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0.8;
                transition: opacity 0.2s ease;
            `;
            closeBtn.onmouseover = () => closeBtn.style.opacity = '1';
            closeBtn.onmouseout = () => closeBtn.style.opacity = '0.8';
            closeBtn.onclick = () => this.hide(id);
            element.appendChild(closeBtn);
        }

        return element;
    }

    /**
     * 确保容器存在
     */
    ensureContainer() {
        if (!this.container || !document.body.contains(this.container)) {
            this.container = document.createElement('div');
            this.container.id = 'mdac-message-container';
            this.container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10001;
                max-width: 400px;
                width: 100%;
            `;
            document.body.appendChild(this.container);
        }
    }

    /**
     * 限制消息数量
     */
    limitMessages() {
        if (this.messages.size > this.maxMessages) {
            const oldestId = Array.from(this.messages.keys())[0];
            this.hide(oldestId);
        }
    }

    /**
     * 生成消息ID
     * @returns {string} 消息ID
     */
    generateMessageId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 清理所有消息
     */
    cleanup() {
        this.messages.forEach((messageData, id) => {
            this.hide(id);
        });
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
            this.container = null;
        }
    }
}

// 导出工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        LoadingManager,
        MessageSystem
    };
} else {
    // 浏览器环境
    window.UIEnhancements = {
        LoadingManager,
        MessageSystem
    };
}
