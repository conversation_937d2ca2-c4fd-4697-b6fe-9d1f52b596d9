# MDAC Chrome扩展开发者指南

## 📖 文档概述

**版本**: 1.0  
**更新日期**: 2025-07-15  
**适用版本**: MDAC AI智能填充工具 v2.0.0  
**目标读者**: 前端开发者、Chrome扩展开发者

---

## 🎯 项目简介

MDAC AI智能填充工具是一个基于Chrome Extension Manifest V3的智能表单填充扩展，专门为马来西亚数字入境卡（MDAC）表单设计。该扩展集成了Gemini AI技术，能够智能解析用户输入的个人信息并自动填充到目标表单中。

### 核心功能
- 🤖 AI驱动的个人信息解析
- 📝 智能表单字段检测和填充
- 🎨 现代化侧边栏用户界面
- 🔄 实时状态监控和错误恢复
- 📊 性能监控和日志记录

---

## 🏗️ 项目架构

### 技术栈
- **前端框架**: 原生JavaScript (ES6+)
- **扩展标准**: Chrome Extension Manifest V3
- **AI服务**: Google Gemini API
- **存储**: Chrome Storage API
- **UI框架**: 原生HTML/CSS + 自定义组件

### 架构模式
```
Chrome Extension (Manifest V3)
├── Background Service Worker (后台服务)
├── Content Scripts (内容脚本)
├── Side Panel UI (侧边栏界面)
├── Modular Components (模块化组件)
└── Event-Driven Communication (事件驱动通信)
```

---

## 📁 项目结构详解

```
MDAC_Extension/
├── manifest.json                 # Chrome扩展配置文件
├── background/
│   └── background.js             # 后台服务Worker
├── content/
│   └── content-script.js         # 内容脚本主文件
├── ui/
│   ├── ui-sidepanel-main.js      # 侧边栏主控制器
│   ├── sidepanel.html            # 侧边栏HTML结构
│   └── sidepanel.css             # 侧边栏样式
├── modules/                      # 核心功能模块
│   ├── event-bus.js              # 事件总线系统
│   ├── state-manager.js          # 状态管理器
│   ├── logger.js                 # 日志记录系统
│   ├── form-field-detector.js    # 表单字段检测器
│   ├── error-recovery.js         # 错误恢复管理
│   └── performance-monitor.js    # 性能监控器
├── config/
│   └── ai-config.js              # AI配置和提示词
├── icons/                        # 扩展图标资源
├── docs/                         # 文档目录
└── tests/                        # 测试文件
```

### 关键文件说明

#### 1. manifest.json
扩展的核心配置文件，定义了权限、脚本加载、API访问等关键设置。

```json
{
  "manifest_version": 3,
  "name": "MDAC AI智能分析工具",
  "version": "2.0.0",
  "permissions": ["sidePanel", "activeTab", "storage", "scripting", "tabs"],
  "host_permissions": ["https://imigresen-online.imi.gov.my/*"],
  "background": {
    "service_worker": "background/background.js"
  },
  "content_scripts": [{
    "matches": ["<all_urls>"],
    "js": ["content/content-script.js"]
  }]
}
```

#### 2. 模块加载机制
项目采用5层渐进式模块加载机制：

```javascript
// content-script.js中的加载层级
const LOADING_LAYERS = {
    LAYER_1: ['modules/event-bus.js', 'modules/logger.js'],
    LAYER_2: ['modules/state-manager.js', 'modules/error-recovery.js'],
    LAYER_3: ['modules/form-field-detector.js', 'modules/performance-monitor.js'],
    LAYER_4: ['config/ai-config.js'],
    LAYER_5: ['ui/ui-sidepanel-main.js']
};
```

---

## 🛠️ 开发环境搭建

### 前置要求
- **Chrome浏览器**: 版本 114+ (支持Manifest V3)
- **代码编辑器**: VS Code (推荐)
- **Node.js**: 16+ (用于开发工具，可选)
- **Git**: 用于版本控制

### 快速开始

#### 1. 克隆项目
```bash
git clone <项目地址>
cd mdac-extension
```

#### 2. 配置开发环境
```bash
# 如果使用Node.js开发工具
npm install  # 可选

# 配置AI API密钥
# 在config/ai-config.js中设置你的Gemini API密钥
```

#### 3. 加载扩展到Chrome
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录

#### 4. 验证安装
```javascript
// 在任意网页的控制台运行以下代码验证
console.log('Chrome Extension API:', typeof chrome !== 'undefined');
console.log('MDAC Extension:', chrome.runtime.id);
```

---

## 🔧 核心模块详解

### 1. 事件总线系统 (EventBus)

**文件**: `modules/event-bus.js`  
**作用**: 提供模块间解耦的事件通信机制

```javascript
// 基本用法
// 监听事件
mdacEventBus.on('dataUpdated', (data) => {
    console.log('数据已更新:', data);
});

// 发送事件
mdacEventBus.emit('dataUpdated', { field: 'name', value: 'John' });

// 一次性监听
mdacEventBus.once('systemReady', () => {
    console.log('系统已就绪');
});

// 移除监听器
mdacEventBus.off('dataUpdated', handlerFunction);
```

### 2. 状态管理器 (StateManager)

**文件**: `modules/state-manager.js`  
**作用**: 中央化状态管理，支持持久化存储

```javascript
// 获取状态
const currentData = await mdacStateManager.getState('personalInfo');

// 设置状态
await mdacStateManager.setState('personalInfo', {
    name: 'Zhang San',
    passport: '*********'
});

// 监听状态变化
mdacStateManager.subscribe('personalInfo', (newData) => {
    console.log('个人信息已更新:', newData);
});

// 清除状态
await mdacStateManager.clearState('personalInfo');
```

### 3. 日志记录系统 (Logger)

**文件**: `modules/logger.js`  
**作用**: 统一的日志记录和性能监控

```javascript
// 基本日志记录
mdacLogger.log('info', 'UserAction', '用户点击了提交按钮');
mdacLogger.log('error', 'APICall', '网络请求失败', { url: '/api/data' });
mdacLogger.log('warn', 'Validation', '输入格式可能有误');

// 性能监控
mdacLogger.startPerformance('apiCall');
// ... 执行API调用
mdacLogger.endPerformance('apiCall'); // 自动记录耗时

// 获取日志
const logs = mdacLogger.getLogs();
const errorLogs = mdacLogger.getLogs('error');
```

### 4. 表单字段检测器 (FormFieldDetector)

**文件**: `modules/form-field-detector.js`  
**作用**: 智能检测和分析页面表单字段

```javascript
// 检测表单字段
const detector = new FormFieldDetector();
const fields = detector.detectFormFields();

// 检测结果示例
console.log(fields);
// {
//   name: { element: <input>, confidence: 0.95, type: 'text' },
//   email: { element: <input>, confidence: 0.90, type: 'email' },
//   passport: { element: <input>, confidence: 0.85, type: 'text' }
// }

// 验证检测结果
const isValid = detector.validateDetection(fields);
```

### 5. AI配置系统 (MDAC_AI_CONFIG)

**文件**: `config/ai-config.js`  
**作用**: 集中管理AI相关配置和提示词

```javascript
// 访问AI配置
const geminiConfig = MDAC_AI_CONFIG.GEMINI_CONFIG;
const prompts = MDAC_AI_CONFIG.AI_PROMPTS;

// 使用特定提示词
const personalInfoPrompt = prompts.PERSONAL_INFO_PARSING;
const travelInfoPrompt = prompts.TRAVEL_INFO_PARSING;

// AI上下文配置
const contexts = MDAC_AI_CONFIG.AI_CONTEXTS;
const professionalContext = contexts.PROFESSIONAL;
```

---

## 🔄 开发工作流程

### 1. 功能开发流程

```mermaid
graph TD
    A[需求分析] --> B[设计模块接口]
    B --> C[创建模块文件]
    C --> D[实现核心逻辑]
    D --> E[集成事件总线]
    E --> F[添加日志记录]
    F --> G[编写测试用例]
    G --> H[功能测试]
    H --> I[代码审查]
    I --> J[文档更新]
```

### 2. 模块开发标准

#### 新模块创建模板
```javascript
/**
 * 模块名称 - 模块功能描述
 * @version 1.0.0
 * <AUTHOR>
 * @date 创建日期
 */

// 导入依赖
// 依赖其他模块时，确保在适当的加载层级

class ModuleName {
    constructor() {
        this.logger = window.mdacLogger;
        this.eventBus = window.mdacEventBus;
        this.initialized = false;
        
        this.init();
    }
    
    /**
     * 模块初始化
     */
    async init() {
        try {
            this.logger?.log('info', 'ModuleName', '模块初始化开始');
            
            // 初始化逻辑
            await this.setupModule();
            
            this.initialized = true;
            this.eventBus?.emit('moduleReady', { module: 'ModuleName' });
            this.logger?.log('info', 'ModuleName', '模块初始化完成');
            
        } catch (error) {
            this.logger?.log('error', 'ModuleName', '模块初始化失败', { error: error.message });
            throw error;
        }
    }
    
    /**
     * 模块具体设置
     */
    async setupModule() {
        // 实现模块特定逻辑
    }
    
    /**
     * 清理资源
     */
    destroy() {
        // 清理逻辑
        this.initialized = false;
    }
}

// 全局暴露
window.ModuleName = ModuleName;
```

### 3. 事件命名规范

```javascript
// 事件命名采用驼峰式，包含动作和对象
const EVENT_NAMES = {
    // 系统级事件
    SYSTEM_READY: 'systemReady',
    SYSTEM_ERROR: 'systemError',
    
    // 数据相关事件  
    DATA_UPDATED: 'dataUpdated',
    DATA_CLEARED: 'dataCleared',
    DATA_SAVED: 'dataSaved',
    
    // UI交互事件
    UI_BUTTON_CLICKED: 'uiButtonClicked',
    UI_FIELD_CHANGED: 'uiFieldChanged',
    
    // AI处理事件
    AI_PARSING_START: 'aiParsingStart',
    AI_PARSING_COMPLETE: 'aiParsingComplete',
    AI_PARSING_ERROR: 'aiParsingError'
};
```

---

## 🧪 测试和调试

### 1. 开发者工具调试

#### 在Content Script中调试
```javascript
// 在页面控制台运行
console.log('EventBus状态:', window.mdacEventBus);
console.log('Logger实例:', window.mdacLogger);
console.log('状态管理器:', window.mdacStateManager);

// 检查模块加载状态
Object.keys(window).filter(key => key.startsWith('mdac')).forEach(key => {
    console.log(`${key}:`, window[key]);
});
```

#### 在Background Script中调试
```javascript
// 在扩展管理页面 -> 背景页面 -> 控制台运行
console.log('Background实例:', globalThis.mdacBackground);
console.log('标签页管理:', globalThis.mdacBackground?.activeTabs);
```

#### 在Side Panel中调试
```javascript
// 在侧边栏页面控制台运行
console.log('主控制器:', window.mdacMainController);
console.log('UI状态:', window.mdacMainController?.uiState);
```

### 2. 常用调试命令

```javascript
// 快速系统健康检查
function systemHealthCheck() {
    const components = [
        'mdacEventBus', 'mdacLogger', 'mdacStateManager',
        'MDACMainController', 'FormFieldDetector'
    ];
    
    components.forEach(component => {
        const status = window[component] ? '✅ 已加载' : '❌ 未找到';
        console.log(`${component}: ${status}`);
    });
}

// 事件监听器调试
function debugEventBus() {
    const originalEmit = window.mdacEventBus.emit;
    window.mdacEventBus.emit = function(event, data) {
        console.log(`🔄 事件发送: ${event}`, data);
        return originalEmit.call(this, event, data);
    };
}

// 状态变化监控
function monitorStateChanges() {
    window.mdacStateManager.subscribe('*', (key, value) => {
        console.log(`📊 状态变化: ${key}`, value);
    });
}
```

### 3. 性能分析

```javascript
// 模块加载时间分析
function analyzeLoadingPerformance() {
    const logs = window.mdacLogger.getLogs()
        .filter(log => log.message.includes('加载完成'))
        .map(log => ({
            module: log.category,
            time: log.timestamp,
            message: log.message
        }));
    
    console.table(logs);
}

// 内存使用监控
function checkMemoryUsage() {
    if (performance.memory) {
        const memory = performance.memory;
        console.log('内存使用情况:', {
            used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
            total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
            limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`
        });
    }
}
```

---

## 📝 代码规范

### 1. JavaScript编码规范

#### 变量命名
```javascript
// 使用驼峰式命名
const userName = 'John';
const apiResponse = await fetchData();

// 常量使用大写下划线
const MAX_RETRY_COUNT = 3;
const DEFAULT_TIMEOUT = 5000;

// 私有方法使用下划线前缀
class MyClass {
    _privateMethod() {
        // 私有方法实现
    }
    
    publicMethod() {
        // 公共方法实现
    }
}
```

#### 函数定义
```javascript
// 使用JSDoc注释格式
/**
 * 解析个人信息
 * @param {string} inputText - 输入的文本
 * @param {Object} options - 解析选项
 * @param {boolean} options.strict - 是否严格模式
 * @returns {Promise<Object>} 解析结果
 */
async function parsePersonalInfo(inputText, options = {}) {
    // 函数实现
}

// 错误处理
async function processData(data) {
    try {
        const result = await heavyOperation(data);
        return result;
    } catch (error) {
        this.logger?.log('error', 'ProcessData', '数据处理失败', {
            error: error.message,
            data: data
        });
        throw error;
    }
}
```

### 2. 模块结构规范

```javascript
// 模块文件结构模板
(function() {
    'use strict';
    
    // 1. 常量定义
    const MODULE_NAME = 'ExampleModule';
    const VERSION = '1.0.0';
    
    // 2. 私有变量
    let initialized = false;
    let config = {};
    
    // 3. 私有方法
    function _validateInput(input) {
        // 验证逻辑
    }
    
    function _processData(data) {
        // 数据处理逻辑
    }
    
    // 4. 主类定义
    class ExampleModule {
        constructor(options = {}) {
            this.config = Object.assign({}, config, options);
            this.logger = window.mdacLogger;
            this.eventBus = window.mdacEventBus;
        }
        
        // 5. 公共方法
        async init() {
            // 初始化逻辑
        }
        
        processInput(input) {
            // 公共方法实现
        }
    }
    
    // 6. 全局暴露
    window.ExampleModule = ExampleModule;
    
    // 7. 自动初始化（如果需要）
    if (window.mdacEventBus) {
        window.mdacEventBus.on('systemReady', () => {
            window.exampleModule = new ExampleModule();
        });
    }
})();
```

### 3. 错误处理规范

```javascript
// 统一错误处理类
class MDACError extends Error {
    constructor(message, code, details = {}) {
        super(message);
        this.name = 'MDACError';
        this.code = code;
        this.details = details;
        this.timestamp = new Date().toISOString();
    }
}

// 错误码定义
const ERROR_CODES = {
    MODULE_LOAD_FAILED: 'MODULE_LOAD_FAILED',
    API_REQUEST_FAILED: 'API_REQUEST_FAILED',
    VALIDATION_FAILED: 'VALIDATION_FAILED',
    NETWORK_ERROR: 'NETWORK_ERROR'
};

// 使用示例
function validateUserInput(input) {
    if (!input || input.trim() === '') {
        throw new MDACError(
            '用户输入不能为空',
            ERROR_CODES.VALIDATION_FAILED,
            { input: input }
        );
    }
}
```

---

## 🔒 安全最佳实践

### 1. 内容安全策略 (CSP)

当前CSP配置：
```json
{
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://generativelanguage.googleapis.com https://imigresen-online.imi.gov.my;"
  }
}
```

### 2. 敏感数据处理

```javascript
// ❌ 错误：不要在日志中记录敏感信息
mdacLogger.log('info', 'UserData', 'API密钥: sk-abc123...');

// ✅ 正确：脱敏处理
mdacLogger.log('info', 'UserData', 'API密钥已配置', { 
    keyLength: apiKey.length,
    keyPrefix: apiKey.substring(0, 6) + '...'
});

// ✅ 正确：使用安全的存储方式
async function storeSecureData(data) {
    // 不存储明文敏感信息
    const sanitizedData = {
        ...data,
        apiKey: undefined,
        password: undefined
    };
    
    await mdacStateManager.setState('userData', sanitizedData);
}
```

### 3. 输入验证和清理

```javascript
// 输入验证工具
class InputValidator {
    static sanitizeText(input) {
        if (typeof input !== 'string') return '';
        
        return input
            .trim()
            .replace(/[<>]/g, '') // 移除可能的HTML标签
            .substring(0, 1000);   // 限制长度
    }
    
    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    static validatePassportNumber(passport) {
        // 护照号格式验证
        const passportRegex = /^[A-Za-z0-9]{6,12}$/;
        return passportRegex.test(passport);
    }
}
```

---

## 📊 性能优化指导

### 1. 模块加载优化

```javascript
// 延迟加载非关键模块
class LazyLoader {
    static async loadModule(moduleName) {
        if (window[moduleName]) {
            return window[moduleName];
        }
        
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = `modules/${moduleName.toLowerCase()}.js`;
            script.onload = () => resolve(window[moduleName]);
            script.onerror = () => reject(new Error(`Failed to load ${moduleName}`));
            document.head.appendChild(script);
        });
    }
}

// 条件加载
async function loadOptionalModules() {
    // 只在需要时加载OCR模块
    if (document.querySelector('input[type="file"]')) {
        await LazyLoader.loadModule('OCRProcessor');
    }
}
```

### 2. 内存管理

```javascript
// 定期清理缓存
class MemoryManager {
    static cleanup() {
        // 清理过期的状态数据
        mdacStateManager.clearExpiredData();
        
        // 清理旧日志
        mdacLogger.clearOldLogs();
        
        // 移除不活跃的事件监听器
        mdacEventBus.removeInactiveListeners();
    }
    
    static startPeriodicCleanup() {
        setInterval(() => {
            this.cleanup();
        }, 5 * 60 * 1000); // 每5分钟清理一次
    }
}
```

### 3. API调用优化

```javascript
// API调用缓存和去重
class APIOptimizer {
    constructor() {
        this.cache = new Map();
        this.pendingRequests = new Map();
    }
    
    async callAPI(endpoint, data) {
        const cacheKey = `${endpoint}_${JSON.stringify(data)}`;
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        // 检查是否有相同请求正在进行
        if (this.pendingRequests.has(cacheKey)) {
            return this.pendingRequests.get(cacheKey);
        }
        
        // 发起新请求
        const promise = this._makeRequest(endpoint, data);
        this.pendingRequests.set(cacheKey, promise);
        
        try {
            const result = await promise;
            this.cache.set(cacheKey, result);
            return result;
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }
    
    async _makeRequest(endpoint, data) {
        // 实际API调用逻辑
    }
}
```

---

## 🚀 部署和发布

### 1. 构建准备

```bash
# 检查代码质量
npm run lint        # 代码规范检查
npm run test        # 运行测试
npm run build       # 构建生产版本

# 版本管理
npm version patch   # 更新补丁版本
npm version minor   # 更新次要版本
npm version major   # 更新主要版本
```

### 2. 打包检查清单

- [ ] 更新manifest.json中的版本号
- [ ] 确认所有API密钥已移除或配置为生产环境
- [ ] 运行完整的功能测试
- [ ] 检查CSP配置是否适当
- [ ] 确认图标和资源文件完整
- [ ] 验证权限配置最小化

### 3. 发布流程

1. **Chrome Web Store发布**
   - 登录Chrome开发者控制台
   - 上传扩展包
   - 填写商店列表信息
   - 提交审核

2. **内部分发**
   - 生成`.crx`文件
   - 通过企业策略分发
   - 或提供开发者加载指南

---

## 📚 相关资源

### 官方文档
- [Chrome Extension Developer Guide](https://developer.chrome.com/docs/extensions/)
- [Manifest V3 Migration Guide](https://developer.chrome.com/docs/extensions/migrating/)
- [Google Gemini API Documentation](https://ai.google.dev/docs)

### 开发工具
- [Chrome Extension Developer Tools](https://chrome.google.com/webstore/detail/chrome-extension-developer-tools)
- [Extension Source Viewer](https://chrome.google.com/webstore/detail/extension-source-viewer)

### 社区资源
- [Chrome Extension Dev Community](https://groups.google.com/a/chromium.org/g/chromium-extensions)
- [Stack Overflow Chrome Extension Tag](https://stackoverflow.com/questions/tagged/google-chrome-extension)

---

## 🤝 获取帮助

### 常见问题
查看项目的 `FAQ.md` 文档或故障排除指南。

### 技术支持
- 查看已有的GitHub Issues
- 创建新的Issue并提供详细信息
- 参考项目的贡献者指南

### 联系方式
如需直接技术支持，请提供：
1. Chrome版本信息
2. 错误重现步骤
3. 控制台错误日志
4. 期望的功能行为

---

**文档版本**: 1.0  
**最后更新**: 2025-07-15  
**维护者**: MDAC开发团队