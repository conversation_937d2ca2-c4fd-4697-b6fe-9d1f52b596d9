/**
 * MDAC Extension Stage 1 Fix Test Script
 * 测试阶段1的错误修复效果
 */

console.log('🧪 开始测试 MDAC Extension Stage 1 修复效果...');

// 测试1: 检查Logger方法是否存在
function testLoggerMethods() {
    console.log('\n📋 测试1: <PERSON><PERSON>方法检查');
    
    try {
        // 尝试加载logger模块
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('modules/logger.js');
        script.onload = () => {
            console.log('✅ Logger模块加载成功');
            
            // 检查是否存在全局MDACLogger
            if (typeof window.logger !== 'undefined') {
                const logger = window.logger;
                
                // 检查setLevel方法
                if (typeof logger.setLevel === 'function') {
                    console.log('✅ setLevel方法存在');
                    logger.setLevel('debug');
                    console.log('✅ setLevel方法测试通过');
                } else {
                    console.error('❌ setLevel方法不存在');
                }
                
                // 检查startPerformance方法
                if (typeof logger.startPerformance === 'function') {
                    console.log('✅ startPerformance方法存在');
                    logger.startPerformance('test');
                    console.log('✅ startPerformance方法测试通过');
                } else {
                    console.error('❌ startPerformance方法不存在');
                }
                
                // 检查endPerformance方法
                if (typeof logger.endPerformance === 'function') {
                    console.log('✅ endPerformance方法存在');
                    const duration = logger.endPerformance('test');
                    console.log(`✅ endPerformance方法测试通过，耗时: ${duration}ms`);
                } else {
                    console.error('❌ endPerformance方法不存在');
                }
            } else {
                console.error('❌ 全局logger对象不存在');
            }
        };
        script.onerror = () => {
            console.error('❌ Logger模块加载失败');
        };
        document.head.appendChild(script);
        
    } catch (error) {
        console.error('❌ Logger测试异常:', error);
    }
}

// 测试2: 检查消息重试机制
function testMessageRetry() {
    console.log('\n📋 测试2: 消息重试机制检查');
    
    // 模拟发送消息到背景脚本
    chrome.runtime.sendMessage({
        action: 'content-script-ready',
        pageType: 'test',
        fieldsDetected: 0
    }, (response) => {
        if (chrome.runtime.lastError) {
            console.error('❌ 背景脚本通信失败:', chrome.runtime.lastError.message);
        } else {
            console.log('✅ 背景脚本通信成功:', response);
        }
    });
}

// 测试3: 检查manifest.json中的资源路径
function testManifestResources() {
    console.log('\n📋 测试3: Manifest资源路径检查');
    
    const resourcePaths = [
        'modules/logger.js',
        'modules/form-field-detector.js',
        'modules/google-maps-integration.js',
        'modules/error-recovery-manager.js',
        'modules/fill-monitor.js',
        'modules/field-status-display.js'
    ];
    
    resourcePaths.forEach(path => {
        try {
            const url = chrome.runtime.getURL(path);
            console.log(`✅ 资源路径正确: ${path} -> ${url}`);
        } catch (error) {
            console.error(`❌ 资源路径错误: ${path}`, error);
        }
    });
}

// 运行所有测试
setTimeout(() => {
    testLoggerMethods();
    setTimeout(testMessageRetry, 1000);
    setTimeout(testManifestResources, 2000);
}, 500);

console.log('🎯 Stage 1 测试脚本已加载，将在页面加载后自动运行测试...');
