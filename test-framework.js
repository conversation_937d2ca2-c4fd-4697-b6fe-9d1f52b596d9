/**
 * MDAC AI Chrome扩展自动化测试框架
 * 支持单元测试、集成测试和端到端测试
 */

class MDACTestFramework {
  constructor() {
    this.testSuites = new Map();
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };
    this.currentSuite = null;
    this.beforeEachHooks = [];
    this.afterEachHooks = [];
    this.beforeAllHooks = [];
    this.afterAllHooks = [];
  }

  /**
   * 创建测试套件
   */
  describe(suiteName, suiteFunction) {
    console.log(`📋 创建测试套件: ${suiteName}`);
    
    this.currentSuite = {
      name: suiteName,
      tests: [],
      beforeEach: [],
      afterEach: [],
      beforeAll: [],
      afterAll: []
    };
    
    // 执行套件函数来收集测试
    try {
      suiteFunction();
      this.testSuites.set(suiteName, this.currentSuite);
    } catch (error) {
      console.error(`❌ 测试套件 ${suiteName} 创建失败:`, error);
    }
    
    this.currentSuite = null;
  }

  /**
   * 定义单个测试
   */
  it(testName, testFunction) {
    if (!this.currentSuite) {
      throw new Error('测试必须在describe块内定义');
    }
    
    this.currentSuite.tests.push({
      name: testName,
      function: testFunction,
      timeout: 5000 // 默认超时5秒
    });
  }

  /**
   * 定义跳过的测试
   */
  xit(testName, testFunction) {
    if (!this.currentSuite) {
      throw new Error('测试必须在describe块内定义');
    }
    
    this.currentSuite.tests.push({
      name: testName,
      function: testFunction,
      skipped: true
    });
  }

  /**
   * 每个测试前执行
   */
  beforeEach(hookFunction) {
    if (this.currentSuite) {
      this.currentSuite.beforeEach.push(hookFunction);
    } else {
      this.beforeEachHooks.push(hookFunction);
    }
  }

  /**
   * 每个测试后执行
   */
  afterEach(hookFunction) {
    if (this.currentSuite) {
      this.currentSuite.afterEach.push(hookFunction);
    } else {
      this.afterEachHooks.push(hookFunction);
    }
  }

  /**
   * 套件开始前执行
   */
  beforeAll(hookFunction) {
    if (this.currentSuite) {
      this.currentSuite.beforeAll.push(hookFunction);
    } else {
      this.beforeAllHooks.push(hookFunction);
    }
  }

  /**
   * 套件结束后执行
   */
  afterAll(hookFunction) {
    if (this.currentSuite) {
      this.currentSuite.afterAll.push(hookFunction);
    } else {
      this.afterAllHooks.push(hookFunction);
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始运行MDAC AI Chrome扩展测试套件...');
    console.log('='.repeat(60));
    
    // 重置测试结果
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };
    
    const startTime = Date.now();
    
    // 执行全局beforeAll钩子
    for (const hook of this.beforeAllHooks) {
      try {
        await hook();
      } catch (error) {
        console.error('❌ 全局beforeAll钩子失败:', error);
      }
    }
    
    // 运行每个测试套件
    for (const [suiteName, suite] of this.testSuites) {
      await this.runTestSuite(suiteName, suite);
    }
    
    // 执行全局afterAll钩子
    for (const hook of this.afterAllHooks) {
      try {
        await hook();
      } catch (error) {
        console.error('❌ 全局afterAll钩子失败:', error);
      }
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateTestReport(duration);
  }

  /**
   * 运行单个测试套件
   */
  async runTestSuite(suiteName, suite) {
    console.log(`\n📋 运行测试套件: ${suiteName}`);
    console.log('-'.repeat(40));
    
    // 执行套件beforeAll钩子
    for (const hook of suite.beforeAll) {
      try {
        await hook();
      } catch (error) {
        console.error(`❌ 套件 ${suiteName} beforeAll钩子失败:`, error);
      }
    }
    
    // 运行每个测试
    for (const test of suite.tests) {
      await this.runSingleTest(suiteName, test, suite);
    }
    
    // 执行套件afterAll钩子
    for (const hook of suite.afterAll) {
      try {
        await hook();
      } catch (error) {
        console.error(`❌ 套件 ${suiteName} afterAll钩子失败:`, error);
      }
    }
  }

  /**
   * 运行单个测试
   */
  async runSingleTest(suiteName, test, suite) {
    this.testResults.total++;
    
    if (test.skipped) {
      this.testResults.skipped++;
      console.log(`⏭️ 跳过: ${test.name}`);
      return;
    }
    
    console.log(`🧪 运行: ${test.name}`);
    
    try {
      // 执行beforeEach钩子
      for (const hook of [...this.beforeEachHooks, ...suite.beforeEach]) {
        await hook();
      }
      
      // 运行测试（带超时）
      const testPromise = test.function();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('测试超时')), test.timeout || 5000);
      });
      
      await Promise.race([testPromise, timeoutPromise]);
      
      // 执行afterEach钩子
      for (const hook of [...this.afterEachHooks, ...suite.afterEach]) {
        await hook();
      }
      
      this.testResults.passed++;
      console.log(`✅ 通过: ${test.name}`);
      
    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push({
        suite: suiteName,
        test: test.name,
        error: error.message,
        stack: error.stack
      });
      console.error(`❌ 失败: ${test.name} - ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport(duration) {
    console.log('\n📊 MDAC AI Chrome扩展测试报告');
    console.log('='.repeat(60));
    
    const successRate = (this.testResults.passed / this.testResults.total * 100).toFixed(1);
    
    console.log(`⏱️ 执行时间: ${duration}ms`);
    console.log(`📊 总测试数: ${this.testResults.total}`);
    console.log(`✅ 通过: ${this.testResults.passed}`);
    console.log(`❌ 失败: ${this.testResults.failed}`);
    console.log(`⏭️ 跳过: ${this.testResults.skipped}`);
    console.log(`📈 成功率: ${successRate}%`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ 失败详情:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`\n${index + 1}. ${error.suite} > ${error.test}`);
        console.log(`   错误: ${error.error}`);
        if (error.stack) {
          console.log(`   堆栈: ${error.stack.split('\n')[1]?.trim()}`);
        }
      });
    }
    
    console.log('='.repeat(60));
    
    if (this.testResults.failed === 0) {
      console.log('🎉 所有测试通过！');
    } else {
      console.log('⚠️ 存在失败的测试，请检查并修复');
    }
  }

  /**
   * 断言工具
   */
  expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`期望 ${actual} 等于 ${expected}`);
        }
      },
      
      toEqual: (expected) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`期望 ${JSON.stringify(actual)} 深度等于 ${JSON.stringify(expected)}`);
        }
      },
      
      toBeTruthy: () => {
        if (!actual) {
          throw new Error(`期望 ${actual} 为真值`);
        }
      },
      
      toBeFalsy: () => {
        if (actual) {
          throw new Error(`期望 ${actual} 为假值`);
        }
      },
      
      toBeNull: () => {
        if (actual !== null) {
          throw new Error(`期望 ${actual} 为 null`);
        }
      },
      
      toBeUndefined: () => {
        if (actual !== undefined) {
          throw new Error(`期望 ${actual} 为 undefined`);
        }
      },
      
      toContain: (expected) => {
        if (!actual.includes(expected)) {
          throw new Error(`期望 ${actual} 包含 ${expected}`);
        }
      },
      
      toHaveLength: (expected) => {
        if (actual.length !== expected) {
          throw new Error(`期望长度为 ${expected}，实际为 ${actual.length}`);
        }
      },
      
      toThrow: () => {
        let threw = false;
        try {
          actual();
        } catch (error) {
          threw = true;
        }
        if (!threw) {
          throw new Error('期望函数抛出异常');
        }
      },
      
      toBeInstanceOf: (expected) => {
        if (!(actual instanceof expected)) {
          throw new Error(`期望 ${actual} 是 ${expected.name} 的实例`);
        }
      }
    };
  }

  /**
   * 异步等待工具
   */
  async waitFor(condition, timeout = 5000, interval = 100) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return true;
      }
      await this.delay(interval);
    }
    
    throw new Error(`等待条件超时 (${timeout}ms)`);
  }

  /**
   * 延迟工具
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 模拟用户输入
   */
  async simulateUserInput(element, value) {
    if (!element) {
      throw new Error('元素不存在');
    }
    
    // 清空现有值
    element.value = '';
    element.focus();
    
    // 模拟逐字输入
    for (const char of value) {
      element.value += char;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await this.delay(50);
    }
    
    // 触发change事件
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.blur();
  }

  /**
   * 模拟点击
   */
  async simulateClick(element) {
    if (!element) {
      throw new Error('元素不存在');
    }
    
    element.focus();
    element.click();
    element.dispatchEvent(new Event('click', { bubbles: true }));
  }

  /**
   * 获取元素文本
   */
  getElementText(element) {
    if (!element) {
      throw new Error('元素不存在');
    }
    
    return element.textContent || element.innerText || element.value || '';
  }

  /**
   * 检查元素是否可见
   */
  isElementVisible(element) {
    if (!element) return false;
    
    const style = window.getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0' &&
           element.offsetWidth > 0 && 
           element.offsetHeight > 0;
  }

  /**
   * 等待元素出现
   */
  async waitForElement(selector, timeout = 5000) {
    return this.waitFor(() => {
      const element = document.querySelector(selector);
      return element && this.isElementVisible(element);
    }, timeout);
  }

  /**
   * 等待元素消失
   */
  async waitForElementToDisappear(selector, timeout = 5000) {
    return this.waitFor(() => {
      const element = document.querySelector(selector);
      return !element || !this.isElementVisible(element);
    }, timeout);
  }
}

// 创建全局测试框架实例
const testFramework = new MDACTestFramework();

// 导出全局函数
window.describe = testFramework.describe.bind(testFramework);
window.it = testFramework.it.bind(testFramework);
window.xit = testFramework.xit.bind(testFramework);
window.beforeEach = testFramework.beforeEach.bind(testFramework);
window.afterEach = testFramework.afterEach.bind(testFramework);
window.beforeAll = testFramework.beforeAll.bind(testFramework);
window.afterAll = testFramework.afterAll.bind(testFramework);
window.expect = testFramework.expect.bind(testFramework);
window.runAllTests = testFramework.runAllTests.bind(testFramework);

// 导出测试框架
window.MDACTestFramework = MDACTestFramework;
window.testFramework = testFramework;

console.log('🧪 MDAC测试框架已加载');
console.log('使用 describe(), it(), expect() 编写测试');
console.log('使用 runAllTests() 运行所有测试');
