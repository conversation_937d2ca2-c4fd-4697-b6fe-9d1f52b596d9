/**
 * MDAC表单字段检测器模块 - 兼容性存根
 * 提供表单字段检测功能的后备实现
 * 
 * 功能特性：
 * - 智能表单字段检测
 * - 字段类型识别
 * - 填充状态监控
 * - 全局实例自动创建
 * - 向后兼容现有代码
 * 
 * 创建时间: 2025-01-12
 * 作者: MDAC AI智能分析工具
 */

(function() {
    'use strict';
    
    /**
     * MDAC表单字段检测器类
     * 提供智能表单字段检测和分析功能
     */
    class FormFieldDetector {
        constructor() {
            this.detectedFields = {};
            this.fieldMappings = {};
            this.isInitialized = false;
            this.logger = window.mdacLogger || console;
            this.selectors = {
                inputs: 'input, select, textarea',
                forms: 'form',
                labels: 'label'
            };
            
            this.initialize();
        }
        
        /**
         * 初始化字段检测器
         */
        async initialize() {
            try {
                this.loadFieldMappings();
                this.setupMutationObserver();
                this.isInitialized = true;
                this.log('info', 'FormFieldDetector', '表单字段检测器初始化成功');
            } catch (error) {
                this.log('error', 'FormFieldDetector', '初始化失败', error);
            }
        }
        
        /**
         * 加载字段映射配置 - 修正了MDAC网站的实际字段ID
         */
        loadFieldMappings() {
            // MDAC官方网站字段映射 - 基于实际测试结果
            this.fieldMappings = {
                // 个人信息字段 - 使用MDAC网站的实际字段ID
                'fullName': ['name', 'fullname', 'full_name', 'family_name'],
                'passportNumber': ['passport', 'passport_no', 'passportno', 'document'],
                'nationality': ['nationality', 'country', 'nation'],
                'dob': ['birth', 'birthdate', 'birth_date', 'dob'],
                'sex': ['gender', 'sex'],
                'passExpDte': ['passexpiry', 'passport_expiry', 'expiry', 'expire'], // 修正：护照到期日期

                // 联系信息字段
                'email': ['email', 'mail', 'e_mail'],
                'countryCode': ['country_code', 'region', 'phone_code'],
                'mobileNumber': ['phone', 'telephone', 'mobile', 'contact'],

                // 旅行信息字段 - 使用MDAC网站的实际字段ID
                'arrDt': ['arrival', 'arrival_date', 'check_in'], // 修正：到达日期
                'depDt': ['departure', 'departure_date', 'check_out'], // 修正：离开日期
                'vesselNm': ['flight', 'flight_no', 'flightno', 'vessel'],
                'trvlMode': ['travel_mode', 'mode', 'transport'],
                'embark': ['embark', 'port', 'departure_port', 'last_port'], // Last Port of Embarkation

                // 住宿信息字段
                'accommodationStay': ['accommodation', 'hotel', 'stay'],
                'accommodationAddress1': ['address1', 'address', 'street'],
                'accommodationAddress2': ['address2', 'street2'],
                'accommodationState': ['state', 'province'],
                'accommodationPostcode': ['postcode', 'zip', 'postal'],
                'accommodationCity': ['city', 'town']
            };
        }
        
        /**
         * 设置DOM变化监听器
         */
        setupMutationObserver() {
            const observer = new MutationObserver((mutations) => {
                let shouldRedetect = false;
                
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (node.matches && (node.matches(this.selectors.inputs) || node.querySelector(this.selectors.inputs))) {
                                    shouldRedetect = true;
                                }
                            }
                        });
                    }
                });
                
                if (shouldRedetect) {
                    this.detectFields();
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
        
        /**
         * 检测页面中的表单字段 (兼容content-script.js)
         * @returns {Object} 检测到的字段对象
         */
        async detectFormFields() {
            this.log('info', 'FormFieldDetector', 'detectFormFields方法被调用（兼容性适配）');
            return await this.detectFields();
        }
        
        /**
         * 检测页面中的表单字段
         * @returns {Object} 检测到的字段对象
         */
        async detectFields() {
            try {
                this.detectedFields = {};
                
                // 获取所有表单元素
                const formElements = document.querySelectorAll(this.selectors.inputs);
                
                formElements.forEach((element, index) => {
                    const fieldInfo = this.analyzeField(element, index);
                    if (fieldInfo.type !== 'unknown') {
                        this.detectedFields[fieldInfo.id] = fieldInfo;
                    }
                });
                
                this.log('info', 'FormFieldDetector', `检测到 ${Object.keys(this.detectedFields).length} 个字段`);
                
                // 发送检测结果事件
                this.notifyFieldsDetected();
                
                return this.detectedFields;
                
            } catch (error) {
                this.log('error', 'FormFieldDetector', '字段检测失败', error);
                return {};
            }
        }
        
        /**
         * 分析单个字段
         * @param {Element} element - 表单元素
         * @param {number} index - 元素索引
         * @returns {Object} 字段信息
         */
        analyzeField(element, index) {
            const fieldInfo = {
                id: element.id || `field_${index}`,
                name: element.name || '',
                type: this.identifyFieldType(element),
                element: element,
                selector: this.generateSelector(element),
                label: this.findFieldLabel(element),
                required: element.required || element.hasAttribute('required'),
                currentValue: element.value || '',
                placeholder: element.placeholder || ''
            };
            
            return fieldInfo;
        }
        
        /**
         * 识别字段类型
         * @param {Element} element - 表单元素
         * @returns {string} 字段类型
         */
        identifyFieldType(element) {
            const name = (element.name || '').toLowerCase();
            const id = (element.id || '').toLowerCase();
            const placeholder = (element.placeholder || '').toLowerCase();
            const label = this.findFieldLabel(element).toLowerCase();
            
            const searchText = `${name} ${id} ${placeholder} ${label}`;
            
            // 遍历字段映射找匹配
            for (const [fieldType, keywords] of Object.entries(this.fieldMappings)) {
                for (const keyword of keywords) {
                    if (searchText.includes(keyword.toLowerCase())) {
                        return fieldType;
                    }
                }
            }
            
            // 基于输入类型判断
            if (element.type === 'email') return 'email';
            if (element.type === 'tel') return 'phone';
            if (element.type === 'date') return 'date';
            if (element.type === 'password') return 'password';
            
            return 'unknown';
        }
        
        /**
         * 生成CSS选择器
         * @param {Element} element - 表单元素
         * @returns {string} CSS选择器
         */
        generateSelector(element) {
            if (element.id) {
                return `#${element.id}`;
            }
            if (element.name) {
                return `[name="${element.name}"]`;
            }
            
            // 生成基于标签和属性的选择器
            let selector = element.tagName.toLowerCase();
            if (element.type) {
                selector += `[type="${element.type}"]`;
            }
            if (element.className) {
                selector += `.${element.className.split(' ')[0]}`;
            }
            
            return selector;
        }
        
        /**
         * 查找字段标签
         * @param {Element} element - 表单元素
         * @returns {string} 字段标签文本
         */
        findFieldLabel(element) {
            // 通过for属性查找
            if (element.id) {
                const label = document.querySelector(`label[for="${element.id}"]`);
                if (label) return label.textContent.trim();
            }
            
            // 查找父级label
            const parentLabel = element.closest('label');
            if (parentLabel) return parentLabel.textContent.trim();
            
            // 查找前面的标签元素
            const prevElement = element.previousElementSibling;
            if (prevElement && (prevElement.tagName === 'LABEL' || prevElement.tagName === 'SPAN')) {
                return prevElement.textContent.trim();
            }
            
            return '';
        }
        
        /**
         * 验证检测结果 (兼容content-script.js)
         * @param {Object} detectedFields - 检测到的字段
         * @returns {Object} 验证结果
         */
        validateDetection(detectedFields) {
            const validation = {
                isValid: true,
                fieldCount: Object.keys(detectedFields).length,
                warnings: [],
                errors: []
            };
            
            // 检查必填字段
            const requiredFields = ['name', 'passportNo'];
            requiredFields.forEach(field => {
                if (!detectedFields[field]) {
                    validation.warnings.push(`缺少必填字段: ${field}`);
                }
            });
            
            // 检查字段总数
            if (validation.fieldCount < 3) {
                validation.warnings.push('检测到的字段数量较少，可能存在检测问题');
            }
            
            this.log('info', 'FormFieldDetector', '字段验证完成', validation);
            
            return validation;
        }
        
        /**
         * 通知字段检测完成
         */
        notifyFieldsDetected() {
            // 发送Chrome扩展消息
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({
                    action: 'fields-detected',
                    data: {
                        fields: this.detectedFields,
                        count: Object.keys(this.detectedFields).length,
                        timestamp: Date.now()
                    }
                }).catch(error => {
                    this.log('warn', 'FormFieldDetector', '发送字段检测消息失败', error);
                });
            }
            
            // 发送自定义事件
            const event = new CustomEvent('mdac:fields-detected', {
                detail: {
                    fields: this.detectedFields,
                    detector: this
                }
            });
            document.dispatchEvent(event);
        }
        
        /**
         * 获取检测到的字段
         * @returns {Object} 字段对象
         */
        getDetectedFields() {
            return this.detectedFields;
        }
        
        /**
         * 获取特定类型的字段
         * @param {string} fieldType - 字段类型
         * @returns {Array} 匹配的字段数组
         */
        getFieldsByType(fieldType) {
            return Object.values(this.detectedFields).filter(field => field.type === fieldType);
        }
        
        /**
         * 清空检测结果
         */
        clearDetectedFields() {
            this.detectedFields = {};
            this.log('info', 'FormFieldDetector', '已清空检测结果');
        }
        
        /**
         * 日志记录方法
         * @param {string} level - 日志级别
         * @param {string} module - 模块名
         * @param {string} message - 消息
         * @param {*} data - 数据
         */
        log(level, module, message, data = null) {
            if (this.logger && typeof this.logger.log === 'function') {
                this.logger.log(level, module, message, data);
            } else {
                console[level](`[${module}] ${message}`, data);
            }
        }
    }
    
    // 检查是否已存在FormFieldDetector，避免重复定义
    if (typeof window.FormFieldDetector === 'undefined') {
        // 将FormFieldDetector类挂载到全局window对象
        window.FormFieldDetector = FormFieldDetector;
        
        // 创建全局实例供其他模块使用
        if (typeof window.mdacFieldDetector === 'undefined') {
            window.mdacFieldDetector = new FormFieldDetector();
            console.log('✅ [FormFieldDetector] 全局实例创建成功');
        }
    } else {
        console.log('✅ [FormFieldDetector] 类已存在，跳过重复定义');
    }
    
})();
