/**
 * MDAC Chrome插件全面验证脚本
 * 使用方法：在Chrome开发者工具Console中运行此脚本
 * 
 * 验证内容：
 * 1. 插件基础组件加载状态
 * 2. AI配置和功能可用性
 * 3. 表单检测和填充功能
 * 4. UI界面响应性
 * 5. 错误处理机制
 */

console.log('🚀 开始MDAC插件全面验证...');

// 验证结果收集器
const verificationResults = {
    timestamp: new Date().toISOString(),
    results: [],
    summary: { passed: 0, failed: 0, warnings: 0 }
};

// 添加验证结果
function addResult(category, name, status, details = '', recommendation = '') {
    const result = {
        category,
        name,
        status, // 'PASS', 'FAIL', 'WARN'
        details,
        recommendation,
        timestamp: new Date().toISOString()
    };
    
    verificationResults.results.push(result);
    verificationResults.summary[status.toLowerCase() === 'pass' ? 'passed' : 
                                 status.toLowerCase() === 'fail' ? 'failed' : 'warnings']++;
    
    const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${icon} [${category}] ${name}: ${status}`);
    if (details) console.log(`   详情: ${details}`);
    if (recommendation) console.log(`   建议: ${recommendation}`);
}

// 1. 验证插件基础组件
function verifyBasicComponents() {
    console.log('\n📦 验证基础组件...');
    
    // 检查Chrome扩展API
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        addResult('基础组件', 'Chrome扩展API', 'PASS', 'chrome.runtime可用');
    } else {
        addResult('基础组件', 'Chrome扩展API', 'FAIL', 'chrome对象不可用', '确保在扩展环境中运行');
    }
    
    // 检查全局对象
    const globalObjects = [
        'MDACMainController',
        'MDACContentScript', 
        'FormFieldDetector',
        'MDACLogger',
        'MDAC_AI_CONFIG'
    ];
    
    globalObjects.forEach(objName => {
        if (typeof window[objName] !== 'undefined') {
            addResult('基础组件', objName, 'PASS', `${objName}已正确加载`);
        } else {
            addResult('基础组件', objName, 'WARN', `${objName}未在全局作用域中找到`, '检查模块加载顺序');
        }
    });
}

// 2. 验证AI配置
function verifyAIConfiguration() {
    console.log('\n🤖 验证AI配置...');
    
    if (window.MDAC_AI_CONFIG) {
        const config = window.MDAC_AI_CONFIG;
        
        // 检查必要配置项
        const requiredConfigs = [
            'GEMINI_CONFIG',
            'AI_PROMPTS', 
            'AI_CONTEXTS',
            'AI_FEATURES'
        ];
        
        requiredConfigs.forEach(configName => {
            if (config[configName]) {
                addResult('AI配置', configName, 'PASS', `配置项完整`);
            } else {
                addResult('AI配置', configName, 'FAIL', `缺少必要配置`, '检查ai-config.js加载');
            }
        });
        
        // 检查提示词数量
        if (config.AI_PROMPTS) {
            const promptCount = Object.keys(config.AI_PROMPTS).length;
            if (promptCount >= 10) {
                addResult('AI配置', 'AI提示词', 'PASS', `包含${promptCount}个提示词`);
            } else {
                addResult('AI配置', 'AI提示词', 'WARN', `仅有${promptCount}个提示词`, '建议增加更多专业提示词');
            }
        }
        
    } else {
        addResult('AI配置', '整体配置', 'FAIL', 'MDAC_AI_CONFIG未加载', '检查配置文件路径');
    }
}

// 3. 验证表单检测功能
function verifyFormDetection() {
    console.log('\n🔍 验证表单检测功能...');
    
    const forms = document.querySelectorAll('form');
    const inputs = document.querySelectorAll('input, select, textarea');
    
    addResult('表单检测', '表单元素', 'PASS', `检测到${forms.length}个表单, ${inputs.length}个输入字段`);
    
    // 检查字段检测器
    if (window.FormFieldDetector || window.mdacFieldDetector) {
        const detector = window.FormFieldDetector || window.mdacFieldDetector;
        
        if (typeof detector.detectFormFields === 'function') {
            addResult('表单检测', '字段检测器', 'PASS', '检测方法可用');
            
            // 尝试运行检测
            try {
                const detected = detector.detectFormFields ? detector.detectFormFields() : null;
                if (detected && typeof detected === 'object') {
                    addResult('表单检测', '检测执行', 'PASS', `成功检测，返回对象类型`);
                } else {
                    addResult('表单检测', '检测执行', 'WARN', '检测返回非对象结果');
                }
            } catch (error) {
                addResult('表单检测', '检测执行', 'FAIL', `检测失败: ${error.message}`, '检查检测器实现');
            }
        } else {
            addResult('表单检测', '字段检测器', 'FAIL', '检测方法不可用', '检查FormFieldDetector加载');
        }
    } else {
        addResult('表单检测', '字段检测器', 'FAIL', '字段检测器未加载');
    }
}

// 4. 验证UI界面
function verifyUI() {
    console.log('\n🎨 验证UI界面...');
    
    // 检查侧边栏元素
    const sidebarElements = [
        'connectionStatus',
        'aiStatus', 
        'updateToMDACBtn',
        'clearAllBtn',
        'previewBtn'
    ];
    
    sidebarElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            addResult('UI界面', elementId, 'PASS', `元素存在且可访问`);
        } else {
            addResult('UI界面', elementId, 'WARN', `元素未找到`, '可能在侧边栏中，需要打开侧边栏验证');
        }
    });
    
    // 检查CSS样式加载
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    addResult('UI界面', 'CSS样式', 'PASS', `加载了${stylesheets.length}个样式表`);
}

// 5. 验证消息传递
function verifyMessaging() {
    console.log('\n📡 验证消息传递...');
    
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 尝试发送ping消息
        try {
            chrome.runtime.sendMessage({ action: 'ping', timestamp: Date.now() }, (response) => {
                if (chrome.runtime.lastError) {
                    addResult('消息传递', 'ping测试', 'WARN', 
                        `消息发送失败: ${chrome.runtime.lastError.message}`, 
                        '可能是正常情况，取决于context');
                } else if (response) {
                    addResult('消息传递', 'ping测试', 'PASS', `收到响应: ${JSON.stringify(response)}`);
                } else {
                    addResult('消息传递', 'ping测试', 'WARN', '发送成功但无响应');
                }
            });
        } catch (error) {
            addResult('消息传递', 'ping测试', 'FAIL', `发送异常: ${error.message}`);
        }
    } else {
        addResult('消息传递', 'Chrome API', 'FAIL', 'chrome.runtime不可用');
    }
}

// 6. 验证错误处理
function verifyErrorHandling() {
    console.log('\n🛡️ 验证错误处理...');
    
    // 检查错误恢复管理器
    if (window.ErrorRecoveryManager) {
        addResult('错误处理', '错误恢复管理器', 'PASS', '错误恢复管理器已加载');
    } else {
        addResult('错误处理', '错误恢复管理器', 'WARN', '错误恢复管理器未在全局作用域');
    }
    
    // 检查日志系统
    if (window.mdacLogger || window.MDACLogger) {
        const logger = window.mdacLogger || window.MDACLogger;
        if (typeof logger.log === 'function') {
            addResult('错误处理', '日志系统', 'PASS', '日志系统功能可用');
            
            // 测试日志功能
            try {
                logger.log('info', 'VerificationTest', '这是一条测试日志');
                addResult('错误处理', '日志测试', 'PASS', '日志记录成功');
            } catch (error) {
                addResult('错误处理', '日志测试', 'FAIL', `日志记录失败: ${error.message}`);
            }
        } else {
            addResult('错误处理', '日志系统', 'FAIL', '日志系统方法不可用');
        }
    } else {
        addResult('错误处理', '日志系统', 'WARN', '日志系统未找到');
    }
}

// 7. 验证性能指标
function verifyPerformance() {
    console.log('\n📊 验证性能指标...');
    
    // 内存使用检查
    if (performance.memory) {
        const memory = performance.memory;
        const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
        const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
        
        if (usedMB < 50) {
            addResult('性能指标', '内存使用', 'PASS', `当前使用${usedMB}MB，限制${limitMB}MB`);
        } else if (usedMB < 100) {
            addResult('性能指标', '内存使用', 'WARN', `使用${usedMB}MB，建议优化`, '考虑清理未使用对象');
        } else {
            addResult('性能指标', '内存使用', 'FAIL', `使用${usedMB}MB，过高`, '需要内存优化');
        }
    }
    
    // DOM元素数量检查
    const elementCount = document.querySelectorAll('*').length;
    if (elementCount < 1000) {
        addResult('性能指标', 'DOM复杂度', 'PASS', `页面包含${elementCount}个元素`);
    } else {
        addResult('性能指标', 'DOM复杂度', 'WARN', `页面包含${elementCount}个元素，较复杂`);
    }
}

// 8. 生成详细报告
function generateReport() {
    console.log('\n📋 生成验证报告...');
    
    const { passed, failed, warnings } = verificationResults.summary;
    const total = passed + failed + warnings;
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 MDAC插件验证报告');
    console.log('='.repeat(60));
    console.log(`📅 验证时间: ${verificationResults.timestamp}`);
    console.log(`📊 验证统计: 总计${total}项 | ✅通过${passed}项 | ❌失败${failed}项 | ⚠️警告${warnings}项`);
    
    if (failed === 0 && warnings <= 2) {
        console.log('🎉 整体状态: 优秀 - 插件工作正常');
    } else if (failed <= 2 && warnings <= 5) {
        console.log('👍 整体状态: 良好 - 存在轻微问题');
    } else if (failed <= 5) {
        console.log('⚠️  整体状态: 需要注意 - 存在多个问题');
    } else {
        console.log('🚨 整体状态: 需要修复 - 存在严重问题');
    }
    
    console.log('\n📋 详细结果:');
    
    // 按类别分组显示结果
    const categories = [...new Set(verificationResults.results.map(r => r.category))];
    categories.forEach(category => {
        console.log(`\n📂 ${category}:`);
        const categoryResults = verificationResults.results.filter(r => r.category === category);
        categoryResults.forEach(result => {
            const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
            console.log(`   ${icon} ${result.name}: ${result.status}`);
            if (result.details) console.log(`      📝 ${result.details}`);
            if (result.recommendation) console.log(`      💡 ${result.recommendation}`);
        });
    });
    
    // 存储到全局变量供进一步分析
    window.mdacVerificationResults = verificationResults;
    
    console.log('\n🔗 完整结果已保存到: window.mdacVerificationResults');
    console.log('💾 使用 JSON.stringify(window.mdacVerificationResults, null, 2) 导出完整报告');
}

// 主验证函数
async function runFullVerification() {
    console.log('🔍 开始全面验证流程...\n');
    
    verifyBasicComponents();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    verifyAIConfiguration();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    verifyFormDetection();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    verifyUI();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    verifyMessaging();
    await new Promise(resolve => setTimeout(resolve, 500)); // 等待消息响应
    
    verifyErrorHandling();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    verifyPerformance();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    generateReport();
}

// 工具函数：检查特定功能
window.mdacQuickTest = {
    // 快速测试AI配置
    testAI: () => {
        console.log('🤖 快速AI测试...');
        verifyAIConfiguration();
    },
    
    // 快速测试表单检测
    testForms: () => {
        console.log('🔍 快速表单测试...');
        verifyFormDetection();
    },
    
    // 快速测试UI
    testUI: () => {
        console.log('🎨 快速UI测试...');
        verifyUI();
    },
    
    // 显示内存使用
    showMemory: () => {
        if (performance.memory) {
            const memory = performance.memory;
            console.log('💾 内存使用情况:');
            console.log(`   已使用: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`);
            console.log(`   总计: ${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`);
            console.log(`   限制: ${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`);
        }
    },
    
    // 检查网络状态
    checkNetwork: () => {
        console.log('🌐 网络状态检查...');
        console.log(`   在线状态: ${navigator.onLine ? '✅ 在线' : '❌ 离线'}`);
        console.log(`   连接类型: ${navigator.connection ? navigator.connection.effectiveType : '未知'}`);
    }
};

// 启动验证
console.log('🚀 MDAC插件验证脚本已加载');
console.log('📝 使用方法:');
console.log('   - runFullVerification() : 运行完整验证');
console.log('   - mdacQuickTest.testAI() : 快速测试AI功能');
console.log('   - mdacQuickTest.testForms() : 快速测试表单功能');
console.log('   - mdacQuickTest.showMemory() : 显示内存使用');
console.log('\n⚡ 正在自动运行完整验证...\n');

// 自动运行完整验证
runFullVerification();