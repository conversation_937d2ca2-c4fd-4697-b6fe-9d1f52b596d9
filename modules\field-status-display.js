/**
 * MDAC字段状态显示器模块 - 兼容性存根
 * 提供字段状态可视化和用户反馈功能的后备实现
 * 
 * 功能特性：
 * - 字段状态可视化显示
 * - 填充进度指示器
 * - 验证结果提示
 * - 全局实例自动创建
 * - 向后兼容现有代码
 * 
 * 创建时间: 2025-01-12
 * 作者: MDAC AI智能分析工具
 */

(function() {
    'use strict';
    
    /**
     * MDAC字段状态显示器类
     * 提供字段状态的可视化显示和用户界面反馈
     */
    class FieldStatusDisplay {
        constructor() {
            this.isInitialized = false;
            this.displayContainer = null;
            this.statusIndicators = new Map();
            this.progressBar = null;
            this.logger = window.mdacLogger || console;
            this.config = {
                showProgressBar: true,
                showFieldIndicators: true,
                position: 'top-right',
                theme: 'light'
            };
            
            this.initialize();
        }
        
        /**
         * 初始化字段状态显示器
         */
        async initialize() {
            try {
                this.createDisplayContainer();
                this.setupEventListeners();
                
                this.isInitialized = true;
                this.log('info', 'FieldStatusDisplay', '字段状态显示器初始化成功');
            } catch (error) {
                this.log('error', 'FieldStatusDisplay', '初始化失败', error);
            }
        }
        
        /**
         * 创建显示容器
         */
        createDisplayContainer() {
            // 检查是否已存在容器
            this.displayContainer = document.getElementById('mdac-field-status-display');
            
            if (!this.displayContainer) {
                this.displayContainer = document.createElement('div');
                this.displayContainer.id = 'mdac-field-status-display';
                this.displayContainer.className = 'mdac-status-display';
                
                // 设置容器样式
                this.applyContainerStyles();
                
                // 添加到页面
                document.body.appendChild(this.displayContainer);
                
                this.log('debug', 'FieldStatusDisplay', '显示容器已创建');
            }
            
            // 创建进度条
            if (this.config.showProgressBar) {
                this.createProgressBar();
            }
        }
        
        /**
         * 应用容器样式
         */
        applyContainerStyles() {
            const position = this.getPositionStyles();
            
            this.displayContainer.style.cssText = `
                position: fixed;
                ${position}
                z-index: 9999;
                background: ${this.config.theme === 'dark' ? '#333' : '#fff'};
                border: 1px solid ${this.config.theme === 'dark' ? '#555' : '#ddd'};
                border-radius: 8px;
                padding: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                font-family: Arial, sans-serif;
                font-size: 14px;
                min-width: 200px;
                max-width: 300px;
                transition: all 0.3s ease;
                opacity: 0;
                transform: translateY(-10px);
            `;
            
            // 添加动画效果
            setTimeout(() => {
                this.displayContainer.style.opacity = '1';
                this.displayContainer.style.transform = 'translateY(0)';
            }, 100);
        }
        
        /**
         * 获取位置样式
         * @returns {string} CSS位置样式
         */
        getPositionStyles() {
            switch (this.config.position) {
                case 'top-left':
                    return 'top: 20px; left: 20px;';
                case 'top-right':
                    return 'top: 20px; right: 20px;';
                case 'bottom-left':
                    return 'bottom: 20px; left: 20px;';
                case 'bottom-right':
                    return 'bottom: 20px; right: 20px;';
                case 'center':
                    return 'top: 50%; left: 50%; transform: translate(-50%, -50%);';
                default:
                    return 'top: 20px; right: 20px;';
            }
        }
        
        /**
         * 创建进度条
         */
        createProgressBar() {
            if (this.progressBar) return;
            
            // 进度条容器
            const progressContainer = document.createElement('div');
            progressContainer.className = 'mdac-progress-container';
            progressContainer.style.cssText = `
                margin-bottom: 12px;
                padding: 8px;
                background: ${this.config.theme === 'dark' ? '#2a2a2a' : '#f5f5f5'};
                border-radius: 6px;
            `;
            
            // 进度条标题
            const progressTitle = document.createElement('div');
            progressTitle.className = 'mdac-progress-title';
            progressTitle.textContent = '填充进度';
            progressTitle.style.cssText = `
                font-weight: bold;
                margin-bottom: 6px;
                color: ${this.config.theme === 'dark' ? '#fff' : '#333'};
                font-size: 12px;
            `;
            
            // 进度条
            const progressBarBg = document.createElement('div');
            progressBarBg.className = 'mdac-progress-bg';
            progressBarBg.style.cssText = `
                width: 100%;
                height: 8px;
                background: ${this.config.theme === 'dark' ? '#444' : '#e0e0e0'};
                border-radius: 4px;
                overflow: hidden;
                position: relative;
            `;
            
            this.progressBar = document.createElement('div');
            this.progressBar.className = 'mdac-progress-bar';
            this.progressBar.style.cssText = `
                width: 0%;
                height: 100%;
                background: linear-gradient(90deg, #4caf50, #8bc34a);
                border-radius: 4px;
                transition: width 0.3s ease;
                position: relative;
            `;
            
            // 进度文本
            const progressText = document.createElement('div');
            progressText.className = 'mdac-progress-text';
            progressText.textContent = '0%';
            progressText.style.cssText = `
                text-align: center;
                margin-top: 4px;
                color: ${this.config.theme === 'dark' ? '#ccc' : '#666'};
                font-size: 11px;
            `;
            
            // 组装进度条
            progressBarBg.appendChild(this.progressBar);
            progressContainer.appendChild(progressTitle);
            progressContainer.appendChild(progressBarBg);
            progressContainer.appendChild(progressText);
            
            this.displayContainer.appendChild(progressContainer);
            
            // 保存引用
            this.progressText = progressText;
        }
        
        /**
         * 设置事件监听器
         */
        setupEventListeners() {
            // 监听填充监控器事件
            document.addEventListener('mdac:fill-progress-updated', (event) => {
                this.updateProgress(event.detail);
            });
            
            document.addEventListener('mdac:fill-field-updated', (event) => {
                this.updateFieldStatus(event.detail);
            });
            
            document.addEventListener('mdac:fill-monitor-started', (event) => {
                this.show();
                this.updateProgress({ total: 0, filled: 0, valid: 0, progress: 0 });
            });
            
            document.addEventListener('mdac:fill-monitor-stopped', (event) => {
                setTimeout(() => this.hide(), 3000); // 3秒后隐藏
            });
            
            // 监听页面可见性变化
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.hide();
                } else {
                    this.show();
                }
            });
        }
        
        /**
         * 更新填充进度
         * @param {Object} progressData - 进度数据
         */
        updateProgress(progressData) {
            if (!this.progressBar || !this.progressText) return;
            
            const { total, filled, valid, progress } = progressData;
            
            // 更新进度条
            this.progressBar.style.width = `${progress}%`;
            
            // 更新进度文本
            this.progressText.textContent = `${progress}% (${filled}/${total})`;
            
            // 根据进度更新颜色
            if (progress >= 100) {
                this.progressBar.style.background = 'linear-gradient(90deg, #4caf50, #66bb6a)';
            } else if (progress >= 75) {
                this.progressBar.style.background = 'linear-gradient(90deg, #8bc34a, #aed581)';
            } else if (progress >= 50) {
                this.progressBar.style.background = 'linear-gradient(90deg, #ffc107, #ffeb3b)';
            } else {
                this.progressBar.style.background = 'linear-gradient(90deg, #ff9800, #ffb74d)';
            }
            
            this.log('debug', 'FieldStatusDisplay', '进度已更新', progressData);
        }
        
        /**
         * 更新字段状态
         * @param {Object} fieldData - 字段数据
         */
        updateFieldStatus(fieldData) {
            if (!this.config.showFieldIndicators) return;
            
            const { fieldId, isFilled, isValid } = fieldData;
            
            let indicator = this.statusIndicators.get(fieldId);
            
            if (!indicator) {
                indicator = this.createFieldIndicator(fieldId);
                this.statusIndicators.set(fieldId, indicator);
            }
            
            // 更新指示器状态
            this.updateIndicatorStatus(indicator, isFilled, isValid);
        }
        
        /**
         * 创建字段指示器
         * @param {string} fieldId - 字段ID
         * @returns {Element} 指示器元素
         */
        createFieldIndicator(fieldId) {
            // 指示器列表容器
            let indicatorsList = this.displayContainer.querySelector('.mdac-indicators-list');
            
            if (!indicatorsList) {
                const indicatorsContainer = document.createElement('div');
                indicatorsContainer.className = 'mdac-indicators-container';
                indicatorsContainer.style.cssText = `
                    margin-top: 8px;
                    padding-top: 8px;
                    border-top: 1px solid ${this.config.theme === 'dark' ? '#555' : '#eee'};
                `;
                
                const indicatorsTitle = document.createElement('div');
                indicatorsTitle.className = 'mdac-indicators-title';
                indicatorsTitle.textContent = '字段状态';
                indicatorsTitle.style.cssText = `
                    font-weight: bold;
                    margin-bottom: 6px;
                    color: ${this.config.theme === 'dark' ? '#fff' : '#333'};
                    font-size: 12px;
                `;
                
                indicatorsList = document.createElement('div');
                indicatorsList.className = 'mdac-indicators-list';
                indicatorsList.style.cssText = `
                    display: flex;
                    flex-wrap: wrap;
                    gap: 4px;
                `;
                
                indicatorsContainer.appendChild(indicatorsTitle);
                indicatorsContainer.appendChild(indicatorsList);
                this.displayContainer.appendChild(indicatorsContainer);
            }
            
            // 创建指示器
            const indicator = document.createElement('div');
            indicator.className = 'mdac-field-indicator';
            indicator.title = `字段: ${fieldId}`;
            indicator.style.cssText = `
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #ccc;
                border: 2px solid ${this.config.theme === 'dark' ? '#555' : '#ddd'};
                transition: all 0.3s ease;
                cursor: pointer;
            `;
            
            indicatorsList.appendChild(indicator);
            
            return indicator;
        }
        
        /**
         * 更新指示器状态
         * @param {Element} indicator - 指示器元素
         * @param {boolean} isFilled - 是否已填充
         * @param {boolean} isValid - 是否有效
         */
        updateIndicatorStatus(indicator, isFilled, isValid) {
            if (!isFilled) {
                // 未填充
                indicator.style.background = '#ccc';
                indicator.style.borderColor = this.config.theme === 'dark' ? '#555' : '#ddd';
                indicator.title += ' - 未填充';
            } else if (isValid) {
                // 已填充且有效
                indicator.style.background = '#4caf50';
                indicator.style.borderColor = '#4caf50';
                indicator.title += ' - 已填充（有效）';
            } else {
                // 已填充但无效
                indicator.style.background = '#f44336';
                indicator.style.borderColor = '#f44336';
                indicator.title += ' - 已填充（无效）';
            }
        }
        
        /**
         * 显示状态栏
         */
        show() {
            if (!this.displayContainer) return;
            
            this.displayContainer.style.display = 'block';
            setTimeout(() => {
                this.displayContainer.style.opacity = '1';
                this.displayContainer.style.transform = 'translateY(0)';
            }, 10);
            
            this.log('debug', 'FieldStatusDisplay', '状态栏已显示');
        }
        
        /**
         * 隐藏状态栏
         */
        hide() {
            if (!this.displayContainer) return;
            
            this.displayContainer.style.opacity = '0';
            this.displayContainer.style.transform = 'translateY(-10px)';
            
            setTimeout(() => {
                this.displayContainer.style.display = 'none';
            }, 300);
            
            this.log('debug', 'FieldStatusDisplay', '状态栏已隐藏');
        }
        
        /**
         * 切换显示状态
         */
        toggle() {
            if (this.displayContainer.style.display === 'none') {
                this.show();
            } else {
                this.hide();
            }
        }
        
        /**
         * 显示成功消息
         * @param {string} message - 消息内容
         * @param {number} duration - 显示时长
         */
        showSuccessMessage(message, duration = 3000) {
            this.showMessage(message, 'success', duration);
        }
        
        /**
         * 显示错误消息
         * @param {string} message - 消息内容
         * @param {number} duration - 显示时长
         */
        showErrorMessage(message, duration = 5000) {
            this.showMessage(message, 'error', duration);
        }
        
        /**
         * 显示警告消息
         * @param {string} message - 消息内容
         * @param {number} duration - 显示时长
         */
        showWarningMessage(message, duration = 4000) {
            this.showMessage(message, 'warning', duration);
        }
        
        /**
         * 显示消息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         * @param {number} duration - 显示时长
         */
        showMessage(message, type = 'info', duration = 3000) {
            // 移除现有消息
            const existingMessage = this.displayContainer.querySelector('.mdac-status-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            // 创建消息元素
            const messageElement = document.createElement('div');
            messageElement.className = 'mdac-status-message';
            
            const colors = {
                success: '#4caf50',
                error: '#f44336',
                warning: '#ff9800',
                info: '#2196f3'
            };
            
            messageElement.style.cssText = `
                background: ${colors[type] || colors.info};
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                margin-top: 8px;
                font-size: 12px;
                text-align: center;
                animation: slideIn 0.3s ease;
            `;
            
            messageElement.textContent = message;
            
            // 添加CSS动画
            if (!document.querySelector('#mdac-status-animations')) {
                const style = document.createElement('style');
                style.id = 'mdac-status-animations';
                style.textContent = `
                    @keyframes slideIn {
                        from { opacity: 0; transform: translateY(-10px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                    @keyframes slideOut {
                        from { opacity: 1; transform: translateY(0); }
                        to { opacity: 0; transform: translateY(-10px); }
                    }
                `;
                document.head.appendChild(style);
            }
            
            this.displayContainer.appendChild(messageElement);
            
            // 自动移除
            if (duration > 0) {
                setTimeout(() => {
                    if (messageElement && messageElement.parentNode) {
                        messageElement.style.animation = 'slideOut 0.3s ease';
                        setTimeout(() => {
                            if (messageElement && messageElement.parentNode) {
                                messageElement.remove();
                            }
                        }, 300);
                    }
                }, duration);
            }
        }
        
        /**
         * 设置配置
         * @param {Object} newConfig - 新配置
         */
        setConfig(newConfig) {
            this.config = { ...this.config, ...newConfig };
            
            // 重新应用样式
            if (this.displayContainer) {
                this.applyContainerStyles();
            }
            
            this.log('debug', 'FieldStatusDisplay', '配置已更新', this.config);
        }
        
        /**
         * 清除所有指示器
         */
        clearIndicators() {
            this.statusIndicators.clear();
            
            const indicatorsList = this.displayContainer.querySelector('.mdac-indicators-list');
            if (indicatorsList) {
                indicatorsList.innerHTML = '';
            }
            
            this.log('debug', 'FieldStatusDisplay', '所有指示器已清除');
        }
        
        /**
         * 重置进度
         */
        resetProgress() {
            if (this.progressBar && this.progressText) {
                this.progressBar.style.width = '0%';
                this.progressText.textContent = '0%';
                this.progressBar.style.background = 'linear-gradient(90deg, #ff9800, #ffb74d)';
            }
        }
        
        /**
         * 销毁显示器
         */
        destroy() {
            if (this.displayContainer && this.displayContainer.parentNode) {
                this.displayContainer.parentNode.removeChild(this.displayContainer);
            }
            
            this.statusIndicators.clear();
            this.displayContainer = null;
            this.progressBar = null;
            this.progressText = null;
            
            this.log('info', 'FieldStatusDisplay', '显示器已销毁');
        }
        
        /**
         * 日志记录方法
         * @param {string} level - 日志级别
         * @param {string} module - 模块名
         * @param {string} message - 消息
         * @param {*} data - 数据
         */
        log(level, module, message, data = null) {
            if (this.logger && typeof this.logger.log === 'function') {
                this.logger.log(level, module, message, data);
            } else {
                console[level](`[${module}] ${message}`, data);
            }
        }
    }
    
    // 检查是否已存在FieldStatusDisplay，避免重复定义
    if (typeof window.FieldStatusDisplay === 'undefined') {
        // 将FieldStatusDisplay类挂载到全局window对象
        window.FieldStatusDisplay = FieldStatusDisplay;
        
        // 创建全局实例供其他模块使用
        if (typeof window.mdacFieldStatusDisplay === 'undefined') {
            window.mdacFieldStatusDisplay = new FieldStatusDisplay();
            console.log('✅ [FieldStatusDisplay] 全局实例创建成功');
        }
    } else {
        console.log('✅ [FieldStatusDisplay] 类已存在，跳过重复定义');
    }
    
})();
