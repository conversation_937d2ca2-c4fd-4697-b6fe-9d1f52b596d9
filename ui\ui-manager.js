/**
 * MDAC UI管理器
 * 集成和管理所有UI组件
 */

class MDACUIManager {
  constructor() {
    this.fieldStatusIndicator = null;
    this.progressIndicator = null;
    this.isInitialized = false;
  }

  /**
   * 初始化UI系统
   */
  async init() {
    if (this.isInitialized) return;
    
    try {
      console.log('🎨 初始化MDAC UI系统...');
      
      // 初始化各个组件
      this.fieldStatusIndicator = new FieldStatusIndicator();
      this.progressIndicator = new ProgressIndicator();
      
      // 等待页面加载完成
      await this.waitForPageReady();
      
      // 设置表单字段
      this.setupFormFields();
      
      // 设置全局引用
      window.mdacUIManager = this;
      window.mdacFieldStatusIndicator = this.fieldStatusIndicator;
      window.mdacProgressIndicator = this.progressIndicator;
      
      this.isInitialized = true;
      console.log('✅ MDAC UI系统初始化完成');
      
    } catch (error) {
      console.error('❌ MDAC UI系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 等待页面准备就绪
   */
  waitForPageReady() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve);
      }
    });
  }

  /**
   * 设置表单字段
   */
  setupFormFields() {
    const fieldIds = [
      'fullName', 'passportNumber', 'dob', 'nationality', 'sex', 'passExpDte',
      'email', 'region', 'mobile', 'arrDt', 'depDt', 'embark', 'vesselNm', 'trvlMode'
    ];
    
    fieldIds.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        // 添加状态指示器
        this.fieldStatusIndicator.addIndicator(field, fieldId);
        console.log(`✅ 字段 ${fieldId} UI组件设置完成`);
      } else {
        console.warn(`⚠️ 字段 ${fieldId} 不存在`);
      }
    });
  }

  /**
   * 开始填充进度显示
   */
  startFillProgress(totalFields) {
    this.progressIndicator.show();
    this.progressIndicator.clearStatus();
    this.progressIndicator.updateProgress(0, totalFields, '开始填充表单...');
  }

  /**
   * 更新字段填充状态
   */
  updateFieldStatus(fieldId, status, message = '') {
    // 更新状态指示器
    this.fieldStatusIndicator.updateStatus(fieldId, status, message);
    
    // 更新进度指示器
    this.progressIndicator.addFieldStatus(fieldId, status, message);
  }

  /**
   * 完成填充进度显示
   */
  completeFillProgress(successCount, totalCount) {
    this.progressIndicator.updateProgress(totalCount, totalCount, 
      `填充完成：${successCount}/${totalCount} 个字段成功`);
    
    // 3秒后自动隐藏进度指示器
    setTimeout(() => {
      this.progressIndicator.hide();
    }, 3000);
  }

  /**
   * 显示通知消息
   */
  showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `mdac-notification mdac-notification-${type}`;
    notification.innerHTML = `
      <div class="mdac-notification-content">
        <span class="mdac-notification-icon">${this.getNotificationIcon(type)}</span>
        <span class="mdac-notification-message">${message}</span>
      </div>
      <button class="mdac-notification-close">×</button>
    `;
    
    // 添加样式（如果还没有）
    this.ensureNotificationStyles();
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 添加关闭事件
    notification.querySelector('.mdac-notification-close').addEventListener('click', () => {
      notification.remove();
    });
    
    // 自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, duration);
    
    // 显示动画
    setTimeout(() => {
      notification.classList.add('visible');
    }, 10);
  }

  /**
   * 获取通知图标
   */
  getNotificationIcon(type) {
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    return icons[type] || icons.info;
  }

  /**
   * 确保通知样式存在
   */
  ensureNotificationStyles() {
    if (document.getElementById('mdac-notification-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'mdac-notification-styles';
    style.textContent = `
      .mdac-notification {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%) translateY(-100px);
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 10002;
        min-width: 300px;
        max-width: 500px;
        opacity: 0;
        transition: all 0.3s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .mdac-notification.visible {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
      }
      
      .mdac-notification-content {
        display: flex;
        align-items: center;
        padding: 15px 20px;
      }
      
      .mdac-notification-icon {
        margin-right: 10px;
        font-size: 18px;
      }
      
      .mdac-notification-message {
        flex: 1;
        font-size: 14px;
        color: #333;
      }
      
      .mdac-notification-close {
        position: absolute;
        top: 5px;
        right: 5px;
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: #999;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
      }
      
      .mdac-notification-close:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
      
      .mdac-notification-info {
        border-left: 4px solid #17a2b8;
      }
      
      .mdac-notification-success {
        border-left: 4px solid #28a745;
      }
      
      .mdac-notification-warning {
        border-left: 4px solid #ffc107;
      }
      
      .mdac-notification-error {
        border-left: 4px solid #dc3545;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 销毁UI系统
   */
  destroy() {
    try {
      // 隐藏所有组件
      if (this.progressIndicator) {
        this.progressIndicator.hide();
      }
      
      // 清理全局引用
      delete window.mdacUIManager;
      delete window.mdacFieldStatusIndicator;
      delete window.mdacProgressIndicator;
      
      this.isInitialized = false;
      console.log('🗑️ MDAC UI系统已销毁');
      
    } catch (error) {
      console.error('❌ MDAC UI系统销毁失败:', error);
    }
  }
}

// 导出UI管理器
window.MDACUIManager = MDACUIManager;
