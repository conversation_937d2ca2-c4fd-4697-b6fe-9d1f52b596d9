# MDAC Chrome扩展API接口文档

## 📖 文档概述

**版本**: 1.0  
**更新日期**: 2025-07-15  
**适用版本**: MDAC AI智能填充工具 v2.0.0  
**目标读者**: 前端开发者、API集成开发者

---

## 🎯 API概述

本文档详细描述了MDAC Chrome扩展中所有模块的公共API接口，包括方法签名、参数说明、返回值类型和使用示例。所有API都遵循统一的设计原则，支持异步操作和错误处理。

### API设计原则
- 🔄 **异步优先**: 所有耗时操作都采用Promise/async-await模式
- 🛡️ **错误处理**: 统一的错误类型和处理机制
- 📊 **类型安全**: 详细的参数类型和返回值定义
- 🔍 **可观测性**: 内置日志记录和性能监控

---

## 🌐 全局对象

### window.mdacEventBus
全局事件总线实例，用于模块间通信。

### window.mdacLogger
全局日志记录器实例，用于统一日志管理。

### window.mdacStateManager
全局状态管理器实例，用于数据持久化和状态管理。

---

## 📡 EventBus API

**文件位置**: `modules/event-bus.js`  
**全局实例**: `window.mdacEventBus`

### 类：EventBus

基于发布-订阅模式的事件通信系统，支持类型安全的事件传递。

#### 构造函数

```javascript
constructor()
```

创建新的EventBus实例。

**示例**:
```javascript
const eventBus = new EventBus();
```

#### 方法：on

```javascript
on(event, listener)
```

注册事件监听器。

**参数**:
- `event` (string): 事件名称
- `listener` (function): 事件处理函数

**返回值**: `EventBus` - 支持链式调用

**示例**:
```javascript
mdacEventBus.on('dataUpdated', (data) => {
    console.log('数据已更新:', data);
});

// 链式调用
mdacEventBus
    .on('userLogin', handleLogin)
    .on('userLogout', handleLogout);
```

#### 方法：once

```javascript
once(event, listener)
```

注册一次性事件监听器，触发后自动移除。

**参数**:
- `event` (string): 事件名称
- `listener` (function): 事件处理函数

**返回值**: `EventBus` - 支持链式调用

**示例**:
```javascript
mdacEventBus.once('systemReady', () => {
    console.log('系统初始化完成，此消息只显示一次');
    initializeUI();
});
```

#### 方法：off

```javascript
off(event, listener)
```

移除事件监听器。

**参数**:
- `event` (string): 事件名称
- `listener` (function): 要移除的事件处理函数

**返回值**: `EventBus` - 支持链式调用

**示例**:
```javascript
function handleDataUpdate(data) {
    console.log('处理数据更新:', data);
}

// 注册监听器
mdacEventBus.on('dataUpdated', handleDataUpdate);

// 移除监听器
mdacEventBus.off('dataUpdated', handleDataUpdate);
```

#### 方法：emit

```javascript
emit(event, ...args)
```

触发事件，通知所有监听器。

**参数**:
- `event` (string): 事件名称
- `...args` (any[]): 传递给监听器的参数

**返回值**: `boolean` - 是否有监听器处理了该事件

**示例**:
```javascript
// 发送简单事件
mdacEventBus.emit('buttonClicked');

// 发送带数据的事件
mdacEventBus.emit('dataUpdated', {
    type: 'personalInfo',
    data: { name: 'John', age: 30 }
});

// 发送多个参数
mdacEventBus.emit('apiResponse', response, status, headers);
```

#### 方法：removeAllListeners

```javascript
removeAllListeners(event)
```

移除指定事件的所有监听器。

**参数**:
- `event` (string, optional): 事件名称，不传则移除所有事件的监听器

**返回值**: `EventBus` - 支持链式调用

**示例**:
```javascript
// 移除特定事件的所有监听器
mdacEventBus.removeAllListeners('dataUpdated');

// 移除所有事件的监听器
mdacEventBus.removeAllListeners();
```

### 预定义事件类型

```javascript
// 系统级事件
const SYSTEM_EVENTS = {
    READY: 'systemReady',
    ERROR: 'systemError',
    SHUTDOWN: 'systemShutdown'
};

// 数据事件
const DATA_EVENTS = {
    UPDATED: 'dataUpdated',
    SAVED: 'dataSaved',
    CLEARED: 'dataCleared',
    LOADED: 'dataLoaded'
};

// UI事件
const UI_EVENTS = {
    BUTTON_CLICKED: 'uiButtonClicked',
    FIELD_CHANGED: 'uiFieldChanged',
    PANEL_OPENED: 'uiPanelOpened',
    PANEL_CLOSED: 'uiPanelClosed'
};

// AI事件
const AI_EVENTS = {
    PARSING_START: 'aiParsingStart',
    PARSING_COMPLETE: 'aiParsingComplete',
    PARSING_ERROR: 'aiParsingError'
};
```

---

## 📝 Logger API

**文件位置**: `modules/logger.js`  
**全局实例**: `window.mdacLogger`

### 类：MDACLogger

统一的日志记录和性能监控系统。

#### 构造函数

```javascript
constructor(config = {})
```

创建新的Logger实例。

**参数**:
- `config` (object): 配置对象
  - `maxLogs` (number): 最大日志条数，默认1000
  - `level` (string): 日志级别，默认'info'
  - `enablePerformance` (boolean): 是否启用性能监控，默认true

**示例**:
```javascript
const logger = new MDACLogger({
    maxLogs: 500,
    level: 'debug',
    enablePerformance: true
});
```

#### 方法：log

```javascript
log(level, category, message, details = {})
```

记录日志信息。

**参数**:
- `level` (string): 日志级别 ('debug', 'info', 'warn', 'error')
- `category` (string): 日志分类
- `message` (string): 日志消息
- `details` (object): 附加详细信息

**返回值**: `void`

**示例**:
```javascript
// 基本日志记录
mdacLogger.log('info', 'UserAction', '用户点击了提交按钮');

// 带详细信息的日志
mdacLogger.log('error', 'APICall', '网络请求失败', {
    url: '/api/parse',
    status: 500,
    error: 'Internal Server Error'
});

// 调试日志
mdacLogger.log('debug', 'DataProcessing', '开始处理用户输入', {
    inputLength: 150,
    timestamp: Date.now()
});
```

#### 方法：startPerformance

```javascript
startPerformance(operationName)
```

开始性能监控计时。

**参数**:
- `operationName` (string): 操作名称

**返回值**: `void`

**示例**:
```javascript
mdacLogger.startPerformance('apiCall');
// 执行耗时操作
await callAPI();
mdacLogger.endPerformance('apiCall');
```

#### 方法：endPerformance

```javascript
endPerformance(operationName)
```

结束性能监控计时并记录耗时。

**参数**:
- `operationName` (string): 操作名称

**返回值**: `number` - 操作耗时（毫秒）

**示例**:
```javascript
mdacLogger.startPerformance('dataProcessing');

// 处理数据的代码
const result = await processLargeDataset();

const duration = mdacLogger.endPerformance('dataProcessing');
console.log(`数据处理耗时: ${duration}ms`);
```

#### 方法：getLogs

```javascript
getLogs(level = null, category = null, limit = null)
```

获取日志记录。

**参数**:
- `level` (string, optional): 筛选日志级别
- `category` (string, optional): 筛选日志分类
- `limit` (number, optional): 限制返回条数

**返回值**: `Array<LogEntry>` - 日志条目数组

**LogEntry结构**:
```javascript
{
    timestamp: string,    // ISO时间戳
    level: string,        // 日志级别
    category: string,     // 日志分类
    message: string,      // 日志消息
    details: object,      // 详细信息
    id: string           // 唯一标识
}
```

**示例**:
```javascript
// 获取所有日志
const allLogs = mdacLogger.getLogs();

// 获取错误日志
const errorLogs = mdacLogger.getLogs('error');

// 获取特定分类的最近10条日志
const apiLogs = mdacLogger.getLogs(null, 'APICall', 10);

// 获取警告级别的UI相关日志
const uiWarnings = mdacLogger.getLogs('warn', 'UI');
```

#### 方法：clearLogs

```javascript
clearLogs()
```

清除所有日志记录。

**返回值**: `void`

**示例**:
```javascript
mdacLogger.clearLogs();
console.log('日志已清除');
```

#### 方法：setLevel

```javascript
setLevel(level)
```

设置日志记录级别。

**参数**:
- `level` (string): 日志级别 ('debug', 'info', 'warn', 'error')

**返回值**: `void`

**示例**:
```javascript
// 设置为调试模式
mdacLogger.setLevel('debug');

// 生产环境只记录警告和错误
mdacLogger.setLevel('warn');
```

#### 方法：exportLogs

```javascript
exportLogs(format = 'json')
```

导出日志数据。

**参数**:
- `format` (string): 导出格式 ('json', 'csv', 'txt')

**返回值**: `string` - 格式化的日志数据

**示例**:
```javascript
// 导出JSON格式
const jsonLogs = mdacLogger.exportLogs('json');

// 导出CSV格式
const csvLogs = mdacLogger.exportLogs('csv');

// 保存到文件
const blob = new Blob([jsonLogs], { type: 'application/json' });
const url = URL.createObjectURL(blob);
// 下载文件逻辑...
```

---

## 🗄️ StateManager API

**文件位置**: `modules/state-manager.js`  
**全局实例**: `window.mdacStateManager`

### 类：StateManager

基于Chrome Storage API的状态管理系统，支持数据持久化和状态监听。

#### 构造函数

```javascript
constructor(config = {})
```

创建新的StateManager实例。

**参数**:
- `config` (object): 配置对象
  - `storageArea` (string): 存储区域 ('local', 'sync')，默认'local'
  - `prefix` (string): 键名前缀，默认'mdac_'
  - `autoSave` (boolean): 是否自动保存，默认true

#### 方法：setState

```javascript
async setState(key, value)
```

设置状态数据。

**参数**:
- `key` (string): 状态键名
- `value` (any): 状态值

**返回值**: `Promise<void>`

**示例**:
```javascript
// 保存个人信息
await mdacStateManager.setState('personalInfo', {
    name: 'Zhang San',
    passport: '*********',
    nationality: 'Chinese'
});

// 保存用户设置
await mdacStateManager.setState('userSettings', {
    language: 'zh-CN',
    theme: 'light',
    autoFill: true
});
```

#### 方法：getState

```javascript
async getState(key, defaultValue = null)
```

获取状态数据。

**参数**:
- `key` (string): 状态键名
- `defaultValue` (any): 默认值

**返回值**: `Promise<any>` - 状态值

**示例**:
```javascript
// 获取个人信息
const personalInfo = await mdacStateManager.getState('personalInfo');

// 获取用户设置，如果不存在则使用默认值
const settings = await mdacStateManager.getState('userSettings', {
    language: 'en-US',
    theme: 'light'
});

// 检查数据是否存在
if (personalInfo) {
    console.log('用户姓名:', personalInfo.name);
} else {
    console.log('未找到个人信息');
}
```

#### 方法：updateState

```javascript
async updateState(key, updates)
```

更新状态数据（合并操作）。

**参数**:
- `key` (string): 状态键名
- `updates` (object): 要更新的字段

**返回值**: `Promise<any>` - 更新后的完整状态

**示例**:
```javascript
// 只更新姓名字段
await mdacStateManager.updateState('personalInfo', {
    name: 'Li Si'
});

// 更新多个字段
await mdacStateManager.updateState('userSettings', {
    language: 'zh-CN',
    autoFill: false
});
```

#### 方法：clearState

```javascript
async clearState(key)
```

清除指定状态数据。

**参数**:
- `key` (string): 状态键名

**返回值**: `Promise<void>`

**示例**:
```javascript
// 清除个人信息
await mdacStateManager.clearState('personalInfo');

// 清除所有用户数据
await mdacStateManager.clearState('userSettings');
```

#### 方法：getAllStates

```javascript
async getAllStates()
```

获取所有状态数据。

**返回值**: `Promise<object>` - 包含所有状态的对象

**示例**:
```javascript
const allStates = await mdacStateManager.getAllStates();
console.log('所有状态数据:', allStates);

// 遍历所有状态
Object.entries(allStates).forEach(([key, value]) => {
    console.log(`${key}:`, value);
});
```

#### 方法：subscribe

```javascript
subscribe(key, callback)
```

订阅状态变化。

**参数**:
- `key` (string): 状态键名，使用'*'监听所有状态变化
- `callback` (function): 回调函数 `(newValue, oldValue, key) => {}`

**返回值**: `function` - 取消订阅函数

**示例**:
```javascript
// 监听个人信息变化
const unsubscribe = mdacStateManager.subscribe('personalInfo', (newValue, oldValue) => {
    console.log('个人信息已更新:', newValue);
    updateUI(newValue);
});

// 监听所有状态变化
mdacStateManager.subscribe('*', (newValue, oldValue, key) => {
    console.log(`状态 ${key} 已更新:`, newValue);
});

// 取消订阅
unsubscribe();
```

#### 方法：hasState

```javascript
async hasState(key)
```

检查状态是否存在。

**参数**:
- `key` (string): 状态键名

**返回值**: `Promise<boolean>` - 是否存在

**示例**:
```javascript
if (await mdacStateManager.hasState('personalInfo')) {
    console.log('个人信息已存在');
} else {
    console.log('需要用户输入个人信息');
}
```

---

## 🔍 FormFieldDetector API

**文件位置**: `modules/form-field-detector.js`  
**类名**: `FormFieldDetector`

### 类：FormFieldDetector

智能表单字段检测和分析系统。

#### 构造函数

```javascript
constructor(config = {})
```

创建表单字段检测器实例。

**参数**:
- `config` (object): 配置对象
  - `confidenceThreshold` (number): 置信度阈值，默认0.7
  - `enableDeepScan` (boolean): 是否启用深度扫描，默认true

#### 方法：detectFormFields

```javascript
detectFormFields(rootElement = document)
```

检测表单字段。

**参数**:
- `rootElement` (Element): 根元素，默认为document

**返回值**: `object` - 检测结果对象

**检测结果结构**:
```javascript
{
    name: {
        element: HTMLInputElement,
        confidence: 0.95,
        type: 'text',
        selector: '#full-name'
    },
    email: {
        element: HTMLInputElement,
        confidence: 0.90,
        type: 'email',
        selector: 'input[type="email"]'
    },
    // ... 其他字段
}
```

**示例**:
```javascript
const detector = new FormFieldDetector();
const fields = detector.detectFormFields();

// 检查是否检测到姓名字段
if (fields.name) {
    console.log('检测到姓名字段:', fields.name.selector);
    console.log('置信度:', fields.name.confidence);
}

// 遍历所有检测到的字段
Object.entries(fields).forEach(([fieldType, fieldInfo]) => {
    console.log(`${fieldType}: ${fieldInfo.selector} (${fieldInfo.confidence})`);
});
```

#### 方法：fillField

```javascript
fillField(fieldName, value, fields = null)
```

填充指定字段。

**参数**:
- `fieldName` (string): 字段名称
- `value` (string): 填充值
- `fields` (object): 字段检测结果，不传则自动检测

**返回值**: `boolean` - 是否填充成功

**示例**:
```javascript
const detector = new FormFieldDetector();

// 自动检测并填充
const success = detector.fillField('name', 'Zhang San');
if (success) {
    console.log('姓名字段填充成功');
}

// 使用预检测的字段结果
const fields = detector.detectFormFields();
detector.fillField('email', '<EMAIL>', fields);
```

#### 方法：fillMultipleFields

```javascript
fillMultipleFields(data, fields = null)
```

批量填充多个字段。

**参数**:
- `data` (object): 字段数据对象
- `fields` (object): 字段检测结果，不传则自动检测

**返回值**: `object` - 填充结果 `{ success: number, failed: number, details: array }`

**示例**:
```javascript
const detector = new FormFieldDetector();

const data = {
    name: 'Zhang San',
    email: '<EMAIL>',
    passport: '*********',
    nationality: 'Chinese'
};

const result = detector.fillMultipleFields(data);
console.log(`成功填充 ${result.success} 个字段，失败 ${result.failed} 个字段`);

// 查看详细结果
result.details.forEach(detail => {
    console.log(`${detail.field}: ${detail.success ? '成功' : '失败'}`);
});
```

#### 方法：validateDetection

```javascript
validateDetection(fields)
```

验证检测结果的质量。

**参数**:
- `fields` (object): 检测结果

**返回值**: `object` - 验证结果

**验证结果结构**:
```javascript
{
    valid: boolean,
    confidence: number,
    issues: string[],
    suggestions: string[]
}
```

**示例**:
```javascript
const detector = new FormFieldDetector();
const fields = detector.detectFormFields();
const validation = detector.validateDetection(fields);

if (validation.valid) {
    console.log('检测结果良好，置信度:', validation.confidence);
} else {
    console.log('检测质量问题:', validation.issues);
    console.log('改进建议:', validation.suggestions);
}
```

---

## 🤖 AI配置 API

**文件位置**: `config/ai-config.js`  
**全局对象**: `window.MDAC_AI_CONFIG`

### 对象：MDAC_AI_CONFIG

AI相关配置和提示词管理对象。

#### 属性：GEMINI_CONFIG

```javascript
GEMINI_CONFIG: {
    apiKey: string,
    model: string,
    endpoint: string,
    maxTokens: number,
    temperature: number
}
```

Gemini AI配置参数。

**示例**:
```javascript
const config = MDAC_AI_CONFIG.GEMINI_CONFIG;
console.log('使用模型:', config.model);
console.log('最大令牌数:', config.maxTokens);
```

#### 属性：AI_PROMPTS

```javascript
AI_PROMPTS: {
    PERSONAL_INFO_PARSING: string,
    TRAVEL_INFO_PARSING: string,
    PASSPORT_PARSING: string,
    // ... 其他提示词
}
```

AI提示词配置。

**示例**:
```javascript
// 获取个人信息解析提示词
const personalPrompt = MDAC_AI_CONFIG.AI_PROMPTS.PERSONAL_INFO_PARSING;

// 使用提示词进行API调用
const response = await callGeminiAPI(personalPrompt, userInput);
```

#### 属性：AI_CONTEXTS

```javascript
AI_CONTEXTS: {
    PROFESSIONAL: string,
    CASUAL: string,
    TECHNICAL: string,
    // ... 其他上下文
}
```

AI上下文配置。

#### 属性：AI_FEATURES

```javascript
AI_FEATURES: {
    PERSONAL_INFO_PARSING: object,
    TRAVEL_INFO_PARSING: object,
    ERROR_CORRECTION: object,
    // ... 其他功能配置
}
```

AI功能特性配置。

---

## 🎨 UI控制器 API

**文件位置**: `ui/ui-sidepanel-main.js`  
**类名**: `MDACMainController`

### 类：MDACMainController

侧边栏主控制器，管理用户界面和用户交互。

#### 构造函数

```javascript
constructor()
```

创建主控制器实例，自动初始化UI组件。

#### 方法：updateConnectionStatus

```javascript
updateConnectionStatus(status, message = '')
```

更新连接状态显示。

**参数**:
- `status` (string): 状态类型 ('connected', 'disconnected', 'connecting', 'error')
- `message` (string): 状态消息

**示例**:
```javascript
const controller = window.mdacMainController;

// 显示连接成功
controller.updateConnectionStatus('connected', 'MDAC系统已连接');

// 显示连接错误
controller.updateConnectionStatus('error', '无法连接到MDAC服务器');
```

#### 方法：updateToMDAC

```javascript
async updateToMDAC()
```

将数据发送到MDAC表单。

**返回值**: `Promise<boolean>` - 是否发送成功

**示例**:
```javascript
const success = await controller.updateToMDAC();
if (success) {
    console.log('数据发送成功');
} else {
    console.log('数据发送失败');
}
```

#### 方法：clearAllData

```javascript
async clearAllData()
```

清除所有用户数据。

**返回值**: `Promise<void>`

#### 方法：previewData

```javascript
previewData()
```

预览当前数据。

**示例**:
```javascript
// 显示数据预览对话框
controller.previewData();
```

#### 方法：parsePersonalInfo

```javascript
async parsePersonalInfo()
```

解析个人信息。

**返回值**: `Promise<object>` - 解析结果

#### 方法：parseTravelInfo

```javascript
async parseTravelInfo()
```

解析旅行信息。

**返回值**: `Promise<object>` - 解析结果

---

## 🔄 Background API

**文件位置**: `background/background.js`  
**全局实例**: `globalThis.mdacBackground`

### 类：MDACBackground

后台服务管理器，处理API调用和消息传递。

#### 方法：callGeminiAI

```javascript
async callGeminiAI(prompt, userInput, context = '')
```

调用Gemini AI API。

**参数**:
- `prompt` (string): AI提示词
- `userInput` (string): 用户输入
- `context` (string): 附加上下文

**返回值**: `Promise<object>` - API响应结果

**示例**:
```javascript
// 通过消息传递调用
chrome.runtime.sendMessage({
    action: 'callGeminiAI',
    prompt: MDAC_AI_CONFIG.AI_PROMPTS.PERSONAL_INFO_PARSING,
    userInput: '姓名：张三\n护照：*********',
    context: MDAC_AI_CONFIG.AI_CONTEXTS.PROFESSIONAL
}, (response) => {
    if (response.success) {
        console.log('AI解析结果:', response.data);
    } else {
        console.error('AI调用失败:', response.error);
    }
});
```

#### 方法：handleMessage

```javascript
handleMessage(message, sender, sendResponse)
```

处理来自其他脚本的消息。

**参数**:
- `message` (object): 消息对象
- `sender` (object): 发送者信息
- `sendResponse` (function): 响应函数

---

## 🛠️ 错误处理

### 统一错误类型

```javascript
class MDACError extends Error {
    constructor(message, code, details = {}) {
        super(message);
        this.name = 'MDACError';
        this.code = code;
        this.details = details;
        this.timestamp = new Date().toISOString();
    }
}
```

### 错误码定义

```javascript
const ERROR_CODES = {
    MODULE_LOAD_FAILED: 'MODULE_LOAD_FAILED',
    API_REQUEST_FAILED: 'API_REQUEST_FAILED',
    VALIDATION_FAILED: 'VALIDATION_FAILED',
    NETWORK_ERROR: 'NETWORK_ERROR',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    DATA_CORRUPTION: 'DATA_CORRUPTION'
};
```

### 错误处理示例

```javascript
try {
    const result = await mdacStateManager.setState('userData', data);
} catch (error) {
    if (error instanceof MDACError) {
        switch (error.code) {
            case ERROR_CODES.NETWORK_ERROR:
                console.log('网络错误，请检查连接');
                break;
            case ERROR_CODES.PERMISSION_DENIED:
                console.log('权限不足，请检查扩展设置');
                break;
            default:
                console.log('未知错误:', error.message);
        }
    } else {
        console.error('系统错误:', error);
    }
}
```

---

## 📊 使用示例

### 完整工作流程示例

```javascript
// 1. 等待系统准备就绪
mdacEventBus.once('systemReady', async () => {
    console.log('系统已就绪，开始工作流程');
    
    // 2. 检查是否有保存的数据
    const savedData = await mdacStateManager.getState('personalInfo');
    if (savedData) {
        console.log('发现已保存的数据:', savedData);
    }
    
    // 3. 监听数据更新
    mdacStateManager.subscribe('personalInfo', (newData) => {
        console.log('个人信息已更新:', newData);
        mdacEventBus.emit('dataUpdated', { type: 'personal', data: newData });
    });
    
    // 4. 检测表单字段
    const detector = new FormFieldDetector();
    const fields = detector.detectFormFields();
    console.log('检测到的字段:', Object.keys(fields));
    
    // 5. 记录操作日志
    mdacLogger.log('info', 'Workflow', '工作流程初始化完成');
});

// 触发系统准备事件
mdacEventBus.emit('systemReady');
```

### AI解析示例

```javascript
async function parseUserInput(inputText) {
    try {
        // 开始性能监控
        mdacLogger.startPerformance('aiParsing');
        
        // 发送解析开始事件
        mdacEventBus.emit('aiParsingStart', { text: inputText });
        
        // 调用AI API
        const response = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: MDAC_AI_CONFIG.AI_PROMPTS.PERSONAL_INFO_PARSING,
                userInput: inputText,
                context: MDAC_AI_CONFIG.AI_CONTEXTS.PROFESSIONAL
            }, (response) => {
                if (response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response.error));
                }
            });
        });
        
        // 保存解析结果
        await mdacStateManager.setState('personalInfo', response);
        
        // 结束性能监控
        const duration = mdacLogger.endPerformance('aiParsing');
        
        // 发送解析完成事件
        mdacEventBus.emit('aiParsingComplete', { 
            data: response, 
            duration: duration 
        });
        
        // 记录成功日志
        mdacLogger.log('info', 'AIParser', 'AI解析完成', {
            inputLength: inputText.length,
            outputFields: Object.keys(response).length,
            duration: duration
        });
        
        return response;
        
    } catch (error) {
        // 记录错误
        mdacLogger.log('error', 'AIParser', 'AI解析失败', {
            error: error.message,
            inputText: inputText.substring(0, 100) + '...'
        });
        
        // 发送错误事件
        mdacEventBus.emit('aiParsingError', { error: error.message });
        
        throw error;
    }
}
```

---

## 🔧 调试和测试

### API测试工具

```javascript
// 全局测试对象
window.MDACAPITester = {
    // 测试事件总线
    testEventBus() {
        console.log('测试事件总线...');
        
        let received = false;
        mdacEventBus.once('test', () => {
            received = true;
            console.log('✅ 事件总线工作正常');
        });
        
        mdacEventBus.emit('test');
        
        setTimeout(() => {
            if (!received) {
                console.error('❌ 事件总线测试失败');
            }
        }, 100);
    },
    
    // 测试状态管理器
    async testStateManager() {
        console.log('测试状态管理器...');
        
        try {
            const testData = { test: 'value', timestamp: Date.now() };
            
            await mdacStateManager.setState('test', testData);
            const retrieved = await mdacStateManager.getState('test');
            
            if (JSON.stringify(testData) === JSON.stringify(retrieved)) {
                console.log('✅ 状态管理器工作正常');
            } else {
                console.error('❌ 状态管理器数据不一致');
            }
            
            await mdacStateManager.clearState('test');
            
        } catch (error) {
            console.error('❌ 状态管理器测试失败:', error);
        }
    },
    
    // 测试表单检测器
    testFormDetector() {
        console.log('测试表单检测器...');
        
        try {
            const detector = new FormFieldDetector();
            const fields = detector.detectFormFields();
            
            console.log('✅ 表单检测器工作正常');
            console.log('检测到的字段:', Object.keys(fields));
            
        } catch (error) {
            console.error('❌ 表单检测器测试失败:', error);
        }
    },
    
    // 运行所有测试
    async runAllTests() {
        console.log('🧪 开始API测试...');
        
        this.testEventBus();
        await this.testStateManager();
        this.testFormDetector();
        
        console.log('🎉 API测试完成');
    }
};

// 使用方法：在控制台运行
// window.MDACAPITester.runAllTests();
```

---

## 📚 类型定义 (TypeScript风格)

```typescript
// 事件相关类型
interface EventListener {
    (data?: any): void;
}

interface EventBusInterface {
    on(event: string, listener: EventListener): EventBus;
    once(event: string, listener: EventListener): EventBus;
    off(event: string, listener: EventListener): EventBus;
    emit(event: string, ...args: any[]): boolean;
    removeAllListeners(event?: string): EventBus;
}

// 日志相关类型
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
    timestamp: string;
    level: LogLevel;
    category: string;
    message: string;
    details: object;
    id: string;
}

// 状态管理相关类型
interface StateManagerInterface {
    setState(key: string, value: any): Promise<void>;
    getState(key: string, defaultValue?: any): Promise<any>;
    updateState(key: string, updates: object): Promise<any>;
    clearState(key: string): Promise<void>;
    getAllStates(): Promise<object>;
    subscribe(key: string, callback: Function): Function;
    hasState(key: string): Promise<boolean>;
}

// 表单检测相关类型
interface FieldDetectionResult {
    element: HTMLInputElement;
    confidence: number;
    type: string;
    selector: string;
}

interface FormFields {
    [fieldName: string]: FieldDetectionResult;
}
```

---

## 🔗 相关文档

- [开发者指南](./DEVELOPER_GUIDE.md) - 开发环境搭建和工作流程
- [部署手册](./DEPLOYMENT_GUIDE.md) - 打包和发布流程
- [故障排除指南](./TROUBLESHOOTING_GUIDE.md) - 常见问题解决方案

---

**文档版本**: 1.0  
**最后更新**: 2025-07-15  
**维护者**: MDAC开发团队