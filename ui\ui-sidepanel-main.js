/**
 * MDAC AI智能助手 - 主控制器
 * 统一管理所有UI交互和核心功能
 * 创建日期: 2025-07-12
 * 版本: 3.0.0 (重构版本)
 */

(function() {
    'use strict';

    // 防止重复加载
    if (window.MDACMainController) {
        console.log('✅ [MDACMainController] 已存在，跳过重复加载');
        return;
    }

    class MDACMainController {
        constructor() {
            this.version = '3.1.0'; // 版本升级
            this.initialized = false;
            this.isInitialized = false; // 兼容统一架构
            this.data = {
                personal: {},
                travel: {},
                settings: {}
            };
            this.elements = {};
            this.aiConfig = null;
            this.cityData = null;
            this.fieldMappings = null;
            this.lastError = null; // 用于错误跟踪

            // 内存管理器 - 统一管理所有资源
            this.memoryManager = new window.PerformanceUtils.MemoryManager();

            // 错误处理器
            this.errorHandler = window.PerformanceUtils.ErrorHandler;

            // UI增强工具
            this.loadingManager = new window.UIEnhancements.LoadingManager();
            this.messageSystem = new window.UIEnhancements.MessageSystem();

            // 模块化管理器
            this.autoParseManager = null;
            this.citySearchManager = null;
            this.aiParseManager = null;

            // 自动解析功能相关变量
            this.autoParseTimeouts = {
                personal: null,
                travel: null
            };
            this.autoParseSettings = {
                personal: {
                    enabled: true,
                    delay: 1000, // 1秒延迟
                    minLength: 10 // 最少10个字符
                },
                travel: {
                    enabled: true,
                    delay: 1000,
                    minLength: 10
                }
            };
            this.autoParseCountdowns = {
                personal: null,
                travel: null
            };

            // 防抖函数缓存
            this.debouncedHandlers = {
                autoParsePersonal: null,
                autoParseTravel: null,
                updateFieldStatus: null
            };

            // 统一架构兼容接口
            this.systemType = 'traditional';
            this.capabilities = [
                'formFilling', 'dataCollection', 'aiIntegration',
                'stateManagement', 'errorHandling'
            ];

            // 初始化防抖函数
            this.initializeDebouncedHandlers();

            // 绑定页面卸载事件
            this.bindUnloadEvents();

            // 初始化模块化管理器
            this.initializeModules();
        }

        /**
         * 初始化模块化管理器
         */
        initializeModules() {
            try {
                // 初始化自动解析管理器
                if (window.AutoParseManager) {
                    this.autoParseManager = new window.AutoParseManager(
                        this.memoryManager,
                        this.errorHandler,
                        this.messageSystem
                    );
                    console.log('✅ [MDACMainController] 自动解析管理器初始化完成');
                }

                // 初始化城市搜索管理器
                if (window.CitySearchManager) {
                    this.citySearchManager = new window.CitySearchManager(
                        this.memoryManager,
                        this.errorHandler,
                        this.messageSystem
                    );
                    console.log('✅ [MDACMainController] 城市搜索管理器初始化完成');
                }

                // 初始化AI解析管理器
                if (window.AIParseManager) {
                    this.aiParseManager = new window.AIParseManager(
                        this.memoryManager,
                        this.errorHandler,
                        this.messageSystem,
                        this.loadingManager
                    );
                    console.log('✅ [MDACMainController] AI解析管理器初始化完成');
                }

                console.log('✅ [MDACMainController] 所有模块初始化完成');
            } catch (error) {
                console.error('❌ [MDACMainController] 模块初始化失败:', error);
            }
        }

        /**
         * 初始化防抖函数
         */
        initializeDebouncedHandlers() {
            const { debounce } = window.PerformanceUtils;

            // 自动解析防抖函数
            this.debouncedHandlers.autoParsePersonal = debounce(
                (inputElement) => this.handleAutoParseInputCore('personal', inputElement),
                100
            );

            this.debouncedHandlers.autoParseTravel = debounce(
                (inputElement) => this.handleAutoParseInputCore('travel', inputElement),
                100
            );

            // 字段状态更新防抖函数
            this.debouncedHandlers.updateFieldStatus = debounce(
                () => this.updateFieldStatusCore(),
                200
            );

            console.log('✅ [MDACMainController] 防抖函数初始化完成');
        }

        /**
         * 绑定页面卸载事件
         */
        bindUnloadEvents() {
            // 页面卸载时清理资源
            const cleanup = () => {
                this.cleanup();
            };

            // 使用内存管理器添加事件监听器
            this.memoryManager.addListener(window, 'beforeunload', cleanup);
            this.memoryManager.addListener(window, 'unload', cleanup);
            this.memoryManager.addListener(document, 'visibilitychange', () => {
                if (document.hidden) {
                    // 页面隐藏时也进行清理
                    this.cleanup();
                }
            });

            console.log('✅ [MDACMainController] 页面卸载事件绑定完成');
        }

        /**
         * 清理所有资源 - 防止内存泄漏
         */
        cleanup() {
            if (this.memoryManager && this.memoryManager.isDestroyed) {
                return; // 避免重复清理
            }

            console.log('🧹 [MDACMainController] 开始清理资源...');

            try {
                // 取消所有自动解析
                this.cancelAllAutoParse();

                // 清理内存管理器中的所有资源
                if (this.memoryManager) {
                    this.memoryManager.cleanup();
                }

                // 清理其他资源
                this.data = null;
                this.elements = null;
                this.debouncedHandlers = null;

                console.log('✅ [MDACMainController] 资源清理完成');
            } catch (error) {
                console.error('❌ [MDACMainController] 资源清理失败:', error);
            }
        }

        /**
         * 取消所有自动解析
         */
        cancelAllAutoParse() {
            if (this.autoParseTimeouts) {
                Object.keys(this.autoParseTimeouts).forEach(type => {
                    this.cancelAutoParse(type);
                });
            }
        }

        /**
         * 初始化主控制器
         */
        async initialize() {
            try {
                console.log('🚀 [MDACMainController] 开始初始化...');
                
                // 1. 等待DOM加载完成
                if (document.readyState === 'loading') {
                    await new Promise(resolve => {
                        document.addEventListener('DOMContentLoaded', resolve);
                    });
                }

                // 2. 获取所有UI元素
                this.cacheElements();

                // 3. 加载配置和数据
                await this.loadConfigurations();

                // 4. 设置事件监听器
                this.setupEventListeners();

                // 5. 初始化UI状态
                this.initializeUI();

                // 6. 加载保存的数据
                await this.loadSavedData();
                await this.loadPresetData();

                this.initialized = true;
                this.isInitialized = true; // 兼容统一架构
                
                // 通知统一架构系统已就绪
                this.notifyUnifiedArchitecture();
                
                this.updateConnectionStatus('connected', '🟢 MDAC AI助手已就绪');
                console.log('✅ [MDACMainController] 初始化完成');

                // 显示成功消息
                this.showMessage('success', 'MDAC AI助手已成功加载！');

            } catch (error) {
                console.error('❌ [MDACMainController] 初始化失败:', error);
                this.updateConnectionStatus('error', '🔴 初始化失败');
                this.showMessage('error', '初始化失败: ' + error.message);
            }
        }

        /**
         * 缓存所有UI元素
         */
        cacheElements() {
            console.log('📦 [MDACMainController] 缓存UI元素...');

            // 状态元素
            this.elements.connectionStatus = document.getElementById('connectionStatus');
            this.elements.aiStatus = document.getElementById('aiStatus');

            // 按钮元素
            this.elements.mdacAccessBtn = document.getElementById('mdacAccessBtn');
            this.elements.imageUploadBtn = document.getElementById('imageUploadBtn');
            this.elements.imageInput = document.getElementById('imageInput');
            this.elements.clearAllBtn = document.getElementById('clearAllBtn');
            this.elements.previewBtn = document.getElementById('previewBtn');
            this.elements.updateToMDACBtn = document.getElementById('updateToMDACBtn');

            // 解析相关元素
            this.elements.personalInfoInput = document.getElementById('personalInfoInput');
            this.elements.travelInfoInput = document.getElementById('travelInfoInput');
            this.elements.parsePersonalBtn = document.getElementById('parsePersonalBtn');
            this.elements.parseTravelBtn = document.getElementById('parseTravelBtn');
            this.elements.parseAddressBtn = document.getElementById('parseAddressBtn');

            // 自动解析相关元素
            this.elements.autoParsePersonalEnabled = document.getElementById('autoParsePersonalEnabled');
            this.elements.autoParseTravel_Enabled = document.getElementById('autoParseTravel_Enabled');
            this.elements.autoParsePersonalStatus = document.getElementById('autoParsePersonalStatus');
            this.elements.autoParseTravel_Status = document.getElementById('autoParseTravel_Status');
            this.elements.personalCountdownText = document.getElementById('personalCountdownText');
            this.elements.travelCountdownText = document.getElementById('travelCountdownText');
            this.elements.cancelAutoParsePersonal = document.getElementById('cancelAutoParsePersonal');
            this.elements.cancelAutoParseTravel = document.getElementById('cancelAutoParseTravel');

            // 表单字段元素
            this.elements.formFields = {
                // 个人信息字段
                name: document.getElementById('name'),
                passportNo: document.getElementById('passportNo'),
                dateOfBirth: document.getElementById('dateOfBirth'),
                nationality: document.getElementById('nationality'),
                sex: document.getElementById('sex'),
                passportExpiry: document.getElementById('passportExpiry'),
                
                // 旅行信息字段 - 修正了MDAC网站的实际字段ID
                arrivalDate: document.getElementById('arrDt'), // 修正：到达日期的正确字段ID
                departureDate: document.getElementById('depDt'), // 修正：离开日期的正确字段ID
                flightNo: document.getElementById('flightNo'),
                modeOfTravel: document.getElementById('modeOfTravel'),
                accommodation: document.getElementById('accommodation'),
                address: document.getElementById('address'),
                state: document.getElementById('state'),
                city: document.getElementById('city'),
                postcode: document.getElementById('postcode')
            };

            // 预设字段
            this.elements.presetEmail = document.getElementById('presetEmail');
            this.elements.presetPhone = document.getElementById('presetPhone');


            // 模态框元素
            this.elements.modal = document.getElementById('modal');
            this.elements.modalTitle = document.getElementById('modalTitle');
            this.elements.modalBody = document.getElementById('modalBody');
            this.elements.modalClose = document.getElementById('modalClose');
            this.elements.modalCancel = document.getElementById('modalCancel');
            this.elements.modalConfirm = document.getElementById('modalConfirm');

            console.log('✅ [MDACMainController] UI元素缓存完成');
        }

        /**
         * 加载配置和数据文件
         */
        async loadConfigurations() {
            console.log('📋 [MDACMainController] 加载配置文件...');

            // 定义扩展URL，供后续配置文件加载使用
            const extensionUrl = chrome.runtime ? chrome.runtime.getURL('') : '../';

            try {
                // 加载AI配置 - 使用CSP安全的script标签方式
                try {
                    console.log('🔧 [MDACMainController] 开始加载AI配置...');

                    // 使用安全的script标签加载方式，避免CSP违规
                    const configLoaded = await this.loadConfigScript('config/ai-config.js');

                    if (configLoaded) {
                        // 验证配置是否正确加载到全局作用域
                        await this.waitForConfigLoad();

                        if (window.MDAC_AI_CONFIG) {
                            this.aiConfig = { loaded: true, source: 'ai-config.js' };
                            console.log('✅ [MDACMainController] AI配置加载成功');
                            console.log('🔍 [MDACMainController] 配置验证:', {
                                MDAC_AI_CONFIG: !!window.MDAC_AI_CONFIG,
                                GEMINI_CONFIG: !!window.GEMINI_CONFIG,
                                AI_PROMPTS: !!window.AI_PROMPTS,
                                AI_CONTEXTS: !!window.AI_CONTEXTS,
                                AI_FEATURES: !!window.AI_FEATURES
                            });
                        } else {
                            throw new Error('AI配置对象未正确加载到全局作用域');
                        }
                    } else {
                        // 如果基础配置失败，尝试增强配置
                        console.log('⚠️ [MDACMainController] 基础配置加载失败，尝试增强配置...');
                        const enhancedConfigLoaded = await this.loadConfigScript('config/enhanced-ai-config.js');

                        if (enhancedConfigLoaded) {
                            await this.waitForConfigLoad();

                            if (window.MDAC_AI_CONFIG) {
                                this.aiConfig = { loaded: true, source: 'enhanced-ai-config.js' };
                                console.log('✅ [MDACMainController] 增强AI配置加载成功');
                            } else {
                                throw new Error('增强AI配置对象未正确加载到全局作用域');
                            }
                        } else {
                            throw new Error('所有AI配置文件加载失败');
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ [MDACMainController] AI配置加载失败:', error);
                    this.aiConfig = { loaded: false, error: error.message };

                    // 创建降级配置，确保基本功能可用
                    this.createFallbackAIConfig();
                }

                // 加载城市数据
                try {
                    const cityDataResponse = await fetch(extensionUrl + 'config/malaysia-states-cities.json');
                    if (cityDataResponse.ok) {
                        this.cityData = await cityDataResponse.json();
                        console.log('✅ 城市数据加载成功，包含', Object.keys(this.cityData).length, '个州属');
                    }
                } catch (error) {
                    console.warn('⚠️ 城市数据加载失败:', error);
                    this.cityData = this.getDefaultCityData();
                }

                // 加载字段映射
                try {
                    const mappingResponse = await fetch(extensionUrl + 'config/mdac-official-mappings.json');
                    if (mappingResponse.ok) {
                        this.fieldMappings = await mappingResponse.json();
                        console.log('✅ 字段映射加载成功');
                    }
                } catch (error) {
                    console.warn('⚠️ 字段映射加载失败:', error);
                    this.fieldMappings = this.getDefaultFieldMappings();
                }

                // 整合两个数据源
                this.integrateCityDataSources();

            } catch (error) {

            } catch (error) {
                console.warn('⚠️ 配置文件加载失败:', error);
                // 使用默认配置
                this.aiConfig = { loaded: false };
                this.cityData = this.getDefaultCityData();
                this.fieldMappings = this.getDefaultFieldMappings();
            }

            console.log('✅ [MDACMainController] 配置加载完成');
        }

        /**
         * 整合城市数据源
         * 将malaysia-states-cities.json和mdac-official-mappings.json的数据整合
         */
        integrateCityDataSources() {
            console.log('🔄 [MDACMainController] 整合城市数据源...');

            try {
                if (!this.cityData) {
                    this.cityData = {};
                }

                // 如果有字段映射中的城市数据，将其整合到主城市数据中
                if (this.fieldMappings && this.fieldMappings.cities) {
                    const mappingCities = this.fieldMappings.cities;

                    Object.keys(mappingCities).forEach(cityKey => {
                        const cityInfo = mappingCities[cityKey];
                        const stateCode = cityInfo.state;

                        if (stateCode) {
                            // 确保州属存在
                            if (!this.cityData[stateCode]) {
                                this.cityData[stateCode] = [];
                            }

                            // 检查城市是否已存在
                            const existingCity = this.cityData[stateCode].find(city =>
                                city.code === cityInfo.code || city.name === cityInfo.name
                            );

                            if (!existingCity) {
                                this.cityData[stateCode].push({
                                    code: cityInfo.code || cityKey,
                                    name: cityInfo.name || cityKey,
                                    nameEn: cityInfo.nameEn || cityInfo.name || cityKey
                                });
                            }
                        }
                    });
                }

                // 为每个州属的城市列表排序
                Object.keys(this.cityData).forEach(stateCode => {
                    if (Array.isArray(this.cityData[stateCode])) {
                        this.cityData[stateCode].sort((a, b) => {
                            const nameA = a.nameEn || a.name || a;
                            const nameB = b.nameEn || b.name || b;
                            return nameA.localeCompare(nameB);
                        });
                    }
                });

                console.log('✅ [MDACMainController] 城市数据源整合完成');
            } catch (error) {
                console.error('❌ 整合城市数据源失败:', error);
            }
        }

        /**
         * 使用CSP安全的方式加载配置脚本
         * @param {string} scriptPath - 脚本文件路径
         * @returns {Promise<boolean>} 加载是否成功
         */
        loadConfigScript(scriptPath) {
            return new Promise((resolve) => {
                console.log(`🔧 [MDACMainController] 开始加载配置脚本: ${scriptPath}`);

                // 创建script标签
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = chrome.runtime.getURL(scriptPath);

                // 设置超时处理
                const timeout = setTimeout(() => {
                    console.warn(`⏰ [MDACMainController] 配置脚本加载超时: ${scriptPath}`);
                    resolve(false);
                }, 10000); // 10秒超时

                // 设置加载成功回调
                script.onload = () => {
                    clearTimeout(timeout);
                    console.log(`✅ [MDACMainController] 配置脚本加载成功: ${scriptPath}`);
                    resolve(true);
                };

                // 设置加载失败回调
                script.onerror = (error) => {
                    clearTimeout(timeout);
                    console.error(`❌ [MDACMainController] 配置脚本加载失败: ${scriptPath}`, error);
                    resolve(false); // 不reject，让调用者决定如何处理
                };

                // 将script标签添加到head中
                document.head.appendChild(script);
            });
        }

        /**
         * 等待AI配置加载完成
         * @param {number} maxWaitTime - 最大等待时间（毫秒）
         * @returns {Promise<boolean>} 配置是否成功加载
         */
        async waitForConfigLoad(maxWaitTime = 5000) {
            const startTime = Date.now();

            console.log('⏳ [MDACMainController] 等待AI配置加载完成...');

            while (Date.now() - startTime < maxWaitTime) {
                // 检查主要配置对象是否已加载
                if (window.MDAC_AI_CONFIG &&
                    window.GEMINI_CONFIG &&
                    window.AI_PROMPTS &&
                    window.AI_CONTEXTS &&
                    window.AI_FEATURES) {

                    console.log('✅ [MDACMainController] AI配置已完全加载到全局作用域');
                    return true;
                }

                // 等待100ms后重试
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            console.warn('⚠️ [MDACMainController] AI配置加载超时');
            return false;
        }

        /**
         * 创建降级AI配置，确保基本功能可用
         */
        createFallbackAIConfig() {
            console.log('🔧 [MDACMainController] 创建降级AI配置...');

            // 创建基础的AI配置对象
            if (!window.MDAC_AI_CONFIG) {
                window.MDAC_AI_CONFIG = {
                    GEMINI_CONFIG: {
                        DEFAULT_API_KEY: '',
                        DEFAULT_MODEL: 'gemini-1.5-flash',
                        API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models'
                    },
                    AI_PROMPTS: {
                        CONTENT_PARSING: '请解析以下内容并提取相关信息：{content}',
                        FORM_OPTIMIZATION: '请分析以下表单数据并提供优化建议：{formData}'
                    },
                    AI_CONTEXTS: {
                        FORM_AUDITOR: '你是一个表单数据审核专家，请提供专业的建议。',
                        CONTENT_PARSER: '你是一个内容解析专家，请提取关键信息。'
                    },
                    AI_FEATURES: {
                        CONTENT_PARSING: { enabled: true },
                        FORM_OPTIMIZATION: { enabled: true }
                    }
                };
            }

            // 同时设置向后兼容的全局变量
            if (!window.GEMINI_CONFIG) {
                window.GEMINI_CONFIG = window.MDAC_AI_CONFIG.GEMINI_CONFIG;
            }
            if (!window.AI_PROMPTS) {
                window.AI_PROMPTS = window.MDAC_AI_CONFIG.AI_PROMPTS;
            }
            if (!window.AI_CONTEXTS) {
                window.AI_CONTEXTS = window.MDAC_AI_CONFIG.AI_CONTEXTS;
            }
            if (!window.AI_FEATURES) {
                window.AI_FEATURES = window.MDAC_AI_CONFIG.AI_FEATURES;
            }

            console.log('✅ [MDACMainController] 降级AI配置创建完成');
            this.aiConfig = { loaded: true, fallback: true };
        }

        /**
         * 获取默认城市数据
         */
        getDefaultCityData() {
            return {
                "Kuala Lumpur": [
                    { name: "Kuala Lumpur", code: "KL", postcode: "50000" }
                ],
                "Selangor": [
                    { name: "Shah Alam", code: "SA", postcode: "40000" },
                    { name: "Petaling Jaya", code: "PJ", postcode: "46000" }
                ],
                "Penang": [
                    { name: "George Town", code: "GT", postcode: "10000" }
                ],
                "Johor": [
                    { name: "Johor Bahru", code: "JB", postcode: "80000" }
                ]
            };
        }

        /**
         * 获取默认字段映射
         */
        getDefaultFieldMappings() {
            return {
                name: "fullName",
                passportNo: "passportNumber",
                dateOfBirth: "birthDate",
                nationality: "nationality",
                sex: "gender",
                passportExpiry: "passExpDte", // 修正：护照到期日期的正确字段ID
                arrivalDate: "arrDt", // 修正：到达日期的正确字段ID
                departureDate: "depDt", // 修正：离开日期的正确字段ID
                flightNo: "flightNumber",
                address: "accommodationAddress",
                state: "state",
                city: "city",
                postcode: "postcode"
            };
        }

        /**
         * 设置所有事件监听器
         */
        setupEventListeners() {
            console.log('🎯 [MDACMainController] 设置事件监听器...');

            // MDAC网站访问按钮
            if (this.elements.mdacAccessBtn) {
                this.elements.mdacAccessBtn.addEventListener('click', () => {
                    this.openMDACWebsite();
                });
            }

            // 图片上传按钮
            if (this.elements.imageUploadBtn) {
                this.elements.imageUploadBtn.addEventListener('click', () => {
                    this.elements.imageInput?.click();
                });
            }

            if (this.elements.imageInput) {
                this.elements.imageInput.addEventListener('change', (e) => {
                    this.handleImageUpload(e);
                });
            }

            // AI解析按钮
            if (this.elements.parsePersonalBtn) {
                this.elements.parsePersonalBtn.addEventListener('click', () => {
                    this.parsePersonalInfo();
                });
            }

            if (this.elements.parseTravelBtn) {
                this.elements.parseTravelBtn.addEventListener('click', () => {
                    this.parseTravelInfo();
                });
            }

            if (this.elements.parseAddressBtn) {
                this.elements.parseAddressBtn.addEventListener('click', () => {
                    this.parseAddressInfo();
                });
            }

            // 设置自动解析监听器
            this.setupAutoParseListeners();

            // 设置州属城市联动监听器
            this.setupStateChangeListener();

            // 主要操作按钮
            if (this.elements.updateToMDACBtn) {
                this.elements.updateToMDACBtn.addEventListener('click', () => {
                    this.updateToMDAC();
                });
            }

            if (this.elements.clearAllBtn) {
                this.elements.clearAllBtn.addEventListener('click', () => {
                    this.clearAllData();
                });
            }

            if (this.elements.previewBtn) {
                this.elements.previewBtn.addEventListener('click', () => {
                    this.showDataPreview();
                });
            }


            // 模态框
            if (this.elements.modalClose) {
                this.elements.modalClose.addEventListener('click', () => {
                    this.hideModal();
                });
            }

            if (this.elements.modalCancel) {
                this.elements.modalCancel.addEventListener('click', () => {
                    this.hideModal();
                });
            }

            // 表单字段变化监听
            Object.values(this.elements.formFields).forEach(field => {
                if (field) {
                    field.addEventListener('input', () => {
                        this.updateFieldStatus();
                        this.autoSaveData(); // 自动保存
                    });
                }
            });

            // 预设字段监听
            if (this.elements.presetEmail) {
                this.elements.presetEmail.addEventListener('input', () => {
                    this.savePresetData();
                });
            }

            if (this.elements.presetPhone) {
                this.elements.presetPhone.addEventListener('input', () => {
                    this.savePresetData();
                });
            }


            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 's':
                            e.preventDefault();
                            this.saveData();
                            break;
                        case 'Enter':
                            if (e.shiftKey) {
                                e.preventDefault();
                                this.updateToMDAC();
                            }
                            break;
                    }
                }
            });

            console.log('✅ [MDACMainController] 事件监听器设置完成');
        }

        /**
         * 设置自动解析监听器 - 使用内存管理器
         */
        setupAutoParseListeners() {
            console.log('🤖 [MDACMainController] 设置自动解析监听器...');

            try {
                // 个人信息输入框自动解析
                if (this.elements.personalInfoInput) {
                    const personalInputHandler = (e) => {
                        this.handleAutoParseInput('personal', e.target);
                    };
                    this.memoryManager.addListener(this.elements.personalInfoInput, 'input', personalInputHandler);
                }

                // 旅行信息输入框自动解析
                if (this.elements.travelInfoInput) {
                    const travelInputHandler = (e) => {
                        this.handleAutoParseInput('travel', e.target);
                    };
                    this.memoryManager.addListener(this.elements.travelInfoInput, 'input', travelInputHandler);
                }

                // 自动解析开关监听
                if (this.elements.autoParsePersonalEnabled) {
                    const personalToggleHandler = (e) => {
                        this.autoParseSettings.personal.enabled = e.target.checked;
                        if (!e.target.checked) {
                            this.cancelAutoParse('personal');
                        }
                    };
                    this.memoryManager.addListener(this.elements.autoParsePersonalEnabled, 'change', personalToggleHandler);
                }

                if (this.elements.autoParseTravel_Enabled) {
                    const travelToggleHandler = (e) => {
                        this.autoParseSettings.travel.enabled = e.target.checked;
                        if (!e.target.checked) {
                            this.cancelAutoParse('travel');
                        }
                    };
                    this.memoryManager.addListener(this.elements.autoParseTravel_Enabled, 'change', travelToggleHandler);
                }

                // 取消自动解析按钮
                if (this.elements.cancelAutoParsePersonal) {
                    const cancelPersonalHandler = () => {
                        this.cancelAutoParse('personal');
                    };
                    this.memoryManager.addListener(this.elements.cancelAutoParsePersonal, 'click', cancelPersonalHandler);
                }

                if (this.elements.cancelAutoParseTravel) {
                    const cancelTravelHandler = () => {
                        this.cancelAutoParse('travel');
                    };
                    this.memoryManager.addListener(this.elements.cancelAutoParseTravel, 'click', cancelTravelHandler);
                }

                console.log('✅ [MDACMainController] 自动解析监听器设置完成');
            } catch (error) {
                this.errorHandler.handle(error, 'SetupAutoParseListeners', '自动解析监听器设置失败', this.showMessage.bind(this));
            }
        }

        /**
         * 设置州属城市联动监听器
         */
        setupStateChangeListener() {
            console.log('🌏 [MDACMainController] 设置州属城市联动监听器...');

            // 查找州属选择字段
            const stateField = document.getElementById('accommodationState') ||
                             document.querySelector('select[name="accommodationState"]');

            if (stateField) {
                stateField.addEventListener('change', (e) => {
                    const selectedState = e.target.value;
                    console.log(`🌏 州属选择变更: ${selectedState}`);
                    this.updateCityOptions(selectedState);
                });
                console.log('✅ 州属选择监听器已设置');
            } else {
                console.warn('⚠️ 未找到州属选择字段');
            }

            console.log('✅ [MDACMainController] 州属城市联动监听器设置完成');
        }

        /**
         * 更新城市选项
         * @param {string} stateCode - 州属代码
         */
        updateCityOptions(stateCode) {
            console.log(`🏙️ [MDACMainController] 更新城市选项: ${stateCode}`);

            const cityField = document.getElementById('accommodationCity') ||
                            document.querySelector('select[name="accommodationCity"]');

            if (!cityField) {
                console.warn('⚠️ 未找到城市选择字段');
                return;
            }

            // 清空当前选项
            cityField.innerHTML = '<option value="">选择城市</option>';

            if (!stateCode || !this.cityData) {
                console.log('🏙️ 州属未选择或城市数据未加载，跳过更新');
                return;
            }

            // 获取州属对应的城市列表
            const cities = this.getCitiesForState(stateCode);

            if (cities && cities.length > 0) {
                console.log(`🏙️ 找到 ${cities.length} 个城市`);
                this.populateCityDropdown(cityField, cities);
            } else {
                console.warn(`⚠️ 未找到州属 ${stateCode} 的城市数据`);
            }
        }

        /**
         * 获取指定州属的城市列表
         * @param {string} stateCode - 州属代码
         * @returns {Array} 城市列表
         */
        getCitiesForState(stateCode) {
            try {
                // 首先尝试从malaysia-states-cities.json获取
                if (this.cityData && this.cityData[stateCode]) {
                    return this.cityData[stateCode];
                }

                // 如果没有找到，尝试从mdac-official-mappings.json获取
                if (this.fieldMappings && this.fieldMappings.cities) {
                    const cityMappings = this.fieldMappings.cities;
                    return Object.keys(cityMappings).filter(city =>
                        cityMappings[city].state === stateCode
                    ).map(city => ({
                        code: cityMappings[city].code || city,
                        name: cityMappings[city].name || city,
                        nameEn: cityMappings[city].nameEn || city
                    }));
                }

                return [];
            } catch (error) {
                console.error('❌ 获取城市列表失败:', error);
                return [];
            }
        }

        /**
         * 填充城市下拉框
         * @param {HTMLSelectElement} cityField - 城市选择字段
         * @param {Array} cities - 城市列表
         */
        populateCityDropdown(cityField, cities) {
            try {
                cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.code || city.name || city;
                    option.textContent = city.nameEn || city.name || city;

                    // 添加中文名称作为数据属性，用于搜索
                    if (city.name && city.name !== city.nameEn) {
                        option.setAttribute('data-name-zh', city.name);
                    }

                    cityField.appendChild(option);
                });

                // 添加模糊搜索功能
                this.addCitySearchFunctionality(cityField);

                console.log(`✅ 城市选项填充完成，共 ${cities.length} 个选项`);
            } catch (error) {
                console.error('❌ 填充城市下拉框失败:', error);
            }
        }

        /**
         * 添加城市模糊搜索功能
         * @param {HTMLSelectElement} cityField - 城市选择字段
         */
        addCitySearchFunctionality(cityField) {
            try {
                if (!cityField) {
                    console.warn('⚠️ [MDACMainController] 城市字段不存在，无法添加搜索功能');
                    return;
                }

                console.log('🔍 [MDACMainController] 开始添加城市模糊搜索功能...');

                // 创建搜索容器
                const searchContainer = this.createSearchableDropdown(cityField);

                // 替换原有的select元素
                cityField.parentNode.replaceChild(searchContainer, cityField);

                console.log('✅ [MDACMainController] 城市模糊搜索功能添加完成');
            } catch (error) {
                this.errorHandler.handle(error, 'CitySearchSetup', '城市搜索功能设置失败', this.showMessage.bind(this));
            }
        }

        /**
         * 创建可搜索的下拉框
         * @param {HTMLSelectElement} originalSelect - 原始select元素
         * @returns {HTMLElement} 搜索容器
         */
        createSearchableDropdown(originalSelect) {
            // 创建容器
            const container = document.createElement('div');
            container.className = 'searchable-dropdown';
            container.style.position = 'relative';

            // 创建搜索输入框
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.className = 'input-base field-input searchable-input';
            searchInput.placeholder = '搜索城市...';
            searchInput.id = originalSelect.id;

            // 创建下拉列表
            const dropdown = document.createElement('div');
            dropdown.className = 'searchable-dropdown-list hidden';
            dropdown.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                max-height: 200px;
                overflow-y: auto;
                background: white;
                border: 1px solid var(--border-color);
                border-top: none;
                border-radius: 0 0 var(--border-radius) var(--border-radius);
                z-index: 1000;
                box-shadow: var(--shadow-md);
            `;

            // 获取当前州属的城市数据
            const currentState = this.getCurrentSelectedState();
            const cities = this.getCitiesForState(currentState);

            // 搜索功能
            const searchHandler = (e) => {
                const query = e.target.value.toLowerCase().trim();
                this.updateDropdownOptions(dropdown, cities, query, searchInput);
            };

            // 使用防抖优化搜索
            const debouncedSearch = window.PerformanceUtils.debounce(searchHandler, 200);
            this.memoryManager.addListener(searchInput, 'input', debouncedSearch);

            // 焦点事件
            const focusHandler = () => {
                dropdown.classList.remove('hidden');
                this.updateDropdownOptions(dropdown, cities, '', searchInput);
            };
            this.memoryManager.addListener(searchInput, 'focus', focusHandler);

            // 失焦事件（延迟隐藏，允许点击选项）
            const blurHandler = () => {
                setTimeout(() => {
                    dropdown.classList.add('hidden');
                }, 200);
            };
            this.memoryManager.addListener(searchInput, 'blur', blurHandler);

            // 组装容器
            container.appendChild(searchInput);
            container.appendChild(dropdown);

            return container;
        }

        /**
         * 更新下拉选项
         * @param {HTMLElement} dropdown - 下拉容器
         * @param {Array} cities - 城市数据
         * @param {string} query - 搜索查询
         * @param {HTMLInputElement} input - 输入框
         */
        updateDropdownOptions(dropdown, cities, query, input) {
            try {
                // 清空现有选项
                dropdown.innerHTML = '';

                // 过滤城市
                const filteredCities = this.fuzzySearchCities(query, cities);

                // 限制显示数量
                const maxResults = 50;
                const citiesToShow = filteredCities.slice(0, maxResults);

                if (citiesToShow.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'dropdown-item no-results';
                    noResults.textContent = '未找到匹配的城市';
                    noResults.style.cssText = `
                        padding: var(--spacing-sm);
                        color: var(--text-muted);
                        font-style: italic;
                    `;
                    dropdown.appendChild(noResults);
                    return;
                }

                // 创建选项
                citiesToShow.forEach(city => {
                    const option = document.createElement('div');
                    option.className = 'dropdown-item';
                    option.style.cssText = `
                        padding: var(--spacing-sm);
                        cursor: pointer;
                        border-bottom: 1px solid var(--border-color);
                        transition: background-color 0.2s ease;
                    `;

                    // 高亮匹配文本
                    const displayText = this.highlightMatch(city.nameEn || city.name, query);
                    option.innerHTML = `
                        <div class="city-name">${displayText}</div>
                        <div class="city-details" style="font-size: 12px; color: var(--text-secondary);">
                            ${city.name !== city.nameEn ? city.name : ''} | ${city.code}
                        </div>
                    `;

                    // 点击选择
                    const selectHandler = () => {
                        input.value = city.nameEn || city.name;
                        input.setAttribute('data-city-code', city.code);
                        dropdown.classList.add('hidden');

                        // 触发change事件
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                    };
                    this.memoryManager.addListener(option, 'click', selectHandler);

                    // 悬停效果
                    const mouseEnterHandler = () => {
                        option.style.backgroundColor = 'var(--bg-tertiary)';
                    };
                    const mouseLeaveHandler = () => {
                        option.style.backgroundColor = '';
                    };
                    this.memoryManager.addListener(option, 'mouseenter', mouseEnterHandler);
                    this.memoryManager.addListener(option, 'mouseleave', mouseLeaveHandler);

                    dropdown.appendChild(option);
                });

            } catch (error) {
                console.error('❌ 更新下拉选项失败:', error);
            }
        }

        /**
         * 模糊搜索城市
         * @param {string} query - 搜索查询
         * @param {Array} cities - 城市数据
         * @returns {Array} 过滤后的城市列表
         */
        fuzzySearchCities(query, cities) {
            if (!query || !cities) return cities || [];

            const normalizedQuery = query.toLowerCase();

            return cities.filter(city => {
                const nameMatch = (city.name || '').toLowerCase().includes(normalizedQuery);
                const nameEnMatch = (city.nameEn || '').toLowerCase().includes(normalizedQuery);
                const codeMatch = (city.code || '').toLowerCase().includes(normalizedQuery);

                return nameMatch || nameEnMatch || codeMatch;
            }).sort((a, b) => {
                // 优先显示完全匹配的结果
                const aNameEn = (a.nameEn || '').toLowerCase();
                const bNameEn = (b.nameEn || '').toLowerCase();

                const aExact = aNameEn.startsWith(normalizedQuery);
                const bExact = bNameEn.startsWith(normalizedQuery);

                if (aExact && !bExact) return -1;
                if (!aExact && bExact) return 1;

                // 按字母顺序排序
                return aNameEn.localeCompare(bNameEn);
            });
        }

        /**
         * 高亮匹配文本
         * @param {string} text - 原始文本
         * @param {string} query - 搜索查询
         * @returns {string} 高亮后的HTML
         */
        highlightMatch(text, query) {
            if (!query || !text) return text;

            const normalizedQuery = query.toLowerCase();
            const normalizedText = text.toLowerCase();
            const index = normalizedText.indexOf(normalizedQuery);

            if (index === -1) return text;

            const before = text.substring(0, index);
            const match = text.substring(index, index + query.length);
            const after = text.substring(index + query.length);

            return `${before}<mark style="background: var(--warning-color); color: white; padding: 1px 2px; border-radius: 2px;">${match}</mark>${after}`;
        }

        /**
         * 获取当前选中的州属
         * @returns {string} 州属代码
         */
        getCurrentSelectedState() {
            try {
                const stateField = document.getElementById('accommodationState');
                return stateField ? stateField.value : '';
            } catch (error) {
                console.warn('⚠️ 获取当前州属失败:', error);
                return '';
            }
        }

        /**
         * 处理自动解析输入事件 - 使用防抖优化
         * @param {string} type - 解析类型 ('personal' 或 'travel')
         * @param {HTMLElement} inputElement - 输入框元素
         */
        handleAutoParseInput(type, inputElement) {
            try {
                // 使用防抖函数处理输入
                if (type === 'personal' && this.debouncedHandlers.autoParsePersonal) {
                    this.debouncedHandlers.autoParsePersonal(inputElement);
                } else if (type === 'travel' && this.debouncedHandlers.autoParseTravel) {
                    this.debouncedHandlers.autoParseTravel(inputElement);
                }
            } catch (error) {
                this.errorHandler.handle(error, 'AutoParseInput', '自动解析处理失败', this.showMessage.bind(this));
            }
        }

        /**
         * 自动解析输入事件核心处理逻辑
         * @param {string} type - 解析类型 ('personal' 或 'travel')
         * @param {HTMLElement} inputElement - 输入框元素
         */
        handleAutoParseInputCore(type, inputElement) {
            try {
                const settings = this.autoParseSettings[type];

                // 检查自动解析是否启用
                if (!settings || !settings.enabled) {
                    return;
                }

                // 清除之前的定时器
                this.cancelAutoParse(type);

                const inputText = inputElement.value.trim();

                // 检查输入长度是否满足条件
                if (inputText.length < settings.minLength) {
                    this.hideAutoParseStatus(type);
                    return;
                }

                // 开始倒计时
                this.startAutoParseCountdown(type, settings.delay);
            } catch (error) {
                this.errorHandler.handle(error, 'AutoParseInputCore', '自动解析核心处理失败', this.showMessage.bind(this));
            }
        }

        /**
         * 开始自动解析倒计时 - 使用内存管理器
         * @param {string} type - 解析类型 ('personal' 或 'travel')
         * @param {number} delay - 延迟时间（毫秒）
         */
        startAutoParseCountdown(type, delay = 1000) {
            try {
                console.log(`⏰ [MDACMainController] 开始${type}自动解析倒计时: ${delay}ms`);

                let countdown = Math.ceil(delay / 1000);
                this.showAutoParseStatus(type, countdown);

                // 倒计时显示 - 使用内存管理器管理
                const countdownInterval = setInterval(() => {
                    countdown--;
                    if (countdown > 0) {
                        this.showAutoParseStatus(type, countdown);
                    } else {
                        this.memoryManager.removeInterval(countdownInterval);
                        this.autoParseCountdowns[type] = null;
                    }
                }, 1000);

                this.autoParseCountdowns[type] = countdownInterval;
                this.memoryManager.addInterval(countdownInterval);

                // 实际解析定时器 - 使用内存管理器管理
                const parseTimeout = setTimeout(() => {
                    try {
                        this.hideAutoParseStatus(type);

                        // 执行解析
                        if (type === 'personal') {
                            this.parsePersonalInfo();
                        } else if (type === 'travel') {
                            this.parseTravelInfo();
                        }

                        // 清理定时器
                        this.memoryManager.removeTimer(parseTimeout);
                        this.autoParseTimeouts[type] = null;
                    } catch (error) {
                        this.errorHandler.handle(error, 'AutoParseExecution', '自动解析执行失败', this.showMessage.bind(this));
                    }
                }, delay);

                this.autoParseTimeouts[type] = parseTimeout;
                this.memoryManager.addTimer(parseTimeout);

            } catch (error) {
                this.errorHandler.handle(error, 'AutoParseCountdown', '自动解析倒计时启动失败', this.showMessage.bind(this));
            }
        }

        /**
         * 取消自动解析 - 使用内存管理器
         * @param {string} type - 解析类型 ('personal' 或 'travel')
         */
        cancelAutoParse(type) {
            try {
                // 取消解析定时器
                if (this.autoParseTimeouts && this.autoParseTimeouts[type]) {
                    this.memoryManager.removeTimer(this.autoParseTimeouts[type]);
                    this.autoParseTimeouts[type] = null;
                }

                // 取消倒计时定时器
                if (this.autoParseCountdowns && this.autoParseCountdowns[type]) {
                    this.memoryManager.removeInterval(this.autoParseCountdowns[type]);
                    this.autoParseCountdowns[type] = null;
                }

                // 隐藏状态显示
                this.hideAutoParseStatus(type);

                console.log(`❌ [MDACMainController] 已取消${type}自动解析`);
            } catch (error) {
                console.warn(`⚠️ [MDACMainController] 取消${type}自动解析时出错:`, error);
            }
        }

        /**
         * 显示自动解析状态
         * @param {string} type - 解析类型 ('personal' 或 'travel')
         * @param {number} countdown - 倒计时秒数
         */
        showAutoParseStatus(type, countdown) {
            const statusElement = type === 'personal'
                ? this.elements.autoParsePersonalStatus
                : this.elements.autoParseTravel_Status;

            const countdownElement = type === 'personal'
                ? this.elements.personalCountdownText
                : this.elements.travelCountdownText;

            if (statusElement) {
                statusElement.classList.remove('hidden');
            }

            if (countdownElement) {
                countdownElement.textContent = `${countdown}秒后自动解析...`;
            }
        }

        /**
         * 隐藏自动解析状态
         * @param {string} type - 解析类型 ('personal' 或 'travel')
         */
        hideAutoParseStatus(type) {
            const statusElement = type === 'personal'
                ? this.elements.autoParsePersonalStatus
                : this.elements.autoParseTravel_Status;

            if (statusElement) {
                statusElement.classList.add('hidden');
            }
        }

        /**
         * 初始化UI状态
         */
        initializeUI() {
            console.log('🎨 [MDACMainController] 初始化UI状态...');

            // 设置预设值
            if (this.elements.presetEmail) {
                this.elements.presetEmail.value = '<EMAIL>';
            }
            if (this.elements.presetPhone) {
                this.elements.presetPhone.value = '+60167372551';
            }


            // 更新状态指示器
            this.updateFieldStatus();

            console.log('✅ [MDACMainController] UI状态初始化完成');
        }

        /**
         * 更新连接状态
         */
        updateConnectionStatus(status, message) {
            if (this.elements.connectionStatus) {
                this.elements.connectionStatus.textContent = message;
                this.elements.connectionStatus.className = `connection-status ${status}`;
            }
        }

        /**
         * 显示消息提示 - 使用增强的消息系统
         */
        showMessage(type, message, options = {}) {
            try {
                // 向后兼容：如果options是数字，转换为duration
                if (typeof options === 'number') {
                    options = { duration: options };
                }

                // 使用新的消息系统
                const messageId = this.messageSystem.show(type, message, {
                    duration: options.duration || 3000,
                    closable: options.closable !== false,
                    persistent: options.persistent || false,
                    ...options
                });

                // 同时更新侧边栏中的消息显示（向后兼容）
                const messageEl = document.getElementById('messageDisplay');
                if (messageEl) {
                    messageEl.className = `message ${type}`;
                    messageEl.textContent = message;
                    messageEl.style.display = 'block';

                    if (!options.persistent) {
                        setTimeout(() => {
                            messageEl.style.display = 'none';
                        }, options.duration || 3000);
                    }
                }

                // 同时在控制台输出
                const emoji = {
                    'success': '✅',
                    'error': '❌',
                    'warning': '⚠️',
                    'info': 'ℹ️'
                };
                console.log(`${emoji[type] || 'ℹ️'} [MDACMainController] ${message}`);

                return messageId;
            } catch (error) {
                // 回退到原始方法
                const messageEl = document.createElement('div');
                messageEl.className = `message ${type}`;
                messageEl.textContent = message;
                document.body.appendChild(messageEl);

                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, options.duration || 3000);

                console.log(`[${type.toUpperCase()}] ${message}`);
                return null;
            }
        }

        /**
         * 隐藏特定消息
         */
        hideMessage(messageId) {
            if (this.messageSystem && messageId) {
                this.messageSystem.hide(messageId);
            }
        }

        /**
         * 显示加载状态
         */
        showLoading(id, text = '处理中...', options = {}) {
            try {
                return this.loadingManager.show(id, {
                    text: text,
                    spinner: true,
                    overlay: true,
                    timeout: 30000,
                    ...options
                });
            } catch (error) {
                console.error('❌ 显示加载状态失败:', error);
                return { hide: () => {}, updateText: () => {}, updateProgress: () => {} };
            }
        }

        /**
         * 隐藏加载状态
         */
        hideLoading(id) {
            if (this.loadingManager) {
                this.loadingManager.hide(id);
            }
        }

        /**
         * 打开MDAC网站
         */
        async openMDACWebsite() {
            try {
                const url = 'https://imigresen-online.imi.gov.my/mdac/main?registerMain';
                await chrome.tabs.create({ url: url });
                this.showMessage('success', 'MDAC网站已在新标签页中打开');
            } catch (error) {
                console.error('打开MDAC网站失败:', error);
                this.showMessage('error', '无法打开MDAC网站');
            }
        }

        /**
         * 处理图片上传
         */
        async handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                this.showMessage('info', '正在处理图片...');

                // 读取图片文件
                const imageData = await this.readFileAsDataURL(file);

                // 这里应该调用AI图片识别服务
                // 暂时显示占位符消息
                this.showMessage('success', '图片上传成功，AI识别功能开发中...');

            } catch (error) {
                console.error('图片处理失败:', error);
                this.showMessage('error', '图片处理失败');
            }
        }

        /**
         * 读取文件为DataURL
         */
        readFileAsDataURL(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        /**
         * 解析个人信息 - 使用增强的加载状态
         */
        async parsePersonalInfo() {
            const inputText = this.elements.personalInfoInput?.value?.trim();
            if (!inputText) {
                this.showMessage('warning', '请输入个人信息内容');
                return;
            }

            // 显示加载状态
            const loader = this.showLoading('parsePersonal', 'AI正在解析个人信息...', {
                progress: true
            });

            try {
                this.elements.parsePersonalBtn.disabled = true;

                // 更新进度
                loader.updateProgress(20);
                loader.updateText('正在连接AI服务...');

                // 更新进度
                loader.updateProgress(40);

                // 调用真正的Gemini AI解析
                const parsedData = await this.callGeminiAI(inputText, 'personal');

                // 更新进度
                loader.updateProgress(70);
                loader.updateText('正在填充表单字段...');

                if (parsedData && Object.keys(parsedData).length > 0) {
                    // 填充表单字段
                    this.fillPersonalFields(parsedData);

                    // 更新进度
                    loader.updateProgress(100);
                    loader.updateText('解析完成！');

                    setTimeout(() => {
                        loader.hide();
                        this.showMessage('success', '个人信息AI解析完成');
                    }, 500);
                } else {
                    // 如果AI解析失败，使用简单解析作为后备
                    loader.updateText('AI服务不可用，使用基础解析...');
                    const fallbackData = this.parsePersonalText(inputText);
                    this.fillPersonalFields(fallbackData);

                    loader.updateProgress(100);
                    setTimeout(() => {
                        loader.hide();
                        this.showMessage('warning', '使用基础解析（AI服务暂不可用）');
                    }, 500);
                }

            } catch (error) {
                console.error('个人信息解析失败:', error);

                // 隐藏加载器
                loader.hide();

                // 详细的错误分析和提示
                let errorMessage = '解析失败：';
                if (error.message.includes('network') || error.message.includes('fetch')) {
                    errorMessage += '网络连接问题，请检查网络连接';
                } else if (error.message.includes('API') || error.message.includes('key')) {
                    errorMessage += 'AI服务配置问题，请检查API密钥';
                } else if (error.message.includes('quota') || error.message.includes('limit')) {
                    errorMessage += 'AI服务配额已用完，请稍后再试';
                } else if (error.message.includes('format') || error.message.includes('parse')) {
                    errorMessage += '输入内容格式不支持，请尝试更清晰的描述';
                } else {
                    errorMessage += error.message || '未知错误';
                }

                // 后备解析
                try {
                    const fallbackLoader = this.showLoading('fallbackPersonal', '使用基础解析...', {
                        timeout: 5000
                    });

                    const fallbackData = this.parsePersonalText(inputText);
                    this.fillPersonalFields(fallbackData);

                    fallbackLoader.hide();
                    this.showMessage('warning', '使用基础解析（' + errorMessage + '）');
                } catch (fallbackError) {
                    this.showMessage('error', errorMessage);
                }
            } finally {
                this.elements.parsePersonalBtn.disabled = false;
            }
        }

        /**
         * 解析旅行信息
         */
        async parseTravelInfo() {
            const inputText = this.elements.travelInfoInput?.value?.trim();
            if (!inputText) {
                this.showMessage('warning', '请输入旅行信息内容');
                return;
            }

            try {
                this.showMessage('info', 'AI正在解析旅行信息...');
                this.elements.parseTravelBtn.disabled = true;

                // 调用真正的Gemini AI解析
                const parsedData = await this.callGeminiAI(inputText, 'travel');

                if (parsedData && Object.keys(parsedData).length > 0) {
                    // 填充表单字段
                    this.fillTravelFields(parsedData);
                    this.showMessage('success', '旅行信息AI解析完成');
                } else {
                    // 如果AI解析失败，使用简单解析作为后备
                    const fallbackData = this.parseTravelText(inputText);
                    this.fillTravelFields(fallbackData);
                    this.showMessage('warning', '使用基础解析（AI服务暂不可用）');
                }

            } catch (error) {
                console.error('旅行信息解析失败:', error);

                // 详细的错误分析和提示
                let errorMessage = '解析失败：';
                if (error.message.includes('network') || error.message.includes('fetch')) {
                    errorMessage += '网络连接问题，请检查网络连接';
                } else if (error.message.includes('API') || error.message.includes('key')) {
                    errorMessage += 'AI服务配置问题，请检查API密钥';
                } else if (error.message.includes('quota') || error.message.includes('limit')) {
                    errorMessage += 'AI服务配额已用完，请稍后再试';
                } else if (error.message.includes('format') || error.message.includes('parse')) {
                    errorMessage += '输入内容格式不支持，请尝试更清晰的描述';
                } else {
                    errorMessage += error.message || '未知错误';
                }

                // 后备解析
                try {
                    const fallbackData = this.parseTravelText(inputText);
                    this.fillTravelFields(fallbackData);
                    this.showMessage('warning', '使用基础解析（' + errorMessage + '）');
                } catch (fallbackError) {
                    this.showMessage('error', errorMessage);
                }
            } finally {
                this.elements.parseTravelBtn.disabled = false;
            }
        }

        /**
         * 解析地址信息
         */
        async parseAddressInfo() {
            const inputText = this.elements.travelInfoInput?.value?.trim();
            if (!inputText) {
                this.showMessage('warning', '请输入包含地址的旅行信息内容');
                return;
            }

            try {
                this.showMessage('info', 'AI正在解析地址信息...');
                this.elements.parseAddressBtn.disabled = true;

                // 使用AddressResolver进行地址解析
                if (window.AddressResolver) {
                    const resolver = new window.AddressResolver();
                    const result = await resolver.resolveAddress(inputText);
                    
                    if (result.success && result.data) {
                        // 填充地址相关字段
                        this.fillAddressFields(result.data);
                        this.showMessage('success', `地址解析完成 (${result.metadata.method})`);
                    } else {
                        this.showMessage('error', '地址解析失败: ' + (result.error || '未找到有效地址'));
                    }
                } else {
                    this.showMessage('error', '地址解析器未加载');
                }

            } catch (error) {
                console.error('地址解析失败:', error);
                this.showMessage('error', '地址解析失败：' + error.message);
            } finally {
                this.elements.parseAddressBtn.disabled = false;
            }
        }

        /**
         * 填充地址相关字段
         * @param {Object} addressData - 地址数据
         */
        fillAddressFields(addressData) {
            try {
                // 填充地址字段
                if (addressData.address && this.elements.formFields.address) {
                    this.elements.formFields.address.value = addressData.address;
                    this.markFieldAsCompleted('address');
                }

                // 填充邮编字段
                if (addressData.postcode && this.elements.formFields.postcode) {
                    this.elements.formFields.postcode.value = addressData.postcode;
                    this.markFieldAsCompleted('postcode');
                }

                // 填充州属字段
                if (addressData.state && this.elements.formFields.state) {
                    this.elements.formFields.state.value = addressData.state;
                    this.markFieldAsCompleted('state');
                    // 触发州属变化事件，更新城市选项
                    this.populateCities(addressData.state);
                }

                // 填充城市字段
                if (addressData.city && this.elements.formFields.city) {
                    // 稍等一下让城市选项填充完成
                    setTimeout(() => {
                        this.elements.formFields.city.value = addressData.city;
                        this.markFieldAsCompleted('city');
                    }, 100);
                }

                // 更新数据对象
                this.data.travel = {
                    ...this.data.travel,
                    address: addressData.address,
                    postcode: addressData.postcode,
                    state: addressData.state,
                    city: addressData.city
                };

                // 保存数据
                this.saveData();

                console.log('✅ [MDACMainController] 地址字段填充完成:', addressData);
                
            } catch (error) {
                console.error('❌ [MDACMainController] 地址字段填充失败:', error);
                this.showMessage('error', '地址字段填充失败：' + error.message);
            }
        }

        /**
         * 调用Gemini AI进行智能解析
         */
        async callGeminiAI(text, type) {
            try {
                // 检查AI配置是否可用
                if (!this.aiConfig?.loaded) {
                    console.warn('AI配置未加载，使用基础解析');
                    return null;
                }

                // 获取API配置
                const apiKey = window.GEMINI_CONFIG?.DEFAULT_API_KEY || window.MDAC_AI_CONFIG?.apiKey;
                if (!apiKey) {
                    console.warn('AI API密钥不可用');
                    return null;
                }

                const model = window.GEMINI_CONFIG?.DEFAULT_MODEL || 'gemini-1.5-flash';
                const baseUrl = window.GEMINI_CONFIG?.API_BASE_URL || 'https://generativelanguage.googleapis.com/v1beta/models';

                // 构造提示词
                const prompt = this.buildAIPrompt(text, type);

                // 调用Gemini API
                const response = await fetch(`${baseUrl}/${model}:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }],
                        generationConfig: window.GEMINI_CONFIG?.DEFAULT_GENERATION_CONFIG || {
                            temperature: 0.1,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 1024
                        },
                        safetySettings: window.GEMINI_CONFIG?.SAFETY_SETTINGS || []
                    })
                });

                if (!response.ok) {
                    throw new Error(`AI API请求失败: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.candidates && result.candidates[0]?.content?.parts?.[0]?.text) {
                    const aiResponse = result.candidates[0].content.parts[0].text;
                    return this.parseAIResponse(aiResponse, type);
                } else {
                    console.warn('AI响应格式异常:', result);
                    return null;
                }

            } catch (error) {
                console.error('Gemini AI调用失败:', error);
                return null;
            }
        }

        /**
         * 构建AI提示词
         */
        buildAIPrompt(text, type) {
            if (type === 'personal') {
                return `请从以下文本中提取个人信息，返回JSON格式：
文本：${text}

请提取以下字段（如果存在）：
- name: 姓名
- passportNo: 护照号
- dateOfBirth: 出生日期 (格式: YYYY-MM-DD)
- nationality: 国籍
- sex: 性别 (1=男, 2=女)
- passportExpiry: 护照到期日期 (格式: YYYY-MM-DD)

只返回JSON格式，不要其他解释文字。`;
            } else if (type === 'travel') {
                return `请从以下文本中提取旅行信息，返回JSON格式：
文本：${text}

请提取以下字段（如果存在）：
- arrivalDate: 到达日期 (格式: YYYY-MM-DD)
- departureDate: 离开日期 (格式: YYYY-MM-DD)
- flightNo: 航班号
- modeOfTravel: 交通方式
- accommodation: 住宿类型
- address: 住址
- state: 州/地区
- city: 城市
- postcode: 邮编

只返回JSON格式，不要其他解释文字。`;
            }
            return text;
        }

        /**
         * 解析AI响应
         */
        parseAIResponse(aiResponse, type) {
            try {
                // 尝试提取JSON
                const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[0]);
                }
                
                // 如果没找到JSON格式，返回空对象
                console.warn('AI响应中未找到有效JSON格式');
                return {};
            } catch (error) {
                console.error('AI响应解析失败:', error);
                return {};
            }
        }

        /**
         * 简单的个人信息文本解析
         */
        parsePersonalText(text) {
            const data = {};

            // 姓名匹配
            const nameMatch = text.match(/(?:姓名|名字|Name)[：:\s]*([A-Za-z\s]+)/i);
            if (nameMatch) data.name = nameMatch[1].trim();

            // 护照号匹配
            const passportMatch = text.match(/(?:护照|Passport)[：:\s]*([A-Z0-9]+)/i);
            if (passportMatch) data.passportNo = passportMatch[1].trim();

            // 出生日期匹配
            const birthMatch = text.match(/(?:出生|生日|Birth)[：:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i);
            if (birthMatch) data.dateOfBirth = this.formatDate(birthMatch[1]);

            // 性别匹配
            const genderMatch = text.match(/(?:性别|Gender)[：:\s]*(男|女|Male|Female|M|F)/i);
            if (genderMatch) {
                const gender = genderMatch[1].toLowerCase();
                data.sex = (gender === '男' || gender === 'male' || gender === 'm') ? '1' : '2';
            }

            return data;
        }

        /**
         * 简单的旅行信息文本解析
         */
        parseTravelText(text) {
            const data = {};

            // 航班号匹配
            const flightMatch = text.match(/(?:航班|Flight)[：:\s]*([A-Z0-9]+)/i);
            if (flightMatch) data.flightNo = flightMatch[1].trim();

            // 到达日期匹配
            const arrivalMatch = text.match(/(?:到达|抵达|Arrival)[：:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i);
            if (arrivalMatch) data.arrivalDate = this.formatDate(arrivalMatch[1]);

            // 离开日期匹配
            const departureMatch = text.match(/(?:离开|出发|Departure)[：:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i);
            if (departureMatch) data.departureDate = this.formatDate(departureMatch[1]);

            // 地址匹配
            const addressMatch = text.match(/(?:地址|住址|Address)[：:\s]*([^\n\r]+)/i);
            if (addressMatch) data.address = addressMatch[1].trim();

            return data;
        }

        /**
         * 格式化日期为YYYY-MM-DD格式
         */
        formatDate(dateStr) {
            try {
                const parts = dateStr.split(/[\/\-]/);
                if (parts.length === 3) {
                    // 假设输入格式为DD/MM/YYYY或DD-MM-YYYY
                    const day = parts[0].padStart(2, '0');
                    const month = parts[1].padStart(2, '0');
                    const year = parts[2];
                    return `${year}-${month}-${day}`;
                }
            } catch (error) {
                console.error('日期格式化失败:', error);
            }
            return dateStr;
        }

        /**
         * 填充个人信息字段
         */
        fillPersonalFields(data) {
            Object.keys(data).forEach(key => {
                const field = this.elements.formFields[key];
                if (field && data[key]) {
                    field.value = data[key];
                    this.data.personal[key] = data[key];
                }
            });
            this.updateFieldStatus();
        }

        /**
         * 填充旅行信息字段
         */
        fillTravelFields(data) {
            Object.keys(data).forEach(key => {
                const field = this.elements.formFields[key];
                if (field && data[key]) {
                    field.value = data[key];
                    this.data.travel[key] = data[key];
                }
            });
            this.updateFieldStatus();
        }

        /**
         * 更新字段状态指示器 - 使用防抖优化
         */
        updateFieldStatus() {
            if (this.debouncedHandlers && this.debouncedHandlers.updateFieldStatus) {
                this.debouncedHandlers.updateFieldStatus();
            } else {
                // 回退到直接调用
                this.updateFieldStatusCore();
            }
        }

        /**
         * 字段状态更新核心逻辑
         */
        updateFieldStatusCore() {
            try {
                if (!this.elements || !this.elements.formFields) {
                    return;
                }

                let filledCount = 0;
                const totalFields = Object.keys(this.elements.formFields).length;

                // 使用批量DOM操作优化
                const { DOMOptimizer } = window.PerformanceUtils;
                DOMOptimizer.batchOperations(() => {
                    Object.entries(this.elements.formFields).forEach(([key, field]) => {
                        if (field) {
                            const statusEl = document.querySelector(`[data-field="${key}"]`);
                            if (field.value && field.value.trim()) {
                                field.classList.add('filled');
                                if (statusEl) {
                                    statusEl.className = 'field-status success';
                                }
                                filledCount++;
                            } else {
                                field.classList.remove('filled');
                                if (statusEl) {
                                    statusEl.className = 'field-status';
                                }
                            }
                        }
                    });

                    // 更新状态指示器
                    const fieldsStatusEl = document.getElementById('fieldsStatus');
                    if (fieldsStatusEl) {
                        const statusTextEl = fieldsStatusEl.querySelector('.status-text');
                        if (statusTextEl) {
                            statusTextEl.textContent = `字段: ${filledCount}/${totalFields}`;
                        }
                    }

                    // 更新完整度
                    const completenessEl = document.getElementById('completenessStatus');
                    if (completenessEl) {
                        const statusTextEl = completenessEl.querySelector('.status-text');
                        if (statusTextEl) {
                            const percentage = Math.round((filledCount / totalFields) * 100);
                            statusTextEl.textContent = `完整度: ${percentage}%`;
                        }
                    }
                });

            } catch (error) {
                this.errorHandler.handle(error, 'UpdateFieldStatus', '字段状态更新失败', this.showMessage.bind(this));
            }
        }

        /**
         * 清除所有数据
         */
        clearAllData() {
            // 清除表单字段
            Object.values(this.elements.formFields).forEach(field => {
                if (field) {
                    field.value = '';
                    field.classList.remove('filled');
                }
            });

            // 清除输入区域
            if (this.elements.personalInfoInput) {
                this.elements.personalInfoInput.value = '';
            }
            if (this.elements.travelInfoInput) {
                this.elements.travelInfoInput.value = '';
            }

            // 清除数据
            this.data.personal = {};
            this.data.travel = {};

            // 更新状态
            this.updateFieldStatus();
            this.showMessage('success', '所有数据已清除');
        }

        /**
         * 显示数据预览
         */
        showDataPreview() {
            const personalData = this.collectPersonalData();
            const travelData = this.collectTravelData();

            const previewContent = this.generatePreviewHTML(personalData, travelData);

            this.showModal('数据预览', previewContent);
        }

        /**
         * 收集个人信息数据
         */
        collectPersonalData() {
            const data = {};
            const personalFields = ['name', 'passportNo', 'dateOfBirth', 'nationality', 'sex', 'passportExpiry'];

            personalFields.forEach(field => {
                const element = this.elements.formFields[field];
                if (element && element.value) {
                    data[field] = element.value;
                }
            });

            return data;
        }

        /**
         * 收集旅行信息数据
         */
        collectTravelData() {
            const data = {};
            const travelFields = ['arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
                                'accommodation', 'address', 'state', 'city', 'postcode'];

            travelFields.forEach(field => {
                const element = this.elements.formFields[field];
                if (element && element.value) {
                    data[field] = element.value;
                }
            });

            return data;
        }

        /**
         * 生成预览HTML
         */
        generatePreviewHTML(personalData, travelData) {
            let html = '<div class="data-preview-container">';

            // 个人信息部分
            html += '<div class="preview-category">';
            html += '<h4>👤 个人信息</h4>';
            Object.entries(personalData).forEach(([key, value]) => {
                const label = this.getFieldLabel(key);
                html += `<div class="preview-item"><strong>${label}:</strong> <span class="preview-value">${value}</span></div>`;
            });
            html += '</div>';

            // 旅行信息部分
            html += '<div class="preview-category">';
            html += '<h4>✈️ 旅行信息</h4>';
            Object.entries(travelData).forEach(([key, value]) => {
                const label = this.getFieldLabel(key);
                html += `<div class="preview-item"><strong>${label}:</strong> <span class="preview-value">${value}</span></div>`;
            });
            html += '</div>';

            html += '</div>';
            return html;
        }

        /**
         * 获取字段标签
         */
        getFieldLabel(fieldName) {
            const labels = {
                name: '姓名',
                passportNo: '护照号码',
                dateOfBirth: '出生日期',
                nationality: '国籍',
                sex: '性别',
                passportExpiry: '护照到期日',
                arrivalDate: '到达日期',
                departureDate: '离开日期',
                flightNo: '航班号',
                modeOfTravel: '交通方式',
                accommodation: '住宿类型',
                address: '住宿地址',
                state: '州属',
                city: '城市',
                postcode: '邮政编码'
            };
            return labels[fieldName] || fieldName;
        }

        /**
         * 更新到MDAC页面
         */
        async updateToMDAC() {
            try {
                this.showMessage('info', '正在验证和更新数据...');

                // 验证表单数据
                const validation = this.validateFormData();
                if (!this.showValidationResult(validation)) {
                    return;
                }

                // 收集所有数据
                const personalData = this.collectPersonalData();
                const travelData = this.collectTravelData();
                const allData = { ...personalData, ...travelData };

                // 检查是否有数据
                if (Object.keys(allData).length === 0) {
                    this.showMessage('warning', '没有数据可以更新');
                    return;
                }

                this.showMessage('info', '正在发送数据到MDAC页面...');

                // 发送消息到content script
                const tabs = await chrome.tabs.query({ url: '*://imigresen-online.imi.gov.my/*' });
                if (tabs.length === 0) {
                    this.showMessage('warning', '请先打开MDAC网站');
                    // 提供快速打开选项
                    this.showModal('打开MDAC网站',
                        '需要先打开MDAC网站才能填充数据。是否现在打开？',
                        () => this.openMDACWebsite()
                    );
                    return;
                }

                // 向所有MDAC标签页发送数据（使用增强重试机制）
                let successCount = 0;
                let connectionErrors = 0;
                for (const tab of tabs) {
                    try {
                        // 等待内容脚本就绪（最多10秒）
                        const isReady = await this.waitForContentScript(tab.id, 10000);
                        if (!isReady) {
                            connectionErrors++;
                            console.warn(`⚠️ 标签页 ${tab.id} 内容脚本未就绪，跳过发送`);
                            continue;
                        }
                        
                        // 使用重试机制发送数据
                        await this.sendMessageWithRetry(tab.id, {
                            action: 'fillMDACForm',
                            data: allData,
                            mappings: this.fieldMappings
                        }, 3, 1000);
                        
                        successCount++;
                        console.log(`✅ 成功发送数据到标签页 ${tab.id}`);
                        
                    } catch (error) {
                        connectionErrors++;
                        if (error.message.includes('Could not establish connection') || 
                            error.message.includes('Receiving end does not exist')) {
                            console.warn(`⚠️ 标签页 ${tab.id} 连接失败，内容脚本可能未正确加载:`, error.message);
                        } else {
                            console.warn(`❌ 发送到标签页 ${tab.id} 失败:`, error);
                        }
                    }
                }

                if (successCount > 0) {
                    this.showMessage('success', `数据已发送到 ${successCount} 个MDAC页面`);
                    // 自动保存成功的数据
                    await this.saveData();
                    
                    // 触发数据更新事件
                    this.emitEvent('dataUpdated', {
                        successCount,
                        data: allData,
                        timestamp: Date.now()
                    });
                    
                } else if (connectionErrors > 0) {
                    this.showMessage('warning', `无法连接到MDAC页面。数据已加入队列，将在页面刷新后自动发送。请刷新MDAC页面或检查内容脚本是否正常加载。`);
                    
                    // 触发连接错误事件
                    this.emitEvent('connectionError', {
                        connectionErrors,
                        data: allData,
                        timestamp: Date.now()
                    });
                    
                } else {
                    this.showMessage('error', '无法发送数据到MDAC页面，请检查页面是否正确加载');
                    
                    // 触发错误事件
                    this.emitEvent('errorOccurred', {
                        type: 'sendFailed',
                        message: '无法发送数据到MDAC页面',
                        timestamp: Date.now()
                    });
                }

            } catch (error) {
                console.error('更新到MDAC失败:', error);
                this.showMessage('error', '更新到MDAC失败: ' + error.message);
            }
        }



        /**
         * 显示模态框
         */
        showModal(title, content, confirmCallback = null) {
            if (!this.elements.modal) return;

            this.elements.modalTitle.textContent = title;
            this.elements.modalBody.innerHTML = content;
            this.elements.modal.classList.add('show');

            // 设置确认回调
            if (confirmCallback) {
                // 移除之前的事件监听器（如果存在）
                this.elements.modalConfirm.removeEventListener('click', this.modalConfirmHandler);
                
                // 创建新的事件处理程序
                this.modalConfirmHandler = () => {
                    confirmCallback();
                    this.hideModal();
                };
                
                // 添加事件监听器
                this.elements.modalConfirm.addEventListener('click', this.modalConfirmHandler);
                this.elements.modalConfirm.style.display = 'block';
            } else {
                this.elements.modalConfirm.style.display = 'none';
            }
        }

        /**
         * 隐藏模态框
         */
        hideModal() {
            if (this.elements.modal) {
                this.elements.modal.classList.remove('show');
            }
        }

        /**
         * 加载保存的数据
         */
        async loadSavedData() {
            try {
                const savedData = await chrome.storage.local.get(['mdac_personal_data', 'mdac_travel_data']);

                if (savedData.mdac_personal_data) {
                    this.data.personal = savedData.mdac_personal_data;
                    this.fillPersonalFields(this.data.personal);
                }

                if (savedData.mdac_travel_data) {
                    this.data.travel = savedData.mdac_travel_data;
                    this.fillTravelFields(this.data.travel);
                }

                console.log('✅ [MDACMainController] 保存的数据加载完成');
            } catch (error) {
                console.warn('⚠️ [MDACMainController] 加载保存数据失败:', error);
            }
        }

        /**
         * 保存数据
         */
        async saveData() {
            try {
                const personalData = this.collectPersonalData();
                const travelData = this.collectTravelData();

                await chrome.storage.local.set({
                    mdac_personal_data: personalData,
                    mdac_travel_data: travelData
                });

                this.showMessage('success', '数据已保存');
            } catch (error) {
                console.error('保存数据失败:', error);
                this.showMessage('error', '保存数据失败');
            }
        }

        /**
         * 获取控制器状态
         */
        getStatus() {
            return {
                initialized: this.initialized,
                version: this.version,
                dataLoaded: {
                    aiConfig: !!this.aiConfig?.loaded,
                    cityData: !!this.cityData && Object.keys(this.cityData).length > 0,
                    fieldMappings: !!this.fieldMappings && Object.keys(this.fieldMappings).length > 0
                },
                fieldsCount: {
                    personal: Object.keys(this.data.personal).length,
                    travel: Object.keys(this.data.travel).length
                }
            };
        }

        /**
         * 自动保存数据 - 使用内存管理器
         */
        async autoSaveData() {
            try {
                // 清除之前的自动保存定时器
                if (this.autoSaveTimeout) {
                    this.memoryManager.removeTimer(this.autoSaveTimeout);
                    this.autoSaveTimeout = null;
                }

                // 设置新的自动保存定时器
                this.autoSaveTimeout = setTimeout(async () => {
                    try {
                        await this.saveData();
                        console.log('🔄 [MDACMainController] 自动保存完成');

                        // 清理定时器引用
                        this.memoryManager.removeTimer(this.autoSaveTimeout);
                        this.autoSaveTimeout = null;
                    } catch (error) {
                        this.errorHandler.handle(error, 'AutoSave', '自动保存失败', this.showMessage.bind(this));
                    }
                }, 2000); // 2秒后自动保存

                // 将定时器添加到内存管理器
                this.memoryManager.addTimer(this.autoSaveTimeout);

            } catch (error) {
                this.errorHandler.handle(error, 'AutoSaveSetup', '自动保存设置失败', this.showMessage.bind(this));
            }
        }

        /**
         * 保存预设数据
         */
        async savePresetData() {
            try {
                const presetData = {
                    email: this.elements.presetEmail?.value || '',
                    phone: this.elements.presetPhone?.value || ''
                };

                await chrome.storage.local.set({ mdac_preset_data: presetData });
                console.log('✅ [MDACMainController] 预设数据已保存');
            } catch (error) {
                console.warn('⚠️ [MDACMainController] 预设数据保存失败:', error);
            }
        }

        /**
         * 加载预设数据
         */
        async loadPresetData() {
            try {
                const result = await chrome.storage.local.get(['mdac_preset_data']);
                if (result.mdac_preset_data) {
                    const presetData = result.mdac_preset_data;
                    if (this.elements.presetEmail) {
                        this.elements.presetEmail.value = presetData.email || '<EMAIL>';
                    }
                    if (this.elements.presetPhone) {
                        this.elements.presetPhone.value = presetData.phone || '+60167372551';
                    }
                }
            } catch (error) {
                console.warn('⚠️ [MDACMainController] 预设数据加载失败:', error);
            }
        }


        /**
         * 验证表单数据
         */
        validateFormData() {
            const errors = [];
            const warnings = [];

            // 验证必填字段
            const requiredFields = {
                name: '姓名',
                passportNo: '护照号码',
                dateOfBirth: '出生日期',
                nationality: '国籍',
                arrivalDate: '到达日期'
            };

            Object.entries(requiredFields).forEach(([field, label]) => {
                const element = this.elements.formFields[field];
                if (!element || !element.value || !element.value.trim()) {
                    errors.push(`${label}为必填项`);
                }
            });

            // 验证日期格式
            const dateFields = ['dateOfBirth', 'arrivalDate', 'departureDate', 'passportExpiry'];
            dateFields.forEach(field => {
                const element = this.elements.formFields[field];
                if (element && element.value) {
                    const dateValue = new Date(element.value);
                    if (isNaN(dateValue.getTime())) {
                        errors.push(`${this.getFieldLabel(field)}日期格式不正确`);
                    }
                }
            });

            // 验证护照号格式
            const passportElement = this.elements.formFields.passportNo;
            if (passportElement && passportElement.value) {
                const passportPattern = /^[A-Z0-9]{6,12}$/;
                if (!passportPattern.test(passportElement.value)) {
                    warnings.push('护照号格式可能不正确');
                }
            }

            return { errors, warnings, isValid: errors.length === 0 };
        }

        /**
         * 显示验证结果
         */
        showValidationResult(validation) {
            if (validation.errors.length > 0) {
                this.showMessage('error', `验证失败: ${validation.errors.join(', ')}`);
                return false;
            }

            if (validation.warnings.length > 0) {
                this.showMessage('warning', `注意: ${validation.warnings.join(', ')}`);
            }

            return true;
        }

        /**
         * 调试信息
         */
        debug() {
            console.log('🐛 [MDACMainController] 调试信息:');
            console.log('状态:', this.getStatus());
            console.log('个人数据:', this.data.personal);
            console.log('旅行数据:', this.data.travel);
            console.log('UI元素:', this.elements);
            console.log('配置数据:', {
                aiConfig: this.aiConfig,
                cityDataKeys: this.cityData ? Object.keys(this.cityData) : null,
                fieldMappings: this.fieldMappings
            });
        }

        /**
         * 通知统一架构系统已就绪
         */
        notifyUnifiedArchitecture() {
            try {
                // 发送到全局事件总线
                if (window.mdacEventBus) {
                    window.mdacEventBus.emit('traditional-system-ready', {
                        version: this.version,
                        capabilities: this.capabilities,
                        systemType: this.systemType
                    });
                }
                
                // 设置为全局可访问
                window.mdacSidePanelApp = this;
                
                console.log('📡 [MDACMainController] 已通知统一架构');
                
            } catch (error) {
                console.warn('⚠️ [MDACMainController] 统一架构通知失败:', error);
            }
        }

        /**
         * 添加事件监听器 (统一架构兼容接口)
         */
        addEventListener(eventType, callback) {
            if (!this.eventListeners.has(eventType)) {
                this.eventListeners.set(eventType, []);
            }
            this.eventListeners.get(eventType).push(callback);
        }

        /**
         * 移除事件监听器
         */
        removeEventListener(eventType, callback) {
            if (this.eventListeners.has(eventType)) {
                const listeners = this.eventListeners.get(eventType);
                const index = listeners.indexOf(callback);
                if (index > -1) {
                    listeners.splice(index, 1);
                }
            }
        }

        /**
         * 触发事件
         */
        emitEvent(eventType, data) {
            if (this.eventListeners.has(eventType)) {
                this.eventListeners.get(eventType).forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        console.error(`❌ [MDACMainController] 事件 ${eventType} 处理失败:`, error);
                    }
                });
            }
            
            // 同时发送到全局事件总线
            if (window.mdacEventBus) {
                window.mdacEventBus.emit(`traditional:${eventType}`, data);
            }
        }

        /**
         * 等待内容脚本就绪
         * @param {number} tabId 标签页ID
         * @param {number} timeout 超时时间（毫秒）
         * @returns {Promise<boolean>} 是否就绪
         */
        async waitForContentScript(tabId, timeout = 10000) {
            const startTime = Date.now();
            
            while (Date.now() - startTime < timeout) {
                try {
                    // 发送ping测试内容脚本是否响应
                    const response = await new Promise((resolve, reject) => {
                        chrome.tabs.sendMessage(tabId, { action: 'ping' }, (response) => {
                            if (chrome.runtime.lastError) {
                                reject(new Error(chrome.runtime.lastError.message));
                            } else {
                                resolve(response);
                            }
                        });
                    });
                    
                    if (response && response.status === 'ready') {
                        console.log(`✅ 标签页 ${tabId} 内容脚本已就绪`);
                        return true;
                    }
                } catch (error) {
                    // 忽略错误，继续重试
                }
                
                // 等待100ms后重试
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.warn(`⚠️ 标签页 ${tabId} 内容脚本就绪超时 (${timeout}ms)`);
            return false;
        }

        /**
         * 带重试机制的消息发送
         * @param {number} tabId 标签页ID
         * @param {Object} message 消息对象
         * @param {number} maxRetries 最大重试次数
         * @param {number} retryDelay 重试延迟（毫秒）
         * @returns {Promise<any>} 响应结果
         */
        async sendMessageWithRetry(tabId, message, maxRetries = 3, retryDelay = 1000) {
            let lastError = null;
            
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    console.log(`📤 向标签页 ${tabId} 发送消息 (尝试 ${attempt + 1}/${maxRetries + 1}):`, message.action);
                    
                    const response = await new Promise((resolve, reject) => {
                        chrome.tabs.sendMessage(tabId, message, (response) => {
                            if (chrome.runtime.lastError) {
                                reject(new Error(chrome.runtime.lastError.message));
                            } else {
                                resolve(response);
                            }
                        });
                    });
                    
                    console.log(`✅ 标签页 ${tabId} 消息发送成功`);
                    return response;
                    
                } catch (error) {
                    lastError = error;
                    console.warn(`⚠️ 标签页 ${tabId} 消息发送失败 (尝试 ${attempt + 1}):`, error.message);
                    
                    if (attempt < maxRetries) {
                        // 指数退避策略
                        const delay = retryDelay * Math.pow(2, attempt);
                        console.log(`🔄 ${delay}ms 后重试...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
            
            console.error(`❌ 标签页 ${tabId} 消息发送彻底失败:`, lastError.message);
            throw lastError;
        }
    }

    // 创建控制器实例并初始化
    const controller = new MDACMainController();
    
    // 将控制器暴露到全局，供其他脚本访问
    window.MDACMainController = controller;
    
    // 页面加载完成后自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            controller.initialize();
        });
    } else {
        // 如果页面已加载完成，立即初始化
        controller.initialize();
    }
    
    console.log('✅ [MDACMainController] 脚本加载完成');
    
})(); // 关闭立即执行函数
