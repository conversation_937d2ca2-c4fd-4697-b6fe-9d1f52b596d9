<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址解析器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #005a8a;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-cases {
            margin: 20px 0;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }
        .test-case:hover {
            background: #e9ecef;
        }
        .test-case-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .test-case-content {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 地址解析器测试</h1>
        
        <div class="test-section">
            <h3>手动测试</h3>
            <textarea 
                id="manualInput" 
                class="test-input" 
                rows="3" 
                placeholder="输入包含地址的文本，例如：我住在吉隆坡KLCC附近，邮编50088...">
            </textarea>
            <button id="manualTestBtn" class="test-button">解析地址</button>
            <div id="manualResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>预设测试用例</h3>
            <div class="test-cases">
                <div class="test-case" data-text="我住在吉隆坡KLCC附近，邮编50088">
                    <div class="test-case-title">测试1: 地标 + 邮编</div>
                    <div class="test-case-content">我住在吉隆坡KLCC附近，邮编50088</div>
                </div>
                <div class="test-case" data-text="酒店地址：7 Jalan Legoland, Bandar Medini Iskandar, 79100 Nusajaya, Johor">
                    <div class="test-case-title">测试2: 完整地址</div>
                    <div class="test-case-content">酒店地址：7 Jalan Legoland, Bandar Medini Iskandar, 79100 Nusajaya, Johor</div>
                </div>
                <div class="test-case" data-text="我要去云顶高原度假，住在那里的酒店">
                    <div class="test-case-title">测试3: 地标名称</div>
                    <div class="test-case-content">我要去云顶高原度假，住在那里的酒店</div>
                </div>
                <div class="test-case" data-text="槟城乔治市的酒店，靠近光大">
                    <div class="test-case-title">测试4: 城市+地标</div>
                    <div class="test-case-content">槟城乔治市的酒店，靠近光大</div>
                </div>
                <div class="test-case" data-text="40100 Shah Alam, Selangor">
                    <div class="test-case-title">测试5: 邮编+城市</div>
                    <div class="test-case-content">40100 Shah Alam, Selangor</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>自动测试</h3>
            <button id="autoTestBtn" class="test-button">运行所有测试</button>
            <div id="autoTestResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>系统状态</h3>
            <div id="systemStatus" class="result info">正在检查系统状态...</div>
        </div>
    </div>

    <script>
        // 模拟Chrome扩展环境
        if (typeof chrome === 'undefined') {
            window.chrome = {
                runtime: {
                    getURL: function(path) {
                        return '../' + path;
                    }
                }
            };
        }

        // 测试用例数据
        const testCases = [
            {
                name: "地标 + 邮编",
                input: "我住在吉隆坡KLCC附近，邮编50088",
                expected: {
                    postcode: "50088",
                    state: "14",
                    city: "1400"
                }
            },
            {
                name: "完整地址",
                input: "酒店地址：7 Jalan Legoland, Bandar Medini Iskandar, 79100 Nusajaya, Johor",
                expected: {
                    postcode: "79100",
                    state: "01",
                    city: "0118"
                }
            },
            {
                name: "地标名称",
                input: "我要去云顶高原度假，住在那里的酒店",
                expected: {
                    postcode: "69000",
                    state: "06",
                    city: "0600"
                }
            },
            {
                name: "城市+地标",
                input: "槟城乔治市的酒店，靠近光大",
                expected: {
                    state: "07",
                    city: "1000"
                }
            },
            {
                name: "邮编+城市",
                input: "40100 Shah Alam, Selangor",
                expected: {
                    postcode: "40100",
                    state: "04",
                    city: "0400"
                }
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            setupEventListeners();
        });

        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let status = [];

            // 检查AddressResolver
            if (typeof window.AddressResolver !== 'undefined') {
                status.push('✅ AddressResolver 类已加载');
            } else {
                status.push('❌ AddressResolver 类未加载');
            }

            // 检查AI配置
            if (typeof window.MDAC_AI_CONFIG !== 'undefined') {
                status.push('✅ AI 配置已加载');
            } else {
                status.push('❌ AI 配置未加载');
            }

            // 检查Logger
            if (typeof window.logger !== 'undefined' || typeof window.mdacLogger !== 'undefined') {
                status.push('✅ Logger 已加载');
            } else {
                status.push('❌ Logger 未加载');
            }

            statusDiv.textContent = status.join('\n');
            statusDiv.className = 'result ' + (status.every(s => s.startsWith('✅')) ? 'success' : 'error');
        }

        function setupEventListeners() {
            // 手动测试按钮
            document.getElementById('manualTestBtn').addEventListener('click', async function() {
                const input = document.getElementById('manualInput').value.trim();
                if (!input) {
                    showResult('manualResult', '请输入测试文本', 'error');
                    return;
                }
                await runSingleTest(input, 'manualResult');
            });

            // 预设测试用例点击
            document.querySelectorAll('.test-case').forEach(testCase => {
                testCase.addEventListener('click', function() {
                    const text = this.dataset.text;
                    document.getElementById('manualInput').value = text;
                });
            });

            // 自动测试按钮
            document.getElementById('autoTestBtn').addEventListener('click', runAutoTests);
        }

        async function runSingleTest(input, resultId) {
            const button = document.getElementById('manualTestBtn');
            const autoButton = document.getElementById('autoTestBtn');
            
            button.disabled = true;
            autoButton.disabled = true;
            
            try {
                showResult(resultId, '正在解析地址...', 'info');
                
                if (typeof window.AddressResolver === 'undefined') {
                    throw new Error('AddressResolver 类未加载');
                }

                const resolver = new window.AddressResolver();
                const result = await resolver.resolveAddress(input);
                
                const output = {
                    输入: input,
                    结果: result,
                    耗时: result.metadata?.duration + 'ms',
                    方法: result.metadata?.method,
                    时间: new Date().toLocaleString()
                };
                
                showResult(resultId, JSON.stringify(output, null, 2), result.success ? 'success' : 'error');
                
            } catch (error) {
                const output = {
                    输入: input,
                    错误: error.message,
                    时间: new Date().toLocaleString()
                };
                showResult(resultId, JSON.stringify(output, null, 2), 'error');
            } finally {
                button.disabled = false;
                autoButton.disabled = false;
            }
        }

        async function runAutoTests() {
            const resultDiv = document.getElementById('autoTestResult');
            resultDiv.style.display = 'block';
            
            const button = document.getElementById('autoTestBtn');
            button.disabled = true;
            
            try {
                showResult('autoTestResult', '开始自动测试...', 'info');
                
                if (typeof window.AddressResolver === 'undefined') {
                    throw new Error('AddressResolver 类未加载');
                }

                const results = [];
                
                for (let i = 0; i < testCases.length; i++) {
                    const testCase = testCases[i];
                    showResult('autoTestResult', `正在测试 ${i + 1}/${testCases.length}: ${testCase.name}`, 'info');
                    
                    try {
                        const resolver = new window.AddressResolver();
                        const result = await resolver.resolveAddress(testCase.input);
                        
                        const testResult = {
                            测试: testCase.name,
                            输入: testCase.input,
                            成功: result.success,
                            数据: result.data,
                            方法: result.metadata?.method,
                            耗时: result.metadata?.duration + 'ms'
                        };
                        
                        results.push(testResult);
                        
                        // 简短等待避免过于频繁的请求
                        await new Promise(resolve => setTimeout(resolve, 500));
                        
                    } catch (error) {
                        results.push({
                            测试: testCase.name,
                            输入: testCase.input,
                            错误: error.message
                        });
                    }
                }
                
                const summary = {
                    总测试数: results.length,
                    成功数: results.filter(r => r.成功).length,
                    失败数: results.filter(r => !r.成功 || r.错误).length,
                    测试结果: results
                };
                
                showResult('autoTestResult', JSON.stringify(summary, null, 2), 'success');
                
            } catch (error) {
                showResult('autoTestResult', '自动测试失败: ' + error.message, 'error');
            } finally {
                button.disabled = false;
            }
        }

        function showResult(elementId, content, type) {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = 'result ' + type;
            element.style.display = 'block';
        }
    </script>
    
    <!-- 加载必要的模块 -->
    <script src="../modules/logger.js"></script>
    <script src="../modules/address-resolver.js"></script>
    <script src="../config/ai-config.js"></script>
</body>
</html>