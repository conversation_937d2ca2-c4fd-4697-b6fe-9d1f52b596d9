<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC Extension 架构重构知识图谱</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: white;
        }
        
        .tab:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .content {
            padding: 0;
        }
        
        .tab-content {
            display: none;
            min-height: 600px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        #architecture-diagram {
            width: 100%;
            height: 800px;
            background: #f8f9fa;
        }
        
        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .legend h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            font-size: 12px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            margin-right: 8px;
        }
        
        .info-panel {
            padding: 30px;
            line-height: 1.6;
        }
        
        .info-panel h3 {
            color: #495057;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .stage-list {
            list-style: none;
            padding: 0;
        }
        
        .stage-item {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .stage-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .stage-desc {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .component-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .component-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .component-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .component-features {
            list-style: none;
            padding: 0;
        }
        
        .component-features li {
            padding: 3px 0;
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .component-features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            stroke-width: 3px;
        }
        
        .link {
            fill: none;
            stroke-width: 2;
            opacity: 0.6;
            transition: all 0.3s ease;
        }
        
        .link:hover {
            opacity: 1;
            stroke-width: 3;
        }
        
        .node-label {
            font-size: 11px;
            font-weight: 500;
            text-anchor: middle;
            dominant-baseline: central;
            pointer-events: none;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .control-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 2px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }
        
        .control-button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ MDAC Extension 架构重构知识图谱</h1>
            <p>Chrome扩展双系统统一架构 - Stage 1 & 2 完整实现</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('overview')">📊 项目概览</button>
            <button class="tab" onclick="showTab('architecture')">🏗️ 架构图谱</button>
            <button class="tab" onclick="showTab('components')">🧩 核心组件</button>
            <button class="tab" onclick="showTab('implementation')">⚙️ 实现详情</button>
            <button class="tab" onclick="showTab('testing')">🧪 测试验证</button>
        </div>
        
        <div class="content">
            <!-- 项目概览 -->
            <div id="overview" class="tab-content active">
                <div class="info-panel">
                    <h3>🎯 项目目标与成果</h3>
                    <p><strong>核心问题</strong>: Chrome扩展出现"Could not establish connection"等模块加载错误</p>
                    <p><strong>解决方案</strong>: 方案B - 架构重构与统一，实现传统系统和模块化系统的协调工作</p>
                    <p><strong>项目状态</strong>: ✅ Stage 1 & Stage 2 已完成，系统已具备企业级稳定性</p>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">2</div>
                            <div class="stat-label">完成阶段</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">7</div>
                            <div class="stat-label">核心组件</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">修复问题</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">向后兼容</div>
                        </div>
                    </div>
                    
                    <h3>📋 实施阶段</h3>
                    <ul class="stage-list">
                        <li class="stage-item">
                            <div class="stage-title">✅ Stage 1: 基础修复</div>
                            <div class="stage-desc">修复Logger兼容性、消息重试机制、队列缓存、就绪通知等立即错误</div>
                        </li>
                        <li class="stage-item">
                            <div class="stage-title">✅ Stage 2: 统一架构</div>
                            <div class="stage-desc">实现双系统协调、热重启机制、统一事件总线、状态管理等核心架构</div>
                        </li>
                        <li class="stage-item">
                            <div class="stage-title">🔮 Stage 3: 高级优化 (可选)</div>
                            <div class="stage-desc">性能优化、监控仪表板、配置热更新、API扩展等增强功能</div>
                        </li>
                    </ul>
                    
                    <h3>🎯 关键成就</h3>
                    <ul>
                        <li><strong>错误修复</strong>: "Could not establish connection" 等核心错误已解决</li>
                        <li><strong>架构统一</strong>: 传统系统 + 模块化系统实现无缝协调</li>
                        <li><strong>容错机制</strong>: 热重启、降级模式、队列保护全面覆盖</li>
                        <li><strong>用户体验</strong>: 透明升级，无破坏性变更，稳定性大幅提升</li>
                        <li><strong>开发体验</strong>: 完整测试框架、实时监控、模块化错误处理</li>
                    </ul>
                </div>
            </div>
            
            <!-- 架构图谱 -->
            <div id="architecture" class="tab-content">
                <div style="position: relative;">
                    <div class="controls">
                        <h4 style="margin: 0 0 10px 0; color: #495057;">🎮 控制面板</h4>
                        <button class="control-button" onclick="resetZoom()">🔍 重置视图</button>
                        <button class="control-button" onclick="togglePhysics()">⚡ 切换物理引擎</button>
                        <button class="control-button" onclick="highlightPath()">🎯 高亮路径</button>
                    </div>
                    
                    <div class="legend">
                        <h4>📋 图例说明</h4>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #667eea;"></div>
                            <span>核心系统</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #28a745;"></div>
                            <span>统一架构</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #ffc107;"></div>
                            <span>配置组件</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #dc3545;"></div>
                            <span>错误处理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #6f42c1;"></div>
                            <span>模块组件</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #fd7e14;"></div>
                            <span>测试验证</span>
                        </div>
                    </div>
                    
                    <svg id="architecture-diagram"></svg>
                    <div class="tooltip" id="tooltip"></div>
                </div>
            </div>
            
            <!-- 核心组件 -->
            <div id="components" class="tab-content">
                <div class="info-panel">
                    <h3>🧩 核心组件详解</h3>
                    
                    <div class="component-grid">
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">🏗️</span>
                                统一模块注册表
                            </div>
                            <p><strong>文件</strong>: ui/unified-module-registry.js</p>
                            <ul class="component-features">
                                <li>自动检测双系统状态</li>
                                <li>统一事件总线管理</li>
                                <li>动态模块加载注册</li>
                                <li>系统桥接适配器</li>
                                <li>热重启监控机制</li>
                                <li>消息队列管理</li>
                                <li>降级模式保护</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">🚀</span>
                                统一架构启动器
                            </div>
                            <p><strong>文件</strong>: ui/unified-architecture-bootstrap.js</p>
                            <ul class="component-features">
                                <li>智能系统状态检测</li>
                                <li>4种启动策略选择</li>
                                <li>自动化启动协调</li>
                                <li>错误恢复降级</li>
                                <li>DOM就绪等待</li>
                                <li>紧急模式支持</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">🔗</span>
                                传统系统兼容
                            </div>
                            <p><strong>文件</strong>: ui/ui-sidepanel-main.js (增强)</p>
                            <ul class="component-features">
                                <li>统一架构兼容接口</li>
                                <li>事件系统桥接</li>
                                <li>自动就绪通知</li>
                                <li>状态同步机制</li>
                                <li>错误事件触发</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">🔄</span>
                                消息重试机制
                            </div>
                            <p><strong>实现</strong>: Stage 1 增强</p>
                            <ul class="component-features">
                                <li>3次重试 + 指数退避</li>
                                <li>消息队列缓存</li>
                                <li>内容脚本就绪检测</li>
                                <li>背景脚本队列管理</li>
                                <li>连接失败恢复</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">📡</span>
                                统一事件系统
                            </div>
                            <p><strong>全局</strong>: window.mdacEventBus</p>
                            <ul class="component-features">
                                <li>跨系统事件通信</li>
                                <li>事件监听管理</li>
                                <li>错误隔离处理</li>
                                <li>系统间桥接转发</li>
                                <li>实时状态同步</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">🗂️</span>
                                统一状态管理
                            </div>
                            <p><strong>全局</strong>: window.mdacUnifiedStateManager</p>
                            <ul class="component-features">
                                <li>中央化状态存储</li>
                                <li>状态变化事件</li>
                                <li>原子性操作</li>
                                <li>持久化支持</li>
                                <li>实时状态同步</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">🔥</span>
                                热重启机制
                            </div>
                            <p><strong>功能</strong>: 自动故障恢复</p>
                            <ul class="component-features">
                                <li>最多3次重启尝试</li>
                                <li>状态保存恢复</li>
                                <li>模块重新加载</li>
                                <li>降级模式切换</li>
                                <li>错误分类处理</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">
                                <span class="component-icon">🧪</span>
                                测试验证框架
                            </div>
                            <p><strong>文件</strong>: test-stage1-fixes.js, test-stage2-unified.js</p>
                            <ul class="component-features">
                                <li>自动化测试套件</li>
                                <li>功能完整性验证</li>
                                <li>性能监控测试</li>
                                <li>错误处理验证</li>
                                <li>详细测试报告</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 实现详情 -->
            <div id="implementation" class="tab-content">
                <div class="info-panel">
                    <h3>⚙️ 技术实现详情</h3>
                    
                    <h4>🔧 Stage 1: 基础修复实现</h4>
                    <ul>
                        <li><strong>Logger兼容性</strong>: 在 modules/logger.js 中添加缺失的 setLevel 方法</li>
                        <li><strong>消息重试</strong>: ui/ui-sidepanel-main.js 中实现 sendMessageWithRetry 函数</li>
                        <li><strong>队列缓存</strong>: background/background.js 中添加消息队列管理系统</li>
                        <li><strong>就绪通知</strong>: content/content-script.js 中添加初始化完成通知</li>
                        <li><strong>路径验证</strong>: manifest.json 中确认所有模块路径正确</li>
                    </ul>
                    
                    <h4>🏗️ Stage 2: 统一架构实现</h4>
                    <ul>
                        <li><strong>统一注册表</strong>: 创建 ui/unified-module-registry.js，实现模块管理和系统协调</li>
                        <li><strong>架构启动器</strong>: 创建 ui/unified-architecture-bootstrap.js，实现智能启动策略</li>
                        <li><strong>系统桥接</strong>: 为传统和模块化系统创建适配器接口</li>
                        <li><strong>热重启机制</strong>: 实现3次重试的自动故障恢复</li>
                        <li><strong>事件统一</strong>: 建立全局事件总线 window.mdacEventBus</li>
                        <li><strong>状态管理</strong>: 创建统一状态管理器 window.mdacUnifiedStateManager</li>
                        <li><strong>配置更新</strong>: 更新 HTML 加载顺序和 manifest.json 资源配置</li>
                    </ul>
                    
                    <h4>📊 启动策略选择逻辑</h4>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-size: 12px;">
// 策略选择算法
function determineBootstrapStrategy(systemStatus) {
    // 策略1: 理想情况 - 两个系统都正常
    if (traditional.ok && modular.ok) return 'unified';
    
    // 策略2: 传统系统正常，模块化有问题  
    if (traditional.ok && !modular.ok) return 'traditional-priority';
    
    // 策略3: 模块化正常，传统有问题
    if (!traditional.ok && modular.ok) return 'modular-priority';
    
    // 策略4: 两个系统都有问题
    return 'emergency';
}
                    </pre>
                    
                    <h4>🔄 错误恢复流程</h4>
                    <ol>
                        <li><strong>错误检测</strong>: 事件总线接收 critical-error, module-failure, connection-failure</li>
                        <li><strong>错误分类</strong>: 根据错误类型选择对应的恢复策略</li>
                        <li><strong>重试判断</strong>: 检查热重启次数是否小于3次</li>
                        <li><strong>状态保存</strong>: 保存当前状态和消息队列</li>
                        <li><strong>系统重启</strong>: 重新初始化核心组件</li>
                        <li><strong>状态恢复</strong>: 恢复之前保存的状态和队列</li>
                        <li><strong>降级保护</strong>: 如果重试失败，进入降级模式</li>
                    </ol>
                    
                    <h4>🎯 API兼容性接口</h4>
                    <ul>
                        <li><strong>传统系统</strong>: updateToMDAC, collectPersonalData, saveData 等</li>
                        <li><strong>模块化系统</strong>: getModuleStatus, reloadModule, toggleDebugMode 等</li>
                        <li><strong>统一接口</strong>: 事件发送、状态管理、错误处理统一API</li>
                        <li><strong>桥接适配</strong>: 双向事件转发和数据同步机制</li>
                    </ul>
                </div>
            </div>
            
            <!-- 测试验证 -->
            <div id="testing" class="tab-content">
                <div class="info-panel">
                    <h3>🧪 测试验证体系</h3>
                    
                    <h4>📋 Stage 1 测试覆盖 (test-stage1-fixes.js)</h4>
                    <ul>
                        <li><strong>Logger方法检查</strong>: 验证 setLevel, startPerformance, endPerformance 方法存在性</li>
                        <li><strong>消息重试机制</strong>: 测试背景脚本通信和重试逻辑</li>
                        <li><strong>Manifest资源</strong>: 验证所有模块路径的可访问性</li>
                        <li><strong>模块加载</strong>: 检查核心模块的正确加载</li>
                    </ul>
                    
                    <h4>🏗️ Stage 2 测试覆盖 (test-stage2-unified.js)</h4>
                    <div class="component-grid">
                        <div class="component-card">
                            <div class="component-title">🚀 启动器测试</div>
                            <ul class="component-features">
                                <li>UnifiedArchitectureBootstrap 类检查</li>
                                <li>启动器实例创建验证</li>
                                <li>初始化状态检查</li>
                                <li>启动策略选择测试</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">📦 注册表测试</div>
                            <ul class="component-features">
                                <li>UnifiedModuleRegistry 类检查</li>
                                <li>注册表实例验证</li>
                                <li>模块计数统计</li>
                                <li>初始化状态确认</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">🔍 系统检测测试</div>
                            <ul class="component-features">
                                <li>传统系统检测</li>
                                <li>模块化系统检测</li>
                                <li>双系统状态验证</li>
                                <li>错误状态检查</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">📡 事件系统测试</div>
                            <ul class="component-features">
                                <li>事件总线可用性</li>
                                <li>事件发送接收</li>
                                <li>事件处理验证</li>
                                <li>跨系统通信测试</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">🗂️ 状态管理测试</div>
                            <ul class="component-features">
                                <li>状态设置获取</li>
                                <li>状态删除操作</li>
                                <li>状态变化事件</li>
                                <li>数据持久化</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">🔥 热重启测试</div>
                            <ul class="component-features">
                                <li>重启计数器检查</li>
                                <li>错误处理函数验证</li>
                                <li>降级模式配置</li>
                                <li>监控功能确认</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">📝 队列系统测试</div>
                            <ul class="component-features">
                                <li>消息队列初始化</li>
                                <li>队列计数统计</li>
                                <li>消息缓存机制</li>
                                <li>自动处理验证</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">🔗 桥接适配测试</div>
                            <ul class="component-features">
                                <li>传统系统适配器</li>
                                <li>模块化系统适配器</li>
                                <li>接口桥接验证</li>
                                <li>双向通信测试</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">⚠️ 错误处理测试</div>
                            <ul class="component-features">
                                <li>降级模式检查</li>
                                <li>错误处理函数</li>
                                <li>恢复机制验证</li>
                                <li>紧急模式支持</li>
                            </ul>
                        </div>
                        
                        <div class="component-card">
                            <div class="component-title">🎯 API接口测试</div>
                            <ul class="component-features">
                                <li>启动器API检查</li>
                                <li>注册表API验证</li>
                                <li>全局API可用性</li>
                                <li>接口完整性确认</li>
                            </ul>
                        </div>
                    </div>
                    
                    <h4>📊 测试执行流程</h4>
                    <ol>
                        <li><strong>自动延迟</strong>: 3秒延迟确保所有系统加载完成</li>
                        <li><strong>测试执行</strong>: 逐项运行10个核心测试</li>
                        <li><strong>结果统计</strong>: 计算通过率和失败率</li>
                        <li><strong>报告生成</strong>: 创建详细的测试报告</li>
                        <li><strong>状态保存</strong>: 将报告保存到全局变量供检查</li>
                    </ol>
                    
                    <h4>🎯 验证指标</h4>
                    <ul>
                        <li><strong>功能完整性</strong>: 所有核心功能正常工作</li>
                        <li><strong>错误恢复</strong>: 故障自动恢复机制有效</li>
                        <li><strong>性能稳定</strong>: 系统响应时间在可接受范围</li>
                        <li><strong>兼容性</strong>: 现有功能无破坏性变更</li>
                        <li><strong>可靠性</strong>: 长时间运行稳定性</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 图谱数据定义
        const graphData = {
            nodes: [
                // 核心系统
                { id: "traditional", name: "传统系统\n(ui-sidepanel-main.js)", group: "core", color: "#667eea", size: 25 },
                { id: "modular", name: "模块化系统\n(ui/sidepanel/*)", group: "core", color: "#667eea", size: 25 },
                
                // 统一架构
                { id: "bootstrap", name: "统一架构启动器\n(bootstrap.js)", group: "unified", color: "#28a745", size: 30 },
                { id: "registry", name: "统一模块注册表\n(registry.js)", group: "unified", color: "#28a745", size: 30 },
                { id: "eventbus", name: "统一事件总线\n(mdacEventBus)", group: "unified", color: "#28a745", size: 20 },
                { id: "statemanager", name: "统一状态管理\n(StateManager)", group: "unified", color: "#28a745", size: 20 },
                
                // 配置组件  
                { id: "manifest", name: "Manifest配置\n(manifest.json)", group: "config", color: "#ffc107", size: 15 },
                { id: "html", name: "HTML入口\n(ui-sidepanel.html)", group: "config", color: "#ffc107", size: 15 },
                { id: "aiconfig", name: "AI配置\n(ai-config.js)", group: "config", color: "#ffc107", size: 15 },
                
                // 错误处理
                { id: "retry", name: "消息重试机制\n(sendMessageWithRetry)", group: "error", color: "#dc3545", size: 18 },
                { id: "queue", name: "消息队列\n(messageQueue)", group: "error", color: "#dc3545", size: 18 },
                { id: "hotreload", name: "热重启机制\n(hotReload)", group: "error", color: "#dc3545", size: 18 },
                { id: "degraded", name: "降级模式\n(degradedMode)", group: "error", color: "#dc3545", size: 18 },
                
                // 模块组件
                { id: "logger", name: "Logger模块\n(logger.js)", group: "module", color: "#6f42c1", size: 15 },
                { id: "detector", name: "字段检测器\n(field-detector.js)", group: "module", color: "#6f42c1", size: 15 },
                { id: "maps", name: "地图集成\n(maps-integration.js)", group: "module", color: "#6f42c1", size: 15 },
                { id: "monitor", name: "填充监控\n(fill-monitor.js)", group: "module", color: "#6f42c1", size: 15 },
                { id: "recovery", name: "错误恢复\n(error-recovery.js)", group: "module", color: "#6f42c1", size: 15 },
                
                // 测试验证
                { id: "test1", name: "Stage1测试\n(test-stage1-fixes.js)", group: "test", color: "#fd7e14", size: 15 },
                { id: "test2", name: "Stage2测试\n(test-stage2-unified.js)", group: "test", color: "#fd7e14", size: 15 },
                
                // 桥接适配器
                { id: "traditional-adapter", name: "传统系统适配器", group: "unified", color: "#28a745", size: 18 },
                { id: "modular-adapter", name: "模块化系统适配器", group: "unified", color: "#28a745", size: 18 },
                
                // 背景脚本
                { id: "background", name: "背景脚本\n(background.js)", group: "core", color: "#667eea", size: 20 },
                { id: "content", name: "内容脚本\n(content-script.js)", group: "core", color: "#667eea", size: 20 }
            ],
            
            links: [
                // 启动器连接
                { source: "html", target: "bootstrap", type: "loads" },
                { source: "bootstrap", target: "registry", type: "creates" },
                { source: "bootstrap", target: "traditional", type: "detects" },
                { source: "bootstrap", target: "modular", type: "detects" },
                
                // 注册表连接
                { source: "registry", target: "eventbus", type: "creates" },
                { source: "registry", target: "statemanager", type: "creates" },
                { source: "registry", target: "traditional-adapter", type: "creates" },
                { source: "registry", target: "modular-adapter", type: "creates" },
                { source: "registry", target: "hotreload", type: "manages" },
                { source: "registry", target: "queue", type: "manages" },
                
                // 系统桥接
                { source: "traditional-adapter", target: "traditional", type: "bridges" },
                { source: "modular-adapter", target: "modular", type: "bridges" },
                { source: "traditional", target: "eventbus", type: "uses" },
                { source: "modular", target: "eventbus", type: "uses" },
                
                // 配置依赖
                { source: "manifest", target: "html", type: "configures" },
                { source: "manifest", target: "background", type: "configures" },
                { source: "manifest", target: "content", type: "configures" },
                { source: "html", target: "aiconfig", type: "loads" },
                { source: "html", target: "traditional", type: "loads" },
                
                // 错误处理
                { source: "traditional", target: "retry", type: "uses" },
                { source: "background", target: "queue", type: "manages" },
                { source: "hotreload", target: "degraded", type: "fallsback" },
                { source: "registry", target: "degraded", type: "activates" },
                
                // 模块依赖
                { source: "traditional", target: "logger", type: "uses" },
                { source: "traditional", target: "detector", type: "uses" },
                { source: "traditional", target: "maps", type: "uses" },
                { source: "modular", target: "monitor", type: "uses" },
                { source: "modular", target: "recovery", type: "uses" },
                { source: "registry", target: "logger", type: "registers" },
                { source: "registry", target: "detector", type: "registers" },
                { source: "registry", target: "maps", type: "registers" },
                { source: "registry", target: "monitor", target: "registers" },
                { source: "registry", target: "recovery", type: "registers" },
                
                // 测试验证
                { source: "test1", target: "logger", type: "tests" },
                { source: "test1", target: "retry", type: "tests" },
                { source: "test1", target: "manifest", type: "tests" },
                { source: "test2", target: "bootstrap", type: "tests" },
                { source: "test2", target: "registry", type: "tests" },
                { source: "test2", target: "eventbus", type: "tests" },
                { source: "test2", target: "statemanager", type: "tests" },
                
                // 通信连接
                { source: "traditional", target: "background", type: "communicates" },
                { source: "background", target: "content", type: "communicates" },
                { source: "content", target: "traditional", type: "notifies" }
            ]
        };
        
        // D3.js 图谱渲染
        let simulation;
        let physicsEnabled = true;
        
        function initializeArchitectureDiagram() {
            const svg = d3.select("#architecture-diagram");
            const width = svg.node().getBoundingClientRect().width;
            const height = svg.node().getBoundingClientRect().height;
            
            svg.selectAll("*").remove();
            
            // 创建缩放行为
            const zoom = d3.zoom()
                .scaleExtent([0.5, 3])
                .on("zoom", (event) => {
                    container.attr("transform", event.transform);
                });
            
            svg.call(zoom);
            
            const container = svg.append("g");
            
            // 创建力导向布局
            simulation = d3.forceSimulation(graphData.nodes)
                .force("link", d3.forceLink(graphData.links).id(d => d.id).distance(100).strength(0.5))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(width / 2, height / 2))
                .force("collision", d3.forceCollide().radius(d => d.size + 5));
            
            // 创建连接线
            const link = container.append("g")
                .selectAll("line")
                .data(graphData.links)
                .enter().append("line")
                .attr("class", "link")
                .style("stroke", "#999")
                .style("stroke-opacity", 0.6)
                .style("stroke-width", 2);
            
            // 创建节点
            const node = container.append("g")
                .selectAll("circle")
                .data(graphData.nodes)
                .enter().append("circle")
                .attr("class", "node")
                .attr("r", d => d.size)
                .style("fill", d => d.color)
                .style("stroke", "#fff")
                .style("stroke-width", 2)
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended))
                .on("mouseover", function(event, d) {
                    showTooltip(event, d);
                    d3.select(this).style("stroke-width", 4);
                })
                .on("mouseout", function(event, d) {
                    hideTooltip();
                    d3.select(this).style("stroke-width", 2);
                });
            
            // 创建标签
            const label = container.append("g")
                .selectAll("text")
                .data(graphData.nodes)
                .enter().append("text")
                .attr("class", "node-label")
                .text(d => d.name)
                .style("font-size", "10px")
                .style("text-anchor", "middle")
                .style("fill", "#333");
            
            // 力导向布局更新
            simulation.on("tick", () => {
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                node
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                
                label
                    .attr("x", d => d.x)
                    .attr("y", d => d.y);
            });
            
            // 拖拽函数
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }
            
            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }
            
            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
            
            // 保存引用
            window.svgZoom = zoom;
            window.svgContainer = container;
        }
        
        // 工具提示
        function showTooltip(event, d) {
            const tooltip = d3.select("#tooltip");
            tooltip.transition().duration(200).style("opacity", 1);
            tooltip.html(`
                <strong>${d.name}</strong><br>
                类型: ${d.group}<br>
                大小: ${d.size}
            `)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px");
        }
        
        function hideTooltip() {
            d3.select("#tooltip").transition().duration(200).style("opacity", 0);
        }
        
        // 控制函数
        function resetZoom() {
            if (window.svgZoom) {
                d3.select("#architecture-diagram").transition().duration(750).call(
                    window.svgZoom.transform,
                    d3.zoomIdentity
                );
            }
        }
        
        function togglePhysics() {
            physicsEnabled = !physicsEnabled;
            if (simulation) {
                if (physicsEnabled) {
                    simulation.alphaTarget(0.3).restart();
                } else {
                    simulation.stop();
                }
            }
        }
        
        function highlightPath() {
            // 高亮关键路径: HTML -> Bootstrap -> Registry -> EventBus
            const keyPath = ["html", "bootstrap", "registry", "eventbus"];
            
            d3.selectAll(".node")
                .style("opacity", d => keyPath.includes(d.id) ? 1 : 0.3);
            
            d3.selectAll(".link")
                .style("opacity", d => {
                    return (keyPath.includes(d.source.id) && keyPath.includes(d.target.id)) ? 1 : 0.1;
                });
            
            // 3秒后恢复
            setTimeout(() => {
                d3.selectAll(".node").style("opacity", 1);
                d3.selectAll(".link").style("opacity", 0.6);
            }, 3000);
        }
        
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应的标签
            event.target.classList.add('active');
            
            // 如果是架构图谱标签，初始化图表
            if (tabName === 'architecture') {
                setTimeout(initializeArchitectureDiagram, 100);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏗️ MDAC Extension 知识图谱已加载');
        });
    </script>
</body>
</html>
