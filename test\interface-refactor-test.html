<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>界面重构测试</title>
    <link rel="stylesheet" href="../ui/ui-sidepanel.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: #667eea;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .test-inputs {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }
        .test-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .test-button-row {
            display: flex;
            gap: 10px;
        }
        .test-status {
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .test-success {
            background: #dcfce7;
            color: #15803d;
            border: 1px solid #bbf7d0;
        }
        .test-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>界面重构测试</h1>
            <p>测试统一组件样式系统</p>
        </div>
        
        <div class="test-content">
            <!-- 状态指示 -->
            <div class="test-status test-success">
                ✅ 统一样式系统已加载
            </div>
            
            <!-- 按钮测试 -->
            <div class="test-section">
                <h3>按钮组件测试</h3>
                
                <div class="test-buttons">
                    <button class="btn-base btn-primary">主要按钮</button>
                    <button class="btn-base btn-gradient">渐变按钮</button>
                    <button class="btn-base">默认按钮</button>
                    <button class="btn-base btn-small">小按钮</button>
                    <button class="btn-base btn-large">大按钮</button>
                </div>
                
                <div class="test-button-row">
                    <button class="btn-base btn-primary flex-1">
                        <span>🚀</span>
                        <span>带图标</span>
                    </button>
                    <button class="btn-base btn-icon-only">
                        <span>📷</span>
                    </button>
                </div>
            </div>
            
            <!-- 输入框测试 -->
            <div class="test-section">
                <h3>输入框组件测试</h3>
                
                <div class="test-inputs">
                    <input type="text" class="input-base" placeholder="默认输入框">
                    <input type="text" class="input-base input-small" placeholder="小输入框">
                    <input type="text" class="input-base input-large" placeholder="大输入框">
                    <select class="input-base">
                        <option>选择选项</option>
                        <option>选项1</option>
                        <option>选项2</option>
                    </select>
                    <textarea class="input-base input-textarea" placeholder="文本区域" rows="3"></textarea>
                </div>
            </div>
            
            <!-- 卡片测试 -->
            <div class="test-section">
                <h3>卡片组件测试</h3>
                
                <div class="card-base card-padding-small">
                    <div class="card-header">
                        <span>📋</span>
                        <span>卡片标题</span>
                    </div>
                    <div class="card-body">
                        <p>这是卡片内容区域。</p>
                        <div class="flex gap-sm">
                            <input type="text" class="input-base flex-1" placeholder="卡片内输入">
                            <button class="btn-base btn-primary">提交</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 布局测试 -->
            <div class="test-section">
                <h3>布局工具类测试</h3>
                
                <div class="card-base card-padding">
                    <div class="flex items-center justify-between">
                        <span>Flex 布局</span>
                        <span class="status-badge success">成功</span>
                    </div>
                    
                    <div class="flex-col gap-sm" style="margin-top: 10px;">
                        <div class="flex items-center gap-xs">
                            <span class="status-dot"></span>
                            <span>状态指示器</span>
                        </div>
                        <div class="flex items-center gap-xs">
                            <span class="status-badge warning">警告</span>
                            <span>状态徽章</span>
                        </div>
                        <div class="flex items-center gap-xs">
                            <span class="status-badge error">错误</span>
                            <span>错误状态</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 网格测试 -->
            <div class="test-section">
                <h3>网格布局测试</h3>
                
                <div class="test-grid">
                    <div class="card-base card-padding-small">
                        <div class="flex-col items-center gap-sm">
                            <span>🎯</span>
                            <span>网格项 1</span>
                        </div>
                    </div>
                    <div class="card-base card-padding-small">
                        <div class="flex-col items-center gap-sm">
                            <span>📊</span>
                            <span>网格项 2</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试结果 -->
            <div class="test-section">
                <h3>测试结果</h3>
                <div class="test-status test-info">
                    <div class="flex items-center gap-xs">
                        <span>ℹ️</span>
                        <span>所有组件样式加载正常</span>
                    </div>
                </div>
                
                <button class="btn-base btn-gradient btn-full-width btn-large">
                    <span>🎉</span>
                    <span>重构测试完成</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 测试交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 测试按钮点击
            document.querySelectorAll('.btn-base').forEach(button => {
                button.addEventListener('click', function() {
                    console.log('按钮点击:', this.textContent);
                });
            });
            
            // 测试输入框焦点
            document.querySelectorAll('.input-base').forEach(input => {
                input.addEventListener('focus', function() {
                    console.log('输入框获得焦点:', this.placeholder);
                });
            });
            
            // 输出测试信息
            console.log('✅ 界面重构测试页面已加载');
            console.log('🎯 测试统一组件样式系统');
            console.log('📊 检查所有按钮、输入框、卡片和布局组件');
        });
    </script>
</body>
</html>