/**
 * MDAC 统一架构启动器
 * Stage 2: 协调传统系统和模块化系统的启动
 */

class UnifiedArchitectureBootstrap {
    constructor() {
        this.startTime = performance.now();
        this.registry = null;
        this.initializationPromise = null;
        this.isInitialized = false;
        
        console.log('🚀 [UnifiedBootstrap] 统一架构启动器初始化中...');
        this.init();
    }

    async init() {
        try {
            // 避免重复初始化
            if (this.initializationPromise) {
                return this.initializationPromise;
            }

            this.initializationPromise = this.performInitialization();
            await this.initializationPromise;
            
        } catch (error) {
            console.error('❌ [UnifiedBootstrap] 初始化失败:', error);
            await this.handleBootstrapFailure(error);
        }
    }

    async performInitialization() {
        console.log('📋 [UnifiedBootstrap] 开始统一架构初始化...');

        // 步骤1: 等待DOM就绪
        await this.waitForDOM();
        
        // 步骤2: 检测现有系统状态
        const systemStatus = await this.detectSystemStatus();
        
        // 步骤3: 决定启动策略
        const strategy = this.determineBootstrapStrategy(systemStatus);
        console.log(`📊 [UnifiedBootstrap] 采用启动策略: ${strategy}`);
        
        // 步骤4: 根据策略执行启动
        switch (strategy) {
            case 'unified':
                await this.startUnifiedMode();
                break;
            case 'traditional-priority':
                await this.startTraditionalPriorityMode();
                break;
            case 'modular-priority':
                await this.startModularPriorityMode();
                break;
            case 'emergency':
                await this.startEmergencyMode();
                break;
            default:
                throw new Error(`未知的启动策略: ${strategy}`);
        }
        
        // 步骤5: 完成初始化
        await this.finalizeInitialization();
        
        const totalTime = performance.now() - this.startTime;
        console.log(`✅ [UnifiedBootstrap] 统一架构启动完成，耗时: ${totalTime.toFixed(2)}ms`);
    }

    async waitForDOM() {
        if (document.readyState === 'complete') {
            return;
        }
        
        return new Promise(resolve => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }

    async detectSystemStatus() {
        console.log('🔍 [UnifiedBootstrap] 检测系统状态...');
        
        const status = {
            traditional: {
                loaded: typeof window.mdacSidePanelApp !== 'undefined',
                initialized: window.mdacSidePanelApp?.isInitialized || false,
                error: null
            },
            modular: {
                loaded: typeof window.mdacModularSidePanel !== 'undefined',
                initialized: window.mdacModularSidePanel?.isInitialized || false,
                error: null
            },
            eventBus: {
                available: typeof window.mdacEventBus !== 'undefined',
                working: false
            },
            bootstrap: {
                ultimate: typeof window.mdacUltimateBootstrap !== 'undefined',
                enhanced: typeof window.mdacEnhancedBootstrap !== 'undefined',
                simple: typeof window.mdacSimpleBootstrap !== 'undefined'
            }
        };

        // 测试事件总线
        if (status.eventBus.available) {
            try {
                window.mdacEventBus.emit('test-event', {});
                status.eventBus.working = true;
            } catch (error) {
                status.eventBus.error = error.message;
            }
        }

        // 检查传统系统错误
        if (status.traditional.loaded) {
            try {
                const app = window.mdacSidePanelApp;
                if (app.lastError) {
                    status.traditional.error = app.lastError;
                }
            } catch (error) {
                status.traditional.error = error.message;
            }
        }

        // 检查模块化系统错误
        if (status.modular.loaded) {
            try {
                const app = window.mdacModularSidePanel;
                const moduleStatus = app.getModuleStatus?.();
                if (moduleStatus?.failedModules?.length > 0) {
                    status.modular.error = `${moduleStatus.failedModules.length} 个模块加载失败`;
                }
            } catch (error) {
                status.modular.error = error.message;
            }
        }

        console.log('📊 [UnifiedBootstrap] 系统状态检测完成:', status);
        return status;
    }

    determineBootstrapStrategy(systemStatus) {
        // 策略1: 理想情况 - 两个系统都正常
        if (systemStatus.traditional.loaded && !systemStatus.traditional.error &&
            systemStatus.modular.loaded && !systemStatus.modular.error) {
            return 'unified';
        }

        // 策略2: 传统系统正常，模块化系统有问题
        if (systemStatus.traditional.loaded && !systemStatus.traditional.error &&
            (!systemStatus.modular.loaded || systemStatus.modular.error)) {
            return 'traditional-priority';
        }

        // 策略3: 模块化系统正常，传统系统有问题
        if (systemStatus.modular.loaded && !systemStatus.modular.error &&
            (!systemStatus.traditional.loaded || systemStatus.traditional.error)) {
            return 'modular-priority';
        }

        // 策略4: 两个系统都有问题
        return 'emergency';
    }

    async startUnifiedMode() {
        console.log('🔗 [UnifiedBootstrap] 启动统一模式...');
        
        // 加载统一模块注册表
        await this.loadUnifiedRegistry();
        
        // 创建注册表实例
        this.registry = new window.UnifiedModuleRegistry();
        
        // 等待注册表初始化完成
        await new Promise((resolve) => {
            const checkInit = () => {
                if (this.registry.isInitialized) {
                    resolve();
                } else {
                    setTimeout(checkInit, 100);
                }
            };
            checkInit();
        });

        // 建立系统协调
        this.establishSystemCoordination();
        
        console.log('✅ [UnifiedBootstrap] 统一模式启动完成');
    }

    async startTraditionalPriorityMode() {
        console.log('🏛️ [UnifiedBootstrap] 启动传统优先模式...');
        
        // 确保传统系统正常运行
        if (!window.mdacSidePanelApp.isInitialized) {
            await this.waitForTraditionalSystem();
        }

        // 加载统一注册表但以传统系统为主
        await this.loadUnifiedRegistry();
        this.registry = new window.UnifiedModuleRegistry();
        
        // 设置传统优先模式
        if (this.registry.stateManager) {
            this.registry.stateManager.setState('primarySystem', 'traditional');
            this.registry.stateManager.setState('fallbackMode', 'modular');
        }

        console.log('✅ [UnifiedBootstrap] 传统优先模式启动完成');
    }

    async startModularPriorityMode() {
        console.log('🧩 [UnifiedBootstrap] 启动模块化优先模式...');
        
        // 确保模块化系统正常运行
        if (!window.mdacModularSidePanel?.isInitialized) {
            await this.waitForModularSystem();
        }

        // 加载统一注册表但以模块化系统为主
        await this.loadUnifiedRegistry();
        this.registry = new window.UnifiedModuleRegistry();
        
        // 设置模块化优先模式
        if (this.registry.stateManager) {
            this.registry.stateManager.setState('primarySystem', 'modular');
            this.registry.stateManager.setState('fallbackMode', 'traditional');
        }

        console.log('✅ [UnifiedBootstrap] 模块化优先模式启动完成');
    }

    async startEmergencyMode() {
        console.log('🚨 [UnifiedBootstrap] 启动紧急模式...');
        
        // 尝试最小化功能启动
        try {
            // 创建最基本的事件总线
            if (!window.mdacEventBus) {
                window.mdacEventBus = {
                    listeners: new Map(),
                    on(event, callback) { 
                        if (!this.listeners.has(event)) this.listeners.set(event, []);
                        this.listeners.get(event).push(callback);
                    },
                    emit(event, data) {
                        if (this.listeners.has(event)) {
                            this.listeners.get(event).forEach(cb => {
                                try { cb(data); } catch (e) { console.error(e); }
                            });
                        }
                    }
                };
            }

            // 创建基本状态管理
            window.mdacEmergencyState = {
                mode: 'emergency',
                startTime: Date.now(),
                errors: []
            };

            // 尝试加载核心功能
            await this.loadEmergencyFeatures();

            console.log('✅ [UnifiedBootstrap] 紧急模式启动完成');
            
        } catch (error) {
            console.error('❌ [UnifiedBootstrap] 紧急模式启动失败:', error);
            this.fallbackToBasicMode();
        }
    }

    async loadUnifiedRegistry() {
        if (typeof window.UnifiedModuleRegistry !== 'undefined') {
            console.log('✅ [UnifiedBootstrap] 统一注册表已加载');
            return;
        }

        console.log('📦 [UnifiedBootstrap] 加载统一注册表...');
        
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('ui/unified-module-registry.js');
            
            script.onload = () => {
                if (typeof window.UnifiedModuleRegistry !== 'undefined') {
                    console.log('✅ [UnifiedBootstrap] 统一注册表加载成功');
                    resolve();
                } else {
                    reject(new Error('统一注册表加载后未找到'));
                }
            };
            
            script.onerror = () => {
                reject(new Error('统一注册表脚本加载失败'));
            };
            
            document.head.appendChild(script);
        });
    }

    establishSystemCoordination() {
        console.log('🤝 [UnifiedBootstrap] 建立系统协调...');
        
        if (!this.registry || !window.mdacEventBus) return;

        // 协调事件转发
        const traditionalApp = window.mdacSidePanelApp;
        const modularApp = window.mdacModularSidePanel;

        // 从传统系统转发事件到统一事件总线
        if (traditionalApp) {
            ['dataUpdated', 'formFilled', 'errorOccurred'].forEach(eventType => {
                // 如果传统系统有事件监听能力
                if (typeof traditionalApp.addEventListener === 'function') {
                    traditionalApp.addEventListener(eventType, (data) => {
                        window.mdacEventBus.emit(`traditional:${eventType}`, data);
                    });
                }
            });
        }

        // 从模块化系统转发事件
        if (modularApp && modularApp.eventBus) {
            ['moduleLoaded', 'moduleError', 'stateChanged'].forEach(eventType => {
                modularApp.eventBus.on(eventType, (data) => {
                    window.mdacEventBus.emit(`modular:${eventType}`, data);
                });
            });
        }

        // 建立双向通信
        this.establishBidirectionalCommunication();
        
        console.log('✅ [UnifiedBootstrap] 系统协调建立完成');
    }

    establishBidirectionalCommunication() {
        if (!window.mdacEventBus) return;

        // 统一的数据更新事件
        window.mdacEventBus.on('data-update-request', (data) => {
            // 优先使用传统系统的updateToMDAC
            if (window.mdacSidePanelApp?.updateToMDAC) {
                window.mdacSidePanelApp.updateToMDAC();
            } else if (window.mdacModularSidePanel?.modules?.FormFiller) {
                window.mdacModularSidePanel.modules.FormFiller.fillForm(data);
            }
        });

        // 统一的数据保存事件
        window.mdacEventBus.on('data-save-request', (data) => {
            if (window.mdacSidePanelApp?.saveData) {
                window.mdacSidePanelApp.saveData();
            } else if (window.mdacModularSidePanel?.modules?.DataManager) {
                window.mdacModularSidePanel.modules.DataManager.saveData(data);
            }
        });

        // 统一的错误处理事件
        window.mdacEventBus.on('error-recovery-request', (error) => {
            if (this.registry) {
                this.registry.emitEvent('critical-error', error);
            }
        });
    }

    async waitForTraditionalSystem() {
        console.log('⏳ [UnifiedBootstrap] 等待传统系统初始化...');
        
        let attempts = 0;
        const maxAttempts = 50; // 5秒超时
        
        while (attempts < maxAttempts) {
            if (window.mdacSidePanelApp?.isInitialized) {
                console.log('✅ [UnifiedBootstrap] 传统系统已就绪');
                return;
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        console.warn('⚠️ [UnifiedBootstrap] 传统系统初始化超时');
    }

    async waitForModularSystem() {
        console.log('⏳ [UnifiedBootstrap] 等待模块化系统初始化...');
        
        let attempts = 0;
        const maxAttempts = 50; // 5秒超时
        
        while (attempts < maxAttempts) {
            if (window.mdacModularSidePanel?.isInitialized) {
                console.log('✅ [UnifiedBootstrap] 模块化系统已就绪');
                return;
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }
        
        console.warn('⚠️ [UnifiedBootstrap] 模块化系统初始化超时');
    }

    async loadEmergencyFeatures() {
        console.log('🔧 [UnifiedBootstrap] 加载紧急功能...');
        
        // 尝试加载最基本的Logger
        try {
            const loggerScript = document.createElement('script');
            loggerScript.src = chrome.runtime.getURL('modules/logger.js');
            document.head.appendChild(loggerScript);
            
            // 简单等待
            await new Promise(resolve => setTimeout(resolve, 500));
            
            if (window.logger) {
                console.log('✅ [UnifiedBootstrap] 紧急Logger加载成功');
            }
        } catch (error) {
            console.warn('⚠️ [UnifiedBootstrap] 紧急Logger加载失败:', error);
        }

        // 创建紧急UI提示
        this.createEmergencyUI();
    }

    createEmergencyUI() {
        console.log('🎨 [UnifiedBootstrap] 创建紧急UI...');
        
        const emergencyDiv = document.createElement('div');
        emergencyDiv.id = 'mdac-emergency-mode';
        emergencyDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #ff6b6b;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
            max-width: 200px;
        `;
        emergencyDiv.innerHTML = `
            ⚠️ MDAC运行在紧急模式<br>
            部分功能可能不可用<br>
        `;
        
        // 创建刷新按钮并添加事件监听器
        const refreshButton = document.createElement('button');
        refreshButton.textContent = '刷新重试';
        refreshButton.style.marginTop = '5px';
        refreshButton.addEventListener('click', () => {
            location.reload();
        });
        
        emergencyDiv.appendChild(refreshButton);
        document.body.appendChild(emergencyDiv);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            emergencyDiv.style.opacity = '0.5';
        }, 5000);
    }

    fallbackToBasicMode() {
        console.log('🛟 [UnifiedBootstrap] 降级到基础模式...');
        
        // 创建最基本的功能
        window.mdacBasicMode = {
            active: true,
            startTime: Date.now(),
            
            // 基本的消息发送功能
            sendToContent: async function(message) {
                const tabs = await chrome.tabs.query({ url: '*://imigresen-online.imi.gov.my/*' });
                if (tabs.length > 0) {
                    return chrome.tabs.sendMessage(tabs[0].id, message);
                }
                throw new Error('未找到MDAC标签页');
            },
            
            // 基本的数据存储
            saveData: function(data) {
                return chrome.storage.sync.set({ mdacBasicData: data });
            },
            
            loadData: function() {
                return chrome.storage.sync.get(['mdacBasicData']);
            }
        };

        // 创建基础UI
        const basicDiv = document.createElement('div');
        const basicContent = document.createElement('div');
        basicContent.style.padding = '20px';
        basicContent.style.textAlign = 'center';
        
        // 添加标题
        const title = document.createElement('h3');
        title.textContent = '⚠️ MDAC基础模式';
        basicContent.appendChild(title);
        
        // 添加描述
        const description = document.createElement('p');
        description.textContent = '系统运行在基础模式，功能受限';
        basicContent.appendChild(description);
        
        // 创建重新加载按钮
        const reloadButton = document.createElement('button');
        reloadButton.textContent = '重新加载';
        reloadButton.addEventListener('click', () => {
            location.reload();
        });
        basicContent.appendChild(reloadButton);
        
        // 创建打开MDAC网站按钮
        const openSiteButton = document.createElement('button');
        openSiteButton.textContent = '打开MDAC网站';
        openSiteButton.addEventListener('click', () => {
            chrome.tabs.create({url: 'https://imigresen-online.imi.gov.my'});
        });
        basicContent.appendChild(openSiteButton);
        
        basicDiv.appendChild(basicContent);
        document.body.appendChild(basicDiv);
        
        console.log('✅ [UnifiedBootstrap] 基础模式激活');
    }

    async handleBootstrapFailure(error) {
        console.error('🚨 [UnifiedBootstrap] 启动器失败:', error);
        
        // 记录错误到存储
        try {
            await chrome.storage.local.set({
                mdacBootstrapError: {
                    error: error.message,
                    stack: error.stack,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent
                }
            });
        } catch (storageError) {
            console.error('无法保存错误信息:', storageError);
        }

        // 尝试最后的回退
        this.fallbackToBasicMode();
    }

    async finalizeInitialization() {
        console.log('🎯 [UnifiedBootstrap] 完成初始化...');
        
        this.isInitialized = true;
        
        // 设置全局状态
        window.mdacUnifiedBootstrap = this;
        
        // 发送初始化完成事件
        if (window.mdacEventBus) {
            window.mdacEventBus.emit('unified-bootstrap-complete', {
                strategy: this.registry?.stateManager?.getState('primarySystem') || 'unknown',
                totalTime: performance.now() - this.startTime,
                registryStatus: this.registry?.getStats()
            });
        }

        // 通知背景脚本
        try {
            chrome.runtime.sendMessage({
                action: 'unified-bootstrap-complete',
                timestamp: Date.now()
            });
        } catch (error) {
            console.warn('无法通知背景脚本:', error);
        }
        
        console.log('✅ [UnifiedBootstrap] 初始化完全完成');
    }

    // 公共API
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            startTime: this.startTime,
            registry: this.registry?.getStats() || null,
            mode: this.registry?.stateManager?.getState('primarySystem') || 'unknown'
        };
    }

    async reload() {
        console.log('🔄 [UnifiedBootstrap] 重新加载...');
        
        // 重置状态
        this.isInitialized = false;
        this.initializationPromise = null;
        
        // 重新初始化
        await this.init();
    }
}

// 自动启动
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new UnifiedArchitectureBootstrap();
    });
} else {
    new UnifiedArchitectureBootstrap();
}

// 全局导出
window.UnifiedArchitectureBootstrap = UnifiedArchitectureBootstrap;
console.log('🚀 [UnifiedBootstrap] 统一架构启动器已加载');
